# Debug Route Mapping - Hướng dẫn khắc phục lỗi profile_management

## 🔍 **Vấn đề đã phát hiện:**

API trả về: `profile_management` (lowercase, underscore)  
Route định nghĩa: `/profile_management`  
Mapping cũ: `ProfileManagement` (PascalCase)  

→ **Không match** → Không hiển thị popup

## ✅ **Giải pháp đã áp dụng:**

### 1. **Sử dụng Route Name thay vì Screen Name**

```dart
// Logic mới trong AlertScreenWrapper:
// 1. Ưu tiên kiểm tra với route key (bỏ dấu /)
String routeKey = widget.routeName.replaceFirst('/', '');
alert = _featureBlockingService.getAlertForFeature(routeKey);

// 2. Nếu không có, kiểm tra với screen name
alert = _featureBlockingService.getAlertForFeature(widget.screenName);

// 3. Thử với feature mapping từ route
String featureName = RouteFeatureMapping.getFeatureForRoute(widget.routeName);
alert = _featureBlockingService.getAlertForFeature(featureName);
```

### 2. **Tạo RouteFeatureMapping mới**

```dart
class RouteFeatureMapping {
  static const Map<String, String> routeToFeature = {
    'profile_management': 'PROFILE',
    'create_order': 'ORDER_MANAGEMENT',
    'debt': 'FINANCIAL',
    // ... mapping đầy đủ từ Routes constants
  };
}
```

## 🧪 **Cách test:**

### 1. **Sử dụng Test Screen**

```dart
// Navigate đến test screen
Navigator.pushNamed(context, '/route_mapping_test');

// Hoặc thêm vào routes.dart:
route_mapping_test: (context) => RouteMappingTest(),
```

### 2. **Debug trong code**

```dart
// Thêm vào bất kỳ đâu để debug
RouteMappingDebugger.debugRouteMapping('/profile_management');

// Output sẽ hiển thị:
// Route: /profile_management
// Route key: profile_management  
// Mapped feature: PROFILE
// Alert by route key: Profile management alert
// Alert by feature: Profile management alert
```

### 3. **Test với dữ liệu thực**

```dart
// Tạo test data giống như API trả về
final testAlerts = [
  AlertModel(feature: 'profile_management', message: 'Test alert', isRequired: false),
];
await FeatureBlockingService.instance.updateAlerts(testAlerts);

// Navigate để test
Navigator.pushNamed(context, '/profile_management');
```

## 🔧 **Kiểm tra từng bước:**

### Bước 1: Kiểm tra API data
```dart
// In AlertChecker, thêm log:
debugPrint('API returned features: ${alerts.map((a) => a.feature).toList()}');
```

### Bước 2: Kiểm tra FeatureBlockingService
```dart
// Kiểm tra alerts đã lưu:
List<AlertModel> alerts = FeatureBlockingService.instance.alerts;
debugPrint('Stored alerts: ${alerts.map((a) => a.feature).toList()}');
```

### Bước 3: Kiểm tra AlertScreenWrapper
```dart
// Trong _checkAndShowAlert(), đã có debug logs:
debugPrint('AlertScreenWrapper: Checking route key: $routeKey');
debugPrint('AlertScreenWrapper: Found alert: ${alert?.message}');
```

## 📋 **Checklist khắc phục:**

- [x] ✅ Cập nhật logic kiểm tra route key trước
- [x] ✅ Tạo RouteFeatureMapping đầy đủ
- [x] ✅ Thêm debug logs chi tiết
- [x] ✅ Tạo test screen để verify
- [ ] 🔄 Test với dữ liệu thực từ API
- [ ] 🔄 Verify popup hiển thị đúng

## 🎯 **Kết quả mong đợi:**

Với dữ liệu: `[DETAIL_ORDER, DEBT, COMMISSION, INVOICE, CREATE_ORDER, profile_management]`

- ✅ `/profile_management` → Tìm thấy alert với key `profile_management`
- ✅ `/create_order` → Tìm thấy alert với key `CREATE_ORDER`  
- ✅ `/debt` → Tìm thấy alert với key `DEBT`
- ✅ Popup hiển thị khi navigate đến các màn hình này

## 🚀 **Test ngay:**

```dart
// 1. Navigate đến test screen
Navigator.pushNamed(context, '/route_mapping_test');

// 2. Chạy test với dữ liệu thực
// 3. Navigate đến /profile_management
// 4. Kiểm tra popup có hiển thị không

// 5. Nếu vẫn không hoạt động, check console logs:
// - "AlertScreenWrapper: Checking route key: profile_management"
// - "AlertScreenWrapper: Found alert: ..."
```

## 🔍 **Nếu vẫn không hoạt động:**

1. **Kiểm tra API response format:**
   ```dart
   debugPrint('Raw API response: ${jsonEncode(alerts)}');
   ```

2. **Kiểm tra AlertModel.fromJson:**
   ```dart
   // Đảm bảo feature field được parse đúng
   ```

3. **Kiểm tra route được wrap đúng:**
   ```dart
   // Trong routes.dart, đảm bảo:
   profile_management: _alertRoute('/profile_management', (context) => ..., 'ProfileManagement'),
   ```

4. **Force refresh cache:**
   ```dart
   await FeatureBlockingService.instance.clearCache();
   // Rồi gọi API lại
   ```

Với những thay đổi này, `profile_management` và tất cả routes khác sẽ hoạt động đúng với dữ liệu từ API! 🎉
