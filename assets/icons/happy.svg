<svg width="147" height="149" viewBox="0 0 147 149" fill="none" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="paint0_linear" x1="73.3766" y1="14.7517" x2="73.3766" y2="113.808"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#FFFF2E"/>
            <stop offset="1" stop-color="#FFAE00"/>
        </linearGradient>
        <radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
                        gradientTransform="translate(58.0061 69.0016) scale(13)">
            <stop stop-color="#FF89FF"/>
            <stop offset="1" stop-color="#FFD114" stop-opacity="0.1"/>
        </radialGradient>
        <radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
                        gradientTransform="translate(89.0061 69.0016) scale(13)">
            <stop stop-color="#FF89FF"/>
            <stop offset="1" stop-color="#FFD114" stop-opacity="0.1"/>
        </radialGradient>
        <linearGradient id="paint3_linear" x1="82.9995" y1="43.5" x2="83.4035" y2="64.6021"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#664800"/>
            <stop offset="1" stop-color="#FFCE00"/>
        </linearGradient>
        <linearGradient id="paint4_linear" x1="83.0701" y1="48.886" x2="83.4245" y2="62.6687"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1"/>
        </linearGradient>
        <linearGradient id="paint5_linear" x1="62.9702" y1="43.5" x2="63.3742" y2="64.6021"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#664800"/>
            <stop offset="1" stop-color="#FFCE00"/>
        </linearGradient>
        <linearGradient id="paint6_linear" x1="63.0287" y1="48.886" x2="63.383" y2="62.6687"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1"/>
        </linearGradient>
    </defs>
    <mask id="mask0" maskUnits="userSpaceOnUse" x="0" y="0" width="81" height="81">
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M40.5 81C62.8675 81 81 62.8675 81 40.5C81 18.1325 62.8675 0 40.5 0C18.1325 0 0 18.1325 0 40.5C0 62.8675 18.1325 81 40.5 81ZM40.5 73.5882C58.7741 73.5882 73.5882 58.7741 73.5882 40.5C73.5882 22.2259 58.7741 7.41177 40.5 7.41177C22.2259 7.41177 7.41178 22.2259 7.41178 40.5C7.41178 58.7741 22.2259 73.5882 40.5 73.5882ZM40.5 69.5916C56.5668 69.5916 69.5916 56.5668 69.5916 40.5C69.5916 24.4332 56.5668 11.4085 40.5 11.4085C24.4332 11.4085 11.4085 24.4332 11.4085 40.5C11.4085 56.5668 24.4332 69.5916 40.5 69.5916ZM40.4998 64.2676C53.6262 64.2676 64.2674 53.6265 64.2674 40.5C64.2674 27.3735 53.6262 16.7324 40.4998 16.7324C27.3733 16.7324 16.7322 27.3735 16.7322 40.5C16.7322 53.6265 27.3733 64.2676 40.4998 64.2676ZM59.9995 40.4998C59.9995 51.2692 51.2692 59.9995 40.4998 59.9995C29.7303 59.9995 21 51.2692 21 40.4998C21 29.7303 29.7303 21 40.4998 21C51.2692 21 59.9995 29.7303 59.9995 40.4998ZM56.4313 40.5C56.4313 49.2985 49.2986 56.4312 40.5001 56.4312C31.7016 56.4312 24.5689 49.2985 24.5689 40.5C24.5689 31.7015 31.7016 24.5688 40.5001 24.5688C49.2986 24.5688 56.4313 31.7015 56.4313 40.5Z"
              fill="#C4C4C4"/>
    </mask>
    <g mask="url(#mask0)">
        <rect opacity="0.1" width="40.6901" height="42.2113" fill="#EE0033"/>
    </g>
    <mask id="mask1" maskUnits="userSpaceOnUse" x="66" y="68" width="81" height="81">
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M106.5 68C84.1325 68 66 86.1325 66 108.5C66 130.868 84.1325 149 106.5 149C128.868 149 147 130.868 147 108.5C147 86.1325 128.868 68 106.5 68ZM106.5 75.4118C88.2259 75.4118 73.4118 90.2259 73.4118 108.5C73.4118 126.774 88.2259 141.588 106.5 141.588C124.774 141.588 139.588 126.774 139.588 108.5C139.588 90.2259 124.774 75.4118 106.5 75.4118ZM106.5 79.4084C90.4332 79.4084 77.4085 92.4332 77.4085 108.5C77.4084 124.567 90.4332 137.592 106.5 137.592C122.567 137.592 135.592 124.567 135.592 108.5C135.592 92.4332 122.567 79.4084 106.5 79.4084ZM106.5 84.7324C93.3738 84.7324 82.7326 95.3735 82.7326 108.5C82.7326 121.626 93.3738 132.268 106.5 132.268C119.627 132.268 130.268 121.626 130.268 108.5C130.268 95.3735 119.627 84.7324 106.5 84.7324ZM87.0005 108.5C87.0005 97.7308 95.7308 89.0005 106.5 89.0005C117.27 89.0005 126 97.7308 126 108.5C126 119.27 117.27 128 106.5 128C95.7308 128 87.0005 119.27 87.0005 108.5ZM90.5687 108.5C90.5687 99.7015 97.7014 92.5688 106.5 92.5688C115.298 92.5688 122.431 99.7015 122.431 108.5C122.431 117.299 115.298 124.431 106.5 124.431C97.7014 124.431 90.5687 117.299 90.5687 108.5Z"
              fill="#C4C4C4"/>
    </mask>
    <g mask="url(#mask1)">
        <rect opacity="0.1" x="147" y="149" width="40.6901" height="42.2113" transform="rotate(-180 147 149)"
              fill="#EE0033"/>
    </g>
    <mask id="mask2" maskUnits="userSpaceOnUse" x="66" y="0" width="81" height="81">
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M106.5 81C84.1325 81 66 62.8675 66 40.5C66 18.1325 84.1325 0 106.5 0C128.868 0 147 18.1325 147 40.5C147 62.8675 128.868 81 106.5 81ZM106.5 73.5882C88.2259 73.5882 73.4118 58.7741 73.4118 40.5C73.4118 22.2259 88.2259 7.41177 106.5 7.41177C124.774 7.41177 139.588 22.2259 139.588 40.5C139.588 58.7741 124.774 73.5882 106.5 73.5882ZM106.5 69.5916C90.4332 69.5916 77.4084 56.5668 77.4084 40.5C77.4084 24.4332 90.4332 11.4085 106.5 11.4085C122.567 11.4085 135.592 24.4332 135.592 40.5C135.592 56.5668 122.567 69.5916 106.5 69.5916ZM106.5 64.2676C93.3738 64.2676 82.7326 53.6265 82.7326 40.5C82.7326 27.3735 93.3738 16.7324 106.5 16.7324C119.627 16.7324 130.268 27.3735 130.268 40.5C130.268 53.6265 119.627 64.2676 106.5 64.2676ZM87.0005 40.4998C87.0005 51.2692 95.7308 59.9995 106.5 59.9995C117.27 59.9995 126 51.2692 126 40.4998C126 29.7303 117.27 21 106.5 21C95.7308 21 87.0005 29.7303 87.0005 40.4998ZM90.5687 40.5C90.5687 49.2985 97.7014 56.4312 106.5 56.4312C115.298 56.4312 122.431 49.2985 122.431 40.5C122.431 31.7015 115.298 24.5688 106.5 24.5688C97.7014 24.5688 90.5687 31.7015 90.5687 40.5Z"
              fill="#C4C4C4"/>
    </mask>
    <g mask="url(#mask2)">
        <rect opacity="0.1" width="40.6901" height="42.2113" transform="matrix(-1 0 0 1 147 0)" fill="#EE0033"/>
    </g>
    <mask id="mask3" maskUnits="userSpaceOnUse" x="0" y="68" width="81" height="81">
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M40.5 68C62.8675 68 81 86.1325 81 108.5C81 130.868 62.8675 149 40.5 149C18.1325 149 1.58519e-06 130.868 3.54062e-06 108.5C5.49605e-06 86.1325 18.1325 68 40.5 68ZM40.5 75.4118C58.7741 75.4118 73.5883 90.2259 73.5883 108.5C73.5883 126.774 58.7741 141.588 40.5 141.588C22.2259 141.588 7.41178 126.774 7.41178 108.5C7.41178 90.2259 22.2259 75.4118 40.5 75.4118ZM40.5 79.4085C56.5668 79.4085 69.5916 92.4332 69.5916 108.5C69.5916 124.567 56.5668 137.592 40.5 137.592C24.4332 137.592 11.4085 124.567 11.4085 108.5C11.4085 92.4332 24.4332 79.4084 40.5 79.4085ZM40.4998 84.7324C53.6262 84.7324 64.2674 95.3735 64.2674 108.5C64.2674 121.626 53.6262 132.268 40.4998 132.268C27.3733 132.268 16.7322 121.626 16.7322 108.5C16.7322 95.3735 27.3733 84.7324 40.4998 84.7324ZM59.9995 108.5C59.9995 97.7308 51.2692 89.0005 40.4998 89.0005C29.7303 89.0005 21 97.7308 21 108.5C21 119.27 29.7303 128 40.4998 128C51.2692 128 59.9995 119.27 59.9995 108.5ZM56.4313 108.5C56.4313 99.7015 49.2986 92.5688 40.5001 92.5688C31.7016 92.5688 24.5689 99.7015 24.5689 108.5C24.5689 117.299 31.7016 124.431 40.5001 124.431C49.2986 124.431 56.4313 117.299 56.4313 108.5Z"
              fill="#C4C4C4"/>
    </mask>
    <g mask="url(#mask3)">
        <rect opacity="0.1" width="40.6901" height="42.2113" transform="matrix(1 8.74228e-08 8.74228e-08 -1 0 149)"
              fill="#EE0033"/>
    </g>
    <path d="M76.4704 16.9193L89.9792 44.3061C90.4713 45.315 91.4556 46.0285 92.5875 46.2008L122.804 50.5807C125.634 50.999 126.766 54.4685 124.723 56.4616L102.848 77.7707C102.036 78.5581 101.667 79.7146 101.864 80.8219L107.031 110.915C107.523 113.745 104.546 115.886 102.012 114.557L74.994 100.384C73.9851 99.8671 72.7794 99.8671 71.7705 100.384L44.7528 114.582C42.2184 115.91 39.2656 113.77 39.7331 110.94L44.9005 80.8465C45.0973 79.7146 44.7282 78.5827 43.9162 77.7953L22.0412 56.4862C19.9989 54.4931 21.1308 50.999 23.9605 50.6053L54.177 46.2254C55.3089 46.0532 56.2686 45.3642 56.7853 44.3307L70.2942 16.9193C71.5491 14.3602 75.2154 14.3602 76.4704 16.9193Z"
          fill="url(#paint0_linear)"/>
    <path d="M58 82C65.1797 82 71 76.1797 71 69C71 61.8203 65.1797 56 58 56C50.8203 56 45 61.8203 45 69C45 76.1797 50.8203 82 58 82Z"
          fill="url(#paint1_radial)"/>
    <path d="M89 82C96.1797 82 102 76.1797 102 69C102 61.8203 96.1797 56 89 56C81.8203 56 76 61.8203 76 69C76 76.1797 81.8203 82 89 82Z"
          fill="url(#paint2_radial)"/>
    <path d="M58.951 76.8357C58.9017 78.2382 59.3447 79.7392 60.2551 81.191C62.1744 84.2421 66.3328 87.0473 73.0012 87.8347C77.0858 88.3268 80.5061 87.0965 83.1144 85.1526C87.1006 82.1752 89.1429 77.6231 88.8476 75.1624C88.823 74.9656 88.7738 74.7441 88.7246 74.5719C87.7896 70.9301 82.6468 69.7736 79.7187 71.3977C76.6921 73.0709 74.4037 73.1447 73.1242 72.6526C62.1744 68.5433 59.6892 71.4469 59.1232 75.064C59.0248 75.6299 58.9756 76.2451 58.951 76.8357Z"
          fill="#AF1A1C"/>
    <path d="M59.0986 75.064C74.6006 80.9695 84.074 77.5 88.7 74.5719C87.765 70.9301 82.6223 69.7736 79.6941 71.3977C76.6675 73.0709 74.3792 73.1447 73.0996 72.6526C62.1498 68.5433 59.6892 71.4469 59.0986 75.064Z"
          fill="white"/>
    <path d="M60.2549 81.2156C62.1742 84.2667 66.3326 87.0719 73.0009 87.8593C77.0856 88.3514 80.5059 87.1211 83.1141 85.1772C80.5059 82.4951 75.8307 78.878 71.3523 81.5847C71.3523 81.5601 66.308 77.5246 60.2549 81.2156Z"
          fill="#FB2A49"/>
    <path d="M89.5603 59.4882C89.6834 63.8189 87.0013 67.4114 83.6056 67.4852C80.21 67.5837 77.3556 64.1388 77.2326 59.8081C77.1096 55.4774 79.7916 51.8848 83.1873 51.811C86.6076 51.7372 89.4619 55.1821 89.5603 59.4882Z"
          fill="url(#paint3_linear)"/>
    <path d="M83.6003 67.5097C86.3039 67.44 88.4234 64.5861 88.3343 61.1354C88.2453 57.6846 85.9816 54.9438 83.278 55.0135C80.5745 55.0832 78.455 57.9371 78.544 61.3879C78.633 64.8386 80.8968 67.5795 83.6003 67.5097Z"
          fill="black"/>
    <path d="M84.6634 58.5039C85.9675 58.4793 87.1978 58.9715 88.2067 59.8081C87.6653 57.003 85.6476 54.936 83.2854 55.0099C80.5788 55.0837 78.438 57.938 78.5365 61.3829C78.5611 62.2687 78.7333 63.0807 79.004 63.8435C79.7668 60.7923 81.9813 58.5778 84.6634 58.5039Z"
          fill="url(#paint4_linear)"/>
    <path d="M82.0308 59.3405C81.8585 60.1772 81.2926 60.7431 80.8004 60.6447C80.3083 60.5462 80.0622 59.7835 80.2345 58.9468C80.4067 58.1102 80.9727 57.5443 81.4648 57.6427C81.957 57.7657 82.2276 58.5285 82.0308 59.3405Z"
          fill="white"/>
    <path d="M81.8834 62.1211C81.9327 62.7116 81.6866 63.2283 81.3175 63.2529C80.973 63.2776 80.6285 62.81 80.6039 62.2195C80.5547 61.6289 80.8008 61.1122 81.1699 61.0876C81.5143 61.063 81.8342 61.5305 81.8834 62.1211Z"
          fill="white"/>
    <path d="M78.684 59.6604C78.684 59.6604 78.6348 58.7746 79.1269 57.5197C79.373 56.9045 79.7667 56.1663 80.4064 55.5512C80.7263 55.2559 81.12 54.936 81.5629 54.7638C82.0058 54.5669 82.498 54.4439 82.9901 54.4193H83.1869C83.2362 54.4193 83.2115 54.4193 83.3346 54.4193H83.4084L83.7283 54.4439C83.9251 54.4439 84.1958 54.5177 84.4419 54.5669C84.934 54.6653 85.3523 54.9114 85.746 55.1329C86.1397 55.3789 86.435 55.6742 86.7056 55.9695C86.9763 56.2648 87.1732 56.5846 87.3454 56.8799C88.0098 58.0856 88.0098 58.9961 88.0098 58.9961C88.0098 58.9961 87.5669 58.2087 86.7549 57.3228C86.558 57.1014 86.3119 56.8799 86.0413 56.6585C85.7706 56.4616 85.4753 56.2648 85.1801 56.1171C84.8848 55.9695 84.5157 55.8464 84.1958 55.7726C83.9497 55.6988 83.3346 55.6496 83.1131 55.6988C82.7686 55.6988 82.3995 55.7726 82.0797 55.9203C81.7598 56.0433 81.4399 56.2155 81.1692 56.437C80.6033 56.8553 80.1604 57.3966 79.7913 57.8888C79.0777 58.8484 78.684 59.6604 78.684 59.6604Z"
          fill="black"/>
    <path d="M69.5311 59.4882C69.6541 63.8189 66.972 67.4114 63.5763 67.4852C60.1807 67.5837 57.3263 64.1388 57.2033 59.8081C57.0803 55.4774 59.7623 51.8848 63.158 51.811C66.5537 51.7372 69.408 55.1821 69.5311 59.4882Z"
          fill="url(#paint5_linear)"/>
    <path d="M63.5708 67.5095C66.2744 67.4397 68.3939 64.5859 68.3049 61.1351C68.216 57.6844 65.9522 54.9435 63.2487 55.0132C60.5451 55.0829 58.4256 57.9368 58.5146 61.3875C58.6036 64.8383 60.8673 67.5792 63.5708 67.5095Z"
          fill="black"/>
    <path d="M64.6097 58.5039C65.9139 58.4793 67.1442 58.9715 68.153 59.8081C67.6117 57.003 65.594 54.936 63.2318 55.0099C60.5251 55.0837 58.3843 57.938 58.4828 61.3829C58.5074 62.2687 58.6796 63.0807 58.9503 63.8435C59.7377 60.7923 61.9523 58.5778 64.6097 58.5039Z"
          fill="url(#paint6_linear)"/>
    <path d="M62.0014 59.3405C61.8292 60.1772 61.2632 60.7431 60.7711 60.6447C60.279 60.5462 60.0329 59.7835 60.2052 58.9468C60.3774 58.1102 60.9434 57.5443 61.4355 57.6427C61.9276 57.7657 62.1737 58.5285 62.0014 59.3405Z"
          fill="white"/>
    <path d="M61.8287 62.1211C61.8779 62.7116 61.6319 63.2283 61.2628 63.2529C60.9183 63.2776 60.5738 62.81 60.5492 62.2195C60.5 61.6289 60.7461 61.1122 61.1152 61.0876C61.4596 61.063 61.8041 61.5305 61.8287 62.1211Z"
          fill="white"/>
    <path d="M58.6303 59.6604C58.6303 59.6604 58.5811 58.7746 59.0732 57.5197C59.3192 56.9045 59.7129 56.1663 60.3527 55.5512C60.6726 55.2559 61.0663 54.936 61.5092 54.7638C61.9521 54.5669 62.4442 54.4439 62.9364 54.4193H63.1332C63.1824 54.4193 63.1578 54.4193 63.2808 54.4193H63.3547L63.6746 54.4439C63.8714 54.4439 64.1421 54.5177 64.3881 54.5669C64.8803 54.6653 65.2986 54.9114 65.6923 55.1329C66.086 55.3789 66.3812 55.6742 66.6519 55.9695C66.9226 56.2648 67.1194 56.5846 67.2917 56.8799C67.956 58.0856 67.956 58.9961 67.956 58.9961C67.956 58.9961 67.5131 58.2087 66.7011 57.3228C66.5043 57.1014 66.2582 56.8799 65.9875 56.6585C65.7169 56.4616 65.4216 56.2648 65.1263 56.1171C64.831 55.9695 64.4619 55.8464 64.1421 55.7726C63.896 55.6988 63.2809 55.6496 63.0594 55.6988C62.7149 55.6988 62.3458 55.7726 62.0259 55.9203C61.7061 56.0433 61.3862 56.2155 61.1155 56.437C60.5496 56.8553 60.1066 57.3966 59.7376 57.8888C59.024 58.8484 58.6303 59.6604 58.6303 59.6604Z"
          fill="black"/>
    <path d="M58.9512 48.0462C59.3203 47.3327 59.7878 46.6929 60.3291 46.0777C60.8705 45.4626 61.461 44.9212 62.1254 44.4537C62.7897 43.9862 63.5525 43.5433 64.3892 43.4203C65.2258 43.2726 66.087 43.3464 66.8498 43.6909L66.8252 43.7648C66.4069 43.814 66.0378 43.8386 65.6441 43.8878C65.275 43.9616 64.9059 44.0108 64.5368 44.1338C63.8232 44.3553 63.1096 44.5768 62.4453 44.9459C61.7809 45.3149 61.1657 45.8071 60.5998 46.3238C60.0339 46.8405 59.4925 47.4557 59.025 48.0709L58.9512 48.0462Z"
          fill="black"/>
    <path d="M88.4789 47.7018C87.9867 47.0866 87.4208 46.5207 86.8056 46.0285C86.2151 45.5118 85.5507 45.0935 84.8863 44.749C84.1974 44.4045 83.4592 44.2323 82.7456 44.06C82.3765 43.9616 82.0074 43.9124 81.6383 43.8878C81.2446 43.8386 80.8755 43.8386 80.4572 43.814L80.4326 43.7401C81.1708 43.3711 82.032 43.2234 82.8686 43.3218C83.7053 43.3957 84.4927 43.814 85.1816 44.2323C85.8706 44.6752 86.5104 45.1919 87.0763 45.7579C87.6423 46.3484 88.1344 46.9636 88.5527 47.6526L88.4789 47.7018Z"
          fill="black"/>

</svg>
