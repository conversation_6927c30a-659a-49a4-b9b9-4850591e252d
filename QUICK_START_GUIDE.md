# Quick Start Guide - Feature Blocking System

## 🚀 Hướng dẫn nhanh để test hệ thống

### 1. Thêm route test vào routes.dart

```dart
// Thêm vào Routes class constants
static const String feature_blocking_example = '/feature_blocking_example';

// Thêm vào routes map
feature_blocking_example: (BuildContext context) => FeatureBlockingExample(),
```

### 2. Test cơ bản

#### A. Test với dữ liệu mẫu từ API

```dart
// Trong AlertChecker, API sẽ trả về:
[
  {
    "feature": "HOME",
    "message": "Trang chủ đang bảo trì", 
    "isRequired": true  // Chặn chức năng
  },
  {
    "feature": "TASK_LIST",
    "message": "Danh sách công việc đang cập nhật",
    "isRequired": true  // Chặn chức năng
  },
  {
    "feature": "COMMUNICATION", 
    "message": "<PERSON><PERSON> cập nhật mới",
    "isRequired": false  // Chỉ thông báo, không chặn
  }
]
```

#### B. Test manual

```dart
// Trong bất kỳ widget nào
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/middleware/route_guard_middleware.dart';

// Kiểm tra chức năng bị chặn
bool isHomeBlocked = FeatureBlockingService.instance.isFeatureBlocked('HOME');
print('Home blocked: $isHomeBlocked');

// Test navigation
bool canNavigate = RouteGuardMiddleware.instance.canAccessRoute('/home');
if (canNavigate) {
  Navigator.pushNamed(context, '/home');
} else {
  print('Navigation blocked!');
}
```

### 3. Test với Extension Methods

```dart
// Sử dụng extension để navigation dễ dàng
await context.guardedPushNamed('/home');
await context.guardedPushNamed('/task_list');
await context.guardedPushNamed('/profile');
```

### 4. Simulate API Response

Để test mà không cần API thật, bạn có thể tạo mock data:

```dart
// Trong initState của widget
Future<void> _simulateAPIResponse() async {
  final mockAlerts = [
    AlertModel(
      feature: 'HOME',
      message: 'Trang chủ đang bảo trì',
      isRequired: true,
    ),
    AlertModel(
      feature: 'ALL', // Chặn tất cả
      message: 'Ứng dụng đang bảo trì',
      isRequired: true,
    ),
  ];
  
  await FeatureBlockingService.instance.updateBlockedFeatures(mockAlerts);
}
```

### 5. Debug Information

```dart
// Xem thông tin debug
Map<String, dynamic> debugInfo = FeatureBlockingService.instance.getBlockingInfo();
print('Blocked features: ${debugInfo['blockedFeatures']}');
print('Cache valid: ${debugInfo['cacheValid']}');
print('Last update: ${debugInfo['lastUpdate']}');

// Xem tất cả routes bị chặn
List<String> blockedFeatures = FeatureBlockingService.instance.blockedFeatures;
for (String feature in blockedFeatures) {
  List<String> blockedRoutes = BlockableFeatures.getBlockedRoutes(feature);
  print('Feature $feature blocks routes: $blockedRoutes');
}
```

### 6. Test Scenarios

#### Scenario 1: Chặn trang chủ
```dart
final alerts = [
  AlertModel(feature: 'HOME', message: 'Trang chủ bảo trì', isRequired: true)
];
await FeatureBlockingService.instance.updateBlockedFeatures(alerts);
// Thử navigate đến /home -> sẽ hiện dialog
```

#### Scenario 2: Chặn tất cả
```dart
final alerts = [
  AlertModel(feature: 'ALL', message: 'App bảo trì', isRequired: true)
];
await FeatureBlockingService.instance.updateBlockedFeatures(alerts);
// Tất cả routes sẽ bị chặn
```

#### Scenario 3: Chỉ thông báo
```dart
final alerts = [
  AlertModel(feature: 'HOME', message: 'Có cập nhật mới', isRequired: false)
];
await FeatureBlockingService.instance.updateBlockedFeatures(alerts);
// Sẽ hiện dialog thông báo nhưng không chặn navigation
```

### 7. Integration với existing code

#### Trong home_page.dart:
```dart
// AlertChecker đã được tích hợp sẵn
AlertChecker(
  features: 'ALL', // Sẽ tự động cập nhật blocked features
),
```

#### Trong navigation code:
```dart
// Thay vì:
Navigator.pushNamed(context, '/home');

// Sử dụng:
await context.guardedPushNamed('/home');
```

### 8. Troubleshooting

#### Lỗi thường gặp:

1. **Dialog không hiện**: Kiểm tra context có hợp lệ không
2. **Route không bị chặn**: Kiểm tra mapping trong `BlockableFeatures.featureToRoutes`
3. **Cache không hoạt động**: Kiểm tra SharedPreferences permissions

#### Debug commands:
```dart
// Clear cache
await FeatureBlockingService.instance.clearCache();

// Force refresh
await FeatureBlockingService.instance.loadFromCache();

// Check route mapping
bool isBlocked = BlockableFeatures.isRouteBlocked('/home', ['HOME']);
```

### 9. Production Checklist

- [ ] API `getAlerts` trả về đúng format
- [ ] `isRequired: true` cho features cần chặn
- [ ] `isRequired: false` cho notifications
- [ ] Test tất cả routes quan trọng
- [ ] Test với network offline
- [ ] Test cache expiration
- [ ] Test dialog UX

### 10. Monitoring

```dart
// Log để monitor
debugPrint('Feature blocking status: ${FeatureBlockingService.instance.getBlockingInfo()}');

// Track blocked navigation attempts
RouteGuardMiddleware.instance.getDebugInfo();
```

## 🎯 Ready to use!

Hệ thống đã sẵn sàng hoạt động với Flutter 1.22.6. Chỉ cần API `getAlerts` trả về đúng format là sẽ tự động chặn các chức năng theo cấu hình!
