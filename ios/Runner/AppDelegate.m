#include "AppDelegate.h"
#include "GeneratedPluginRegistrant.h"
#import <MyccSDK/MyccSDK.h>

NSString *globalUsername;

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    
    int flutter_native_splash = 1;
    UIApplication.sharedApplication.statusBarHidden = false;
    
    FlutterViewController *controller = (FlutterViewController *)self.window.rootViewController;
    FlutterMethodChannel *channel = [FlutterMethodChannel methodChannelWithName:@"com.viettel.vts.channel"
                                                                binaryMessenger:controller.binaryMessenger];
    SDKCallChatConfig *config = [SDKCallChatConfig instance];
    [config setConfigWithBaseUrl:@[@"https://mobilecall.viettelpost.vn/svc-pivot"]
                           accId:@"1fcb8551ccd9d37a9724d37e28219080"
                       sipDomain:@"vcc.mycc.vn"
                        sipProxy:@"mobilecall.viettelpost.vn:15060"
                      hashingKey:@"Viettel@123"
                        userName:globalUsername
                        isUseTCP:NO];
    
    UIWindow *window = nil;
    if (@available(iOS 13.0, *)) {
        window = UIApplication.sharedApplication.connectedScenes.allObjects.firstObject;
        window = [window isKindOfClass:[UIWindowScene class]] ? ((UIWindowScene *)window).windows.firstObject : nil;
    } else {
        window = UIApplication.sharedApplication.keyWindow;
    }
    [SDKCallChatManager instance].delegate = self;
    [[SDKCallChatManager instance] attachViewToWindow:window];
    
    [channel setMethodCallHandler:^(FlutterMethodCall *call, FlutterResult result) {
        if ([call.method isEqualToString:@"startVideoCall"]) {
            [self startCallWithCall:call result:result];
        }
        else if ([call.method isEqualToString:@"registerForPush"]) {
            [self registerForPush: call];
        }
        else {
            result(FlutterMethodNotImplemented);
        }
    }];
    
    [GeneratedPluginRegistrant registerWithRegistry:self];
    return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

- (void)registerForPush:(FlutterMethodCall *)call {
    NSDictionary *args = (NSDictionary *)call.arguments;
    globalUsername = args[@"username"];
    PKPushRegistry *pushRegistry = [[PKPushRegistry alloc] initWithQueue:dispatch_get_main_queue()];
    pushRegistry.delegate = self;
    pushRegistry.desiredPushTypes = [NSSet setWithObject:PKPushTypeVoIP];
}

- (void)startCallWithCall:(FlutterMethodCall *)call result:(FlutterResult)result {
    FlutterViewController *controller = (FlutterViewController *)self.window.rootViewController;
    
    NSDictionary *args = (NSDictionary *)call.arguments;
    NSString *username = args[@"username"];
    NSString *calleeNumber = args[@"calleeNumber"];
    
    NSString *displayName = args[@"displayName"];
    NSString *orderCode = args[@"orderCode"];
    NSString *sender = args[@"sender"];
    NSString *orderDetail = args[@"orderDetail"];
    NSString *cod = args[@"cod"];
    
    SDKCallChatConfig *config = [SDKCallChatConfig instance];
    [config setConfigWithBaseUrl:@[@"https://mobilecall.viettelpost.vn/svc-pivot"]
                           accId:@"1fcb8551ccd9d37a9724d37e28219080"
                       sipDomain:@"vcc.mycc.vn"
                        sipProxy:@"mobilecall.viettelpost.vn:15060"
                      hashingKey:@"Viettel@123"
                        userName:username
                        isUseTCP:NO];
    
    UIWindow *window = nil;
    if (@available(iOS 13.0, *)) {
        window = UIApplication.sharedApplication.connectedScenes.allObjects.firstObject;
        window = [window isKindOfClass:[UIWindowScene class]] ? ((UIWindowScene *)window).windows.firstObject : nil;
    } else {
        window = UIApplication.sharedApplication.keyWindow;
    }
    [SDKCallChatManager instance].delegate = self;
    [[SDKCallChatManager instance] attachViewToWindow:window];
    [[SDKCallChatManager instance] showCallToPhoneNumber:calleeNumber
                                              callPrefix:@""
                                              toUserName:username
                                          isShowCallInfo:YES
                                                  parent:controller];
    [[SDKCallChatManager instance] displayOrderInfosWithDisplayName:displayName
                                                          orderCode:orderCode
                                                             sender:sender
                                                        orderDetail:orderDetail
                                                                cod:cod];
    
    self.myFlutterResult = result;
}

- (NSDictionary *)convertCallStatusToDictionary:(MyccCallState *)callStatus {NSMutableDictionary *callStatusDictionary = [NSMutableDictionary dictionary];
    NSNumber *status = [NSNumber numberWithBool:callStatus.status];
    [callStatusDictionary setObject:status forKey:@"status"];
    
    NSNumber *duration = [NSNumber numberWithInteger:callStatus.duration];
    [callStatusDictionary setObject:duration forKey:@"duration"];
    
    NSNumber *ringingTime = [NSNumber numberWithInteger:callStatus.ringingTime];
    [callStatusDictionary setObject:ringingTime forKey:@"ringingTime"];
    
    NSNumber *startTime = [NSNumber numberWithInteger:callStatus.startTime];
    [callStatusDictionary setObject:startTime forKey:@"startTime"];
    
    NSNumber *endTime = [NSNumber numberWithInteger:callStatus.endTime];
    [callStatusDictionary setObject:endTime forKey:@"endTime"];
    
    NSNumber *errorType = [NSNumber numberWithInteger:callStatus.errorType];
    [callStatusDictionary setObject:errorType forKey:@"errorType"];
    
    NSString *callerNumber = (callStatus.callerNumber != nil) ? [NSString stringWithFormat:@"%@", callStatus.callerNumber] : @"";
    [callStatusDictionary setObject:callerNumber forKey:@"callerNumber"];
    
    NSString *callID = (callStatus.callID != nil) ? [NSString stringWithFormat:@"%@", callStatus.callID] : @"";
    [callStatusDictionary setObject:callID forKey:@"callID"];
    
    return [callStatusDictionary copy];
}

- (void)onMyccSDKClosedWithCallState:(MyccCallState *)callState {
    NSError *error;
    NSDictionary *callStatusDictionary = [self convertCallStatusToDictionary:callState];
    
    if (callStatusDictionary) {
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:callStatusDictionary
                                                           options:NSJSONWritingPrettyPrinted
                                                             error:&error];
        if (!error) {
            NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
            if (self.myFlutterResult) {
                self.myFlutterResult(jsonString);
                NSLog(@"Error converting MyccCallState to JSON: %@", jsonString);
                self.myFlutterResult = nil;
            }
            [self saveWithCallState:callState];
        } else {
            NSLog(@"Error converting CallStatus to JSON: %@", error.localizedDescription);
        }
    }
}

- (void)onCallConnectedWithCallState:(MyccCallState *)callState {
    NSLog(@"onCallConnectedWithCallState: Video call started.");
}

- (void)pushRegistry:(PKPushRegistry *)registry didUpdatePushCredentials:(PKPushCredentials *)pushCredentials forType:(PKPushType)type {
    NSData *token = pushCredentials.token;
    NSString *tokenString = [self stringWithDeviceToken:token];
    NSLog(@"%@ nampv", globalUsername);
    [[SDKCallChatManager instance] setDeviceTokenWithToken:tokenString];
    [[SDKCallChatConfig instance] registerDeviceTokenWithDeviceToken:tokenString userName:globalUsername];
}

- (void)pushRegistry:(PKPushRegistry *)registry didReceiveIncomingPushWithPayload:(PKPushPayload *)payload forType:(PKPushType)type withCompletionHandler:(void (^)(void))completionHandler {
    NSDictionary *aps = payload.dictionaryPayload[@"aps"];
    
    if (aps) {
        NSDictionary *alert = aps[@"alert"];
        if (alert) {
            NSString *bodyString = alert[@"body"] ?: @"";
            NSMutableDictionary *dicResponse = [NSMutableDictionary dictionary];
            NSData *data = [bodyString dataUsingEncoding:NSUTF8StringEncoding];
            if (data) {
                NSError *error = nil;
                dicResponse = [NSJSONSerialization JSONObjectWithData:data options:0 error:&error];
                if (error) {
                    NSLog(@"%@", error.localizedDescription);
                }
            }
            NSString *stringType = dicResponse[@"type"];
            NSString *callerNumber = dicResponse[@"callerNumber"];
            if ([stringType.lowercaseString isEqualToString:@"voicecall"] && type == PKPushTypeVoIP) {
                [self fetchdata:callerNumber withPayload:payload];
                [[SDKCallChatManager instance] handlePushkitWithPayload:payload];
            }
        }
    }
}

- (NSString *)stringWithDeviceToken:(NSData *)deviceToken {
    const char *data = [deviceToken bytes];
    NSMutableString *tokenString = [NSMutableString string];
    
    for (int i = 0; i < [deviceToken length]; i++) {
        [tokenString appendFormat:@"%02.2hhx", data[i]];
    }
    
    return [tokenString copy];
}

- (void)fetchdata:(NSString *)phoneNumber withPayload:(PKPushPayload *)payload {
    NSURLSessionConfiguration *defaultSessionConfiguration = [NSURLSessionConfiguration defaultSessionConfiguration];
    NSURLSession *defaultSession = [NSURLSession sessionWithConfiguration:defaultSessionConfiguration];
    
    NSURL *url = [NSURL URLWithString:@"https://hsapi-app.congtrinhviettel.com.vn/app/getInfoOrderCus"];
    NSMutableURLRequest *urlRequest = [NSMutableURLRequest requestWithURL:url];
    
    NSDictionary *requestData = @{@"phoneNumber": phoneNumber};
    
    NSData *postData = [NSJSONSerialization dataWithJSONObject:requestData options:0 error:nil];
    
    NSString *jsonString = [[NSString alloc] initWithData:postData encoding:NSUTF8StringEncoding];
    
    [urlRequest setHTTPMethod:@"POST"];
    [urlRequest setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    [urlRequest setHTTPBody:[jsonString dataUsingEncoding:NSUTF8StringEncoding]];
    
    NSURLSessionDataTask *dataTask = [defaultSession dataTaskWithRequest:urlRequest completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        NSDictionary *results = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:&error];
        
        if (error) {
            NSLog(@"Error parsing JSON: %@", error.localizedDescription);
        } else {
            NSDictionary *dataObject = results[@"data"];
            NSString *code = dataObject[@"code"];
//            NSString *idValue = dataObject[@"id"];
            self.idStep = dataObject[@"id"];
            NSString *contactName = dataObject[@"contactName"];
            NSString *address = dataObject[@"address"];
            
            [[SDKCallChatManager instance] handlePushkitWithPayload:payload];
            
            dispatch_async(dispatch_get_main_queue(), ^{
                [[SDKCallChatManager instance] displayOrderInfosWithDisplayName:contactName
                                                                      orderCode:code
                                                                         sender:contactName
                                                                    orderDetail:address
                                                                            cod:@""];
            });
        }
    }];
    
    [dataTask resume];
}


- (void)saveWithCallState:(MyccCallState *)callState {

    NSURL *apiUrl = [NSURL URLWithString:@"https://hsapi-app.congtrinhviettel.com.vn/app/updateMobileCall"];
    
    BOOL status = callState.status;
    NSInteger errorType = callState.errorType;
    NSInteger duration = callState.duration;
    NSString *callerNumber = callState.callerNumber ?: @"";
    NSString *callID = callState.callID;
    
    NSMutableDictionary *orderData = [@{
        @"referenceId": self.idStep ?: [NSNull null],
        @"status": @(status),
        @"errorType": @(errorType),
        @"duration": @(duration),
        @"callerNumber": callerNumber,
        @"callID": callID
    } mutableCopy];
    
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd'T'HH:mm:ss.SSS"];
    
    if (callState.ringingTime != 0) {
        NSDate *ringingDate = [NSDate dateWithTimeIntervalSince1970:callState.ringingTime];
        orderData[@"ringingTime"] = [dateFormatter stringFromDate:ringingDate];
    }
    if (callState.startTime != 0) {
        NSDate *startDate = [NSDate dateWithTimeIntervalSince1970:callState.startTime];
        orderData[@"startTime"] = [dateFormatter stringFromDate:startDate];
    }
    if (callState.endTime != 0) {
        NSDate *endDate = [NSDate dateWithTimeIntervalSince1970:callState.endTime];
        orderData[@"endTime"] = [dateFormatter stringFromDate:endDate];
    }
    
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:orderData options:0 error:&error];
    
    if (error) {
        NSLog(@"Error serializing JSON: %@", error.localizedDescription);
        return;
    }
    
    if (jsonData) {
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        NSLog(@"URL: %@", apiUrl);
        NSLog(@"JSON Data: %@", jsonString);
    }
    
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:apiUrl];
    [request setHTTPMethod:@"POST"];
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    [request setHTTPBody:jsonData];
    
    NSURLSession *session = [NSURLSession sharedSession];
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error.localizedDescription);
        } else if (data) {
            NSError *jsonError;
            id json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
            if (jsonError) {
                NSLog(@"Error parsing JSON: %@", jsonError.localizedDescription);
            } else {
                NSLog(@"Response JSON: %@", json);
            }
        }
        self.idStep = nil;
    }];
    
    [task resume];
}


@end
