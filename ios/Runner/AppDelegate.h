#import <Flutter/Flutter.h>
#import <UIKit/UIKit.h>
#import <MyccSDK/MyccSDK.h>
#import <PushKit/PushKit.h>

@interface AppDelegate : FlutterAppDelegate<PKPushRegistryDelegate, SDKCallChatManagerDelegate>

//@property (nonatomic, strong) FlutterMethodChannel *channel;
//
//- (instancetype)initWithChannel:(FlutterMethodChannel *)channel;

@property (nonatomic, strong) FlutterResult myFlutterResult;
@property (nonatomic, strong) NSString *idStep;

@end
