<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Home Services Partner</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Home Services Partner</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>app-worker.vcchomes.vn</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>vccworker</string>
				<string>com.googleusercontent.apps.509570791071-o5ptr0f9g2m2rnis9tp3in3jnaujm2da</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>itms-beta</string>
		<string>itms</string>
		<string>sms</string>
        <string>tel</string>
        <string>https</string>
        <string>*/*</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
        <key>NSAllowsArbitraryLoadsInWebContent</key>
        <true/>
        <key>NSAllowsLocalNetworking</key>
        <true/>
        <key>FirebaseDynamicLinksCustomDomains</key>
        <array>
            <string>https://homeservicesuser.page.link</string>
        </array>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>$(PRODUCT_NAME) cần quyền truy cập vào ứng dụng đa phương tiện để thêm tệp vào hồ sơ người dùng và đơn hàng.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>$(PRODUCT_NAME) need calendars access for create events feature &amp;amp;amp; better user experience</string>
	<key>NSCameraUsageDescription</key>
	<string>Ứng dụng này cần truy cập camera để cập nhật hồ sơ người dùng và đơn hàng</string>
	<key>NSContactsUsageDescription</key>
	<string>$(PRODUCT_NAME) need contacts access for finding product &amp;amp;amp; chatting between users</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) cần truy cập vị trí của bạn để hiển thị trên bản đồ và vị trí của đơn hàng.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Ứng dụng này cần truy cập vị trí của bạn để hiển thị trên bản đồ và vị trí của đơn hàng.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Ứng dụng này cần truy cập vị trí của bạn để hiển thị trên bản đồ và vị trí của đơn hàng.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>$(PRODUCT_NAME) need microphone access for uploading videos</string>
	<key>NSMotionUsageDescription</key>
	<string>$(PRODUCT_NAME) motion use for better user experience</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Ứng dụng này cần truy cập thư viện ảnh để cập nhật hồ sơ người dùng và đơn hàng</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>$(PRODUCT_NAME) speech use for better user experience</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>voip</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>FirebaseDynamicLinksCustomDomains</key>
    <array>
      <string>https://homeservicesuser.page.link</string>
    </array>
</dict>
</plist>
