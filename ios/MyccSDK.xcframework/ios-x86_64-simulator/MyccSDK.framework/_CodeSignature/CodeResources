<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/AudioHelper.h</key>
		<data>
		eZdnSIJKSbZVV+JNEAmo6a7BKlw=
		</data>
		<key>Headers/ConfigCommon.h</key>
		<data>
		PLsF1P3XWTcUQNxEiSNM09bWhGQ=
		</data>
		<key>Headers/MyccSDK-Swift.h</key>
		<data>
		fHL7EYk6/pwK7T4tJx9iFyFAKfs=
		</data>
		<key>Headers/MyccSDK.h</key>
		<data>
		OHvwgIHllbmRp/3I9/sNfsMU/xM=
		</data>
		<key>Info.plist</key>
		<data>
		n9yAfj334BcuWTFDSIfnK0k8218=
		</data>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		pMYndGdjPhBPgDSZEOX/Pnt36RY=
		</data>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		KckuDWlOjii4qYBmFMmHsSchPRU=
		</data>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		SD4QfbX5sbeWvF7RFykTMtd233w=
		</data>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		KckuDWlOjii4qYBmFMmHsSchPRU=
		</data>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		JZYZywubN7+hVrWqNhLwVTd+0YY=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		IPdgAbJ9GFzkKqChWY2+lT1pmt4=
		</data>
		<key>Resource.bundle/bg_demo.png</key>
		<data>
		cXEVaVKhmt1GtAzpSwh/19pvBx4=
		</data>
		<key>Resource.bundle/ic-microphone-selected.png</key>
		<data>
		F5Nbk0CwaUsXoBOUJpFxbMSKiPo=
		</data>
		<key>Resource.bundle/ic-microphone.png</key>
		<data>
		l4aCzXJ0SrWJrjj6cQTEPQ3Teyg=
		</data>
		<key>Resource.bundle/ic-red-hang-up.png</key>
		<data>
		k3r/PMfB00R7WRv3RUJtOcHSn6M=
		</data>
		<key>Resource.bundle/ic-speaker-selected.png</key>
		<data>
		fxMEAAGtycrxXPFWn0DvKTlyuO8=
		</data>
		<key>Resource.bundle/ic-speaker.png</key>
		<data>
		A0SlGjvXD2IGOcdJQ6ppz+zZV2s=
		</data>
		<key>Resource.bundle/ic-switch-camera.png</key>
		<data>
		7NyAYjuE94EizIAme5Sppj5ilWs=
		</data>
		<key>Resource.bundle/ic_accept_call.png</key>
		<data>
		vkhdV2faJB236E9RdmA46aZ6ksM=
		</data>
		<key>Resource.bundle/ic_audio_call.png</key>
		<data>
		iJ/4e9Vl1PoQXNFKXjvmS2Q+AbU=
		</data>
		<key>Resource.bundle/ic_camera.jpg</key>
		<data>
		ImeNiNXTVPiktNV5hIEPjpapAAE=
		</data>
		<key>Resource.bundle/ic_deny_call.png</key>
		<data>
		RSxYHj6X3ttwZFtFP3dODRt4IeI=
		</data>
		<key>Resource.bundle/ic_dropdown.png</key>
		<data>
		6VAM7feBGg+c5VMipAq9+p6tCIo=
		</data>
		<key>Resource.bundle/ic_one_way.png</key>
		<data>
		D0Jt8PffYPWC270F0yTdBEXFl8M=
		</data>
		<key>Resource.bundle/ic_two_way.png</key>
		<data>
		vZ+cbd3fw39bfROr2XOa4wBdPFM=
		</data>
		<key>Resource.bundle/ic_zoom_in.png</key>
		<data>
		1QAk54uFQqslsrY4IfMwMHHaIeo=
		</data>
		<key>Resource.bundle/ic_zoom_out.png</key>
		<data>
		KvliawDcbjJip12daIfYJ69xfD4=
		</data>
		<key>Resource.bundle/sdk_ic_close.png</key>
		<data>
		l31CfkogR2pKCF4tp5PHALyTbZc=
		</data>
		<key>Resource.bundle/sdk_vc_accept_call.png</key>
		<data>
		myPEmIeTdSkyyo0Emm8FtA7T8cw=
		</data>
		<key>Resource.bundle/sdk_vc_camera_deselected.png</key>
		<data>
		syr2i+fllyu22sT9qa8KzlFx19w=
		</data>
		<key>Resource.bundle/sdk_vc_camera_selected.png</key>
		<data>
		gVGcVMdgK167TO8YOwv6/0xLxRY=
		</data>
		<key>Resource.bundle/sdk_vc_cancel_call.png</key>
		<data>
		FvS1bwxBqAh4/gOBDYm9yGu5+c4=
		</data>
		<key>Resource.bundle/sdk_vc_cancel_callcoming.png</key>
		<data>
		Ozh5JjC6kaV1po/K6LSc0QPJVws=
		</data>
		<key>Resource.bundle/sdk_vc_mic_deselected.png</key>
		<data>
		PyAc3KltJhhaWVh+q/orXuxhbPI=
		</data>
		<key>Resource.bundle/sdk_vc_mic_selected.png</key>
		<data>
		dPo0C0Bkpm4yJO+CpTLoSJISj6g=
		</data>
		<key>Resource.bundle/sdk_vc_mute.png</key>
		<data>
		phPnFLuYo13ofuZRDROEBwIH5oU=
		</data>
		<key>Resource.bundle/sdk_vc_speaker.png</key>
		<data>
		bQvHU8ww5FJSeK2LJcf4JRV1JiE=
		</data>
		<key>Resource.bundle/sdk_vc_speaker_deselected.png</key>
		<data>
		epXcDzZMX2IVE8Bcp6xs1RJAi7U=
		</data>
		<key>Resource.bundle/sdk_vc_speaker_selected.png</key>
		<data>
		H5gmzklcWSpihdljD626VBNUpj4=
		</data>
		<key>Resource.bundle/sdk_vc_viewtop.png</key>
		<data>
		H58scXeJ09ShsIRHyCBktOLDqSo=
		</data>
		<key>Resource.bundle/vt_ic_end_call.png</key>
		<data>
		vBCZ4zGCE+15L4un82R0LCnCIeE=
		</data>
		<key>Resource.bundle/vt_ic_mic_off.png</key>
		<data>
		D9ildREOmBvnL9S2tOZWLkIXBa0=
		</data>
		<key>Resource.bundle/vt_ic_mic_on.png</key>
		<data>
		RNHjojU2DSpldoU/hGFs8GV10Z4=
		</data>
		<key>Resource.bundle/vt_ic_switch_camera.png</key>
		<data>
		XdJoY7NEn6U2b7DRnWkzW00fqK4=
		</data>
		<key>beausan_bold.otf</key>
		<data>
		B+BwOMPxLIH562n6dE/ZETq8TdE=
		</data>
		<key>beausans_regular.otf</key>
		<data>
		jIBVmiXzjAXAksAE+DYqB/cPZUM=
		</data>
		<key>beausans_semibold.otf</key>
		<data>
		fMetpuWzM9bvgJ8eLZGhqLKk4Zk=
		</data>
		<key>linphonerc</key>
		<data>
		rjGr7u1iPfbaWAz1GRfg6TSVMg4=
		</data>
		<key>linphonerc-factory</key>
		<data>
		ULi9C8vIN/zJRasPtgvYYU+Io7U=
		</data>
		<key>linphonerc~ipad</key>
		<data>
		hVb3GEORKhAF0S1Qj05x3F7Sbco=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/AudioHelper.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6KNAHui4Sj4WidkZsyCgl16b0jlamMejrlBFoXDsBI4=
			</data>
		</dict>
		<key>Headers/ConfigCommon.h</key>
		<dict>
			<key>hash2</key>
			<data>
			F7y8PjD8260iSXvjlfXH04p6NwVrVCLK5o/srmin5DQ=
			</data>
		</dict>
		<key>Headers/MyccSDK-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			2aJljUn8fTohCgVqaumHjHcTy6MEYTgHHm0B1KEwCH4=
			</data>
		</dict>
		<key>Headers/MyccSDK.h</key>
		<dict>
			<key>hash2</key>
			<data>
			vtLyCDYHS1Jk5HYLh2+oNKPKMNLd3mCZrU57TTwgGcM=
			</data>
		</dict>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2BxqVAkSPCv9o7HJKTGRtI1zbi5+ZBnm3Ve78Ip8QF8=
			</data>
		</dict>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			UqcdAoQhLz4iEWCjSBEspErtZ8NKlo1adSwbbNurNSQ=
			</data>
		</dict>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			Kfs9YP6fWShKgMivrDT9Y72QJZJ/ewy7rtzwUgK0XAs=
			</data>
		</dict>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			UqcdAoQhLz4iEWCjSBEspErtZ8NKlo1adSwbbNurNSQ=
			</data>
		</dict>
		<key>Modules/MyccSDK.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			Ce/QOkKXGAHwXUBrf0m/5rvkJ3boDvEhIK6uObqhuA8=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			OJXuvUCxCZqX1dfLmZNZPcBU89KeltTMVEWua78ubqo=
			</data>
		</dict>
		<key>Resource.bundle/bg_demo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			G7jI0jGjo/r820zIJVIsLl5JF5EUKbfWyPnmeVlgdTU=
			</data>
		</dict>
		<key>Resource.bundle/ic-microphone-selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zV2yqDWicDVNYsikBCRyPBDoToyiIqE/+6F25z9qgds=
			</data>
		</dict>
		<key>Resource.bundle/ic-microphone.png</key>
		<dict>
			<key>hash2</key>
			<data>
			aBXVfkNo3U7z/iaTNuLoI5oxF+FZwp4VMIkmV7pDxAE=
			</data>
		</dict>
		<key>Resource.bundle/ic-red-hang-up.png</key>
		<dict>
			<key>hash2</key>
			<data>
			nLWlBncvRa2sA1IV3+PFjySacPq6rI27hxLritjZhj8=
			</data>
		</dict>
		<key>Resource.bundle/ic-speaker-selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Em0nusC7QXEOuemnurFhQGxjKi2yaRrn+YNUykBY4b8=
			</data>
		</dict>
		<key>Resource.bundle/ic-speaker.png</key>
		<dict>
			<key>hash2</key>
			<data>
			SUgVJIT8vY5xXt194IWuXy7+WUufv3P2fwxFGz5/kIw=
			</data>
		</dict>
		<key>Resource.bundle/ic-switch-camera.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7f2OWgq2Gb7pO/LWqQGTi+Cmyd4yySIWY/suu/WR8I0=
			</data>
		</dict>
		<key>Resource.bundle/ic_accept_call.png</key>
		<dict>
			<key>hash2</key>
			<data>
			h8Sg+O4YX5zNTjH3M4C3ruwafmDlCOojboiIZvoAZAI=
			</data>
		</dict>
		<key>Resource.bundle/ic_audio_call.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zPUqeWejLt6C4AowMpiMy/XY5zkRv5VR8wBorpRkQjw=
			</data>
		</dict>
		<key>Resource.bundle/ic_camera.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			f+7wMNIKmA5Om7VqU/7FuSt0vjXuzB37pUKriWcdTCI=
			</data>
		</dict>
		<key>Resource.bundle/ic_deny_call.png</key>
		<dict>
			<key>hash2</key>
			<data>
			SpnQGXXSIYxU5/ZTXIpdjfg9yTbzXWO17fqGTrknLDM=
			</data>
		</dict>
		<key>Resource.bundle/ic_dropdown.png</key>
		<dict>
			<key>hash2</key>
			<data>
			wqmwtkj6Sozt/yXSJoshElS+hnYl9c+wnakDeu7DuyM=
			</data>
		</dict>
		<key>Resource.bundle/ic_one_way.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kwhFlaUXBsm0yLsEH3F2nhuZJPB69iDOi537KDYV/rQ=
			</data>
		</dict>
		<key>Resource.bundle/ic_two_way.png</key>
		<dict>
			<key>hash2</key>
			<data>
			F/DImUQspkTQsxhuwf0frCtmFF0XTjAUUWRDup5N4gk=
			</data>
		</dict>
		<key>Resource.bundle/ic_zoom_in.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Fg+OG5Ki+LcjYmNOsDXAnrvFtIExq3mhJ0KdXBkjLCE=
			</data>
		</dict>
		<key>Resource.bundle/ic_zoom_out.png</key>
		<dict>
			<key>hash2</key>
			<data>
			IV1wo+0WUZBet2CVEg1+PbWdOwxqTs6K29QqSp3VC5I=
			</data>
		</dict>
		<key>Resource.bundle/sdk_ic_close.png</key>
		<dict>
			<key>hash2</key>
			<data>
			NKPPUKoFaR+rwH+K3PG3iif5rcPot/Ur26vU4ZxeNHg=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_accept_call.png</key>
		<dict>
			<key>hash2</key>
			<data>
			c+qSy0qKS8HhGuxs7u7yjJ7QfNuk4gLmUMNvk9TTjOI=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_camera_deselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6Nj8c2bFEMCs2sjuw+NEVvf7ualAuGDJmJFcZacqfRI=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_camera_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			tZE5dyD/9kq49dS+sOwmA8JlEkCzrhwRETtNnIstdec=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_cancel_call.png</key>
		<dict>
			<key>hash2</key>
			<data>
			LO0LmGwrsseckHdGiaaxJWbCCojorupirSwzwVC3Z1o=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_cancel_callcoming.png</key>
		<dict>
			<key>hash2</key>
			<data>
			vesy2+DluMUZ9qF2xUCVePZgMt/ZFYimpAo7EFUSB60=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_mic_deselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			YokFU7HfQ930v9reL5gQAflgST73ZTP+oI7EQdRaVcU=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_mic_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			MXKUIPCc2OyQMICIYV6lLZPvrIW61CJXddg0oqs11f4=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_mute.png</key>
		<dict>
			<key>hash2</key>
			<data>
			oCJiWcVHRq0/JhIJiCFlbeB6m9S3nSs+KAuY6BzYTBQ=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_speaker.png</key>
		<dict>
			<key>hash2</key>
			<data>
			l4tMeJTjPT0TD7aLn3lzAHPttPhrdcyJs/YDhOG4UUk=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_speaker_deselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			QICHVQwlEtXIkUyTeIwQfkDyT3WpTq5YnY2t/54WZeI=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_speaker_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Zn1te0SyRtvg5a+Xx36YuQUAJiGV2k//9SaANtfjdaA=
			</data>
		</dict>
		<key>Resource.bundle/sdk_vc_viewtop.png</key>
		<dict>
			<key>hash2</key>
			<data>
			hLuZoH/+t20GKqwYuOSUHIU5axAcQvFc+LEatOhZvnk=
			</data>
		</dict>
		<key>Resource.bundle/vt_ic_end_call.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RGyarBi9g+lA2H2DHJnAUT4kpMQ8DwZ+wdjuqYilHpQ=
			</data>
		</dict>
		<key>Resource.bundle/vt_ic_mic_off.png</key>
		<dict>
			<key>hash2</key>
			<data>
			5XfqUCjMVZ84wkqSkiTEEcpYA2YQjtJyO5reVcYbUSY=
			</data>
		</dict>
		<key>Resource.bundle/vt_ic_mic_on.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7Irpp53zJVYzCqMTiqTIKd74wEjz+lR1HaX2YygDCls=
			</data>
		</dict>
		<key>Resource.bundle/vt_ic_switch_camera.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+MoH9yadaKmX9MqkqS/CvonKYtXCSKEcONKOoD0YO+M=
			</data>
		</dict>
		<key>beausan_bold.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			rNiiaOIKBE5Mewz5KMtGiHg28h/UIuc5TLn6TGvjArM=
			</data>
		</dict>
		<key>beausans_regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			JgCLVBmy6K3myXuhrkjFQSqhcjw2f37C/rXTJ2UbO08=
			</data>
		</dict>
		<key>beausans_semibold.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			iwhtYrtV09kgLeRXU6JRpXxW8tsXdgf4HLKU3KcSNuQ=
			</data>
		</dict>
		<key>linphonerc</key>
		<dict>
			<key>hash2</key>
			<data>
			IYR6iS6GgA4k0HGLowPYWDnrsxPSNaDBNbcMGAR55bA=
			</data>
		</dict>
		<key>linphonerc-factory</key>
		<dict>
			<key>hash2</key>
			<data>
			8qJCS61/bHVyt0Jr2kTqi3Wt+bcqlIyOSivU/0xqNkk=
			</data>
		</dict>
		<key>linphonerc~ipad</key>
		<dict>
			<key>hash2</key>
			<data>
			5d2nDdQVfQIkAgVh9YLqQCobBbYXheHD72Q//KP+8QI=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
