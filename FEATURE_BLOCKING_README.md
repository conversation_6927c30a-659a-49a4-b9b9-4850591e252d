# Feature Blocking System (Flutter 1.22.6)

Hệ thống chặn chức năng dựa trên API `_alertService.getAlerts` để kiểm soát quyền truy cập các tính năng trong ứng dụng.

**Lưu ý**: <PERSON>ệ thống này được tối ưu hóa cho Flutter 1.22.6 và sử dụng null-safety syntax cũ.

## Tổng quan

Hệ thống bao gồm các thành phần chính:

1. **FeatureBlockingService**: Quản lý danh sách chức năng bị chặn
2. **RouteGuardMiddleware**: Middleware kiểm tra quyền truy cập route
3. **BlockedFeatureDialog**: Dialog thông báo khi chức năng bị chặn
4. **AlertChecker**: Tích hợp với API alerts để cập nhật trạng thái chặn

## Cách hoạt động

### 1. <PERSON><PERSON><PERSON> danh sách chức năng bị chặn

```dart
// API getAlerts trả về danh sách AlertModel
List<AlertModel> alerts = await _alertService.getAlerts(
  systemName: "HSPartner",
  features: "ALL",
);

// Các alert có isRequired = true sẽ được coi là chức năng bị chặn
final blockedAlerts = alerts.where((alert) => alert.isRequired).toList();
```

### 2. Cập nhật trạng thái chặn

```dart
// FeatureBlockingService tự động cập nhật từ AlertChecker
await FeatureBlockingService.instance.updateBlockedFeatures(alerts);
```

### 3. Kiểm tra quyền truy cập

```dart
// Kiểm tra chức năng có bị chặn không
bool isBlocked = FeatureBlockingService.instance.isFeatureBlocked('HOME');

// Kiểm tra route có thể truy cập không
bool canAccess = RouteGuardMiddleware.instance.canAccessRoute('/home');
```

### 4. Điều hướng với kiểm tra quyền

```dart
// Sử dụng extension method (Flutter 1.22.6)
await context.guardedPushNamed('/home');

// Hoặc sử dụng trực tiếp middleware
await RouteGuardMiddleware.instance.checkAndNavigate(context, '/home');

// Với parameters (không dùng named parameters nullable)
await RouteGuardMiddleware.instance.checkAndNavigate(
  context,
  '/home',
  arguments: someArguments,
  blockedMessage: 'Custom message',
);
```

## Cấu hình

### Định nghĩa chức năng có thể bị chặn

Trong `BlockableFeatures` class:

```dart
class BlockableFeatures {
  static const String ALL = 'ALL';
  static const String HOME = 'HOME';
  static const String TASK_LIST = 'TASK_LIST';
  static const String ORDER_MANAGEMENT = 'ORDER_MANAGEMENT';
  // ... thêm các chức năng khác
}
```

### Map chức năng với routes

```dart
static const Map<String, List<String>> featureToRoutes = {
  HOME: ['/home'],
  TASK_LIST: ['/task_list', '/task_list_screen', '/page_task'],
  ORDER_MANAGEMENT: ['/my_order', '/order_detail', '/create_order'],
  // ... mapping khác
};
```

### Cập nhật routes.dart

```dart
// Sử dụng helper function để tạo guarded route
home: _guardedRoute('/home', (context) => HomeScreen()),
task_list: _guardedRoute('/task_list', (context) {
  final args = ModalRoute.of(context).settings.arguments;
  return TaskListPage(taskListFilterArgs: args);
}),
```

## Các trường hợp sử dụng

### 1. Chặn tất cả chức năng

```json
{
  "feature": "ALL",
  "message": "Ứng dụng đang bảo trì",
  "isRequired": true
}
```

### 2. Chặn chức năng cụ thể

```json
{
  "feature": "HOME",
  "message": "Trang chủ đang cập nhật",
  "isRequired": true
}
```

### 3. Chỉ thông báo (không chặn)

```json
{
  "feature": "COMMUNICATION",
  "message": "Có cập nhật mới cho tính năng liên lạc",
  "isRequired": false
}
```

## Testing

### Sử dụng FeatureBlockingTestHelper

```dart
// Test với dữ liệu mẫu
await FeatureBlockingTestHelper.instance.testWithSampleData();

// Test chặn tất cả
await FeatureBlockingTestHelper.instance.testBlockAllFeatures();

// Test không chặn gì
await FeatureBlockingTestHelper.instance.testNoBlockedFeatures();

// Xem thông tin debug
FeatureBlockingTestHelper.instance.printDebugInfo();
```

### Sử dụng Example Widget (Flutter 1.22.6)

```dart
// Thêm vào routes.dart để test
feature_blocking_example: (BuildContext context) => FeatureBlockingExample(),

// Hoặc sử dụng demo navigation
guarded_navigation_demo: (BuildContext context) => GuardedNavigationDemo(),
```

## Cache và Performance

- **Cache Duration**: 1 giờ (có thể cấu hình)
- **Storage**: SharedPreferences
- **Auto Refresh**: Khi cache hết hạn hoặc app khởi động

### Quản lý cache

```dart
// Load từ cache
await FeatureBlockingService.instance.loadFromCache();

// Xóa cache
await FeatureBlockingService.instance.clearCache();

// Kiểm tra cần refresh không
bool shouldRefresh = FeatureBlockingService.instance.shouldRefreshFromAPI();
```

## Error Handling

### Khi API lỗi

- Sử dụng cache cũ nếu có
- Không chặn chức năng nào nếu không có cache
- Log lỗi để debug

### Khi route bị chặn

- Hiển thị dialog thông báo
- Cung cấp link cập nhật ứng dụng
- Cho phép dismiss nếu không bắt buộc

## Customization

### Tùy chỉnh dialog

```dart
// Tạo dialog tùy chỉnh
class CustomBlockedFeatureDialog extends StatelessWidget {
  // Implementation
}

// Sử dụng trong middleware
RouteGuardMiddleware.instance.handleBlockedRoute(
  context, 
  route, 
  customDialog: CustomBlockedFeatureDialog(),
);
```

### Tùy chỉnh thông báo

```dart
// Thông báo tùy chỉnh cho từng route
await context.guardedPushNamed(
  '/home',
  blockedMessage: 'Trang chủ đang bảo trì, vui lòng thử lại sau',
);
```

## Debug và Monitoring

### Log thông tin

```dart
// Enable debug logging
debugPrint('FeatureBlockingService: Updated blocked features: $_blockedFeatures');
```

### Thông tin debug

```dart
Map<String, dynamic> debugInfo = FeatureBlockingService.instance.getBlockingInfo();
print('Blocked features: ${debugInfo['blockedFeatures']}');
print('Last update: ${debugInfo['lastUpdate']}');
print('Cache valid: ${debugInfo['cacheValid']}');
```

## Best Practices

1. **Luôn kiểm tra cache trước khi gọi API**
2. **Sử dụng extension methods để dễ dàng điều hướng**
3. **Test kỹ các trường hợp edge case**
4. **Cung cấp thông báo rõ ràng cho người dùng**
5. **Log đầy đủ để debug khi cần**

## Troubleshooting

### Chức năng không bị chặn dù API trả về isRequired = true

- Kiểm tra feature name có đúng không
- Kiểm tra mapping trong `featureToRoutes`
- Kiểm tra cache có được cập nhật không

### Dialog không hiển thị

- Kiểm tra context có hợp lệ không
- Kiểm tra route có được guard không
- Kiểm tra import dialog widget

### Performance issues

- Kiểm tra cache duration
- Kiểm tra frequency của API calls
- Optimize loading từ SharedPreferences
