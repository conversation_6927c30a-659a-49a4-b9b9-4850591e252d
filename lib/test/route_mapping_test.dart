import 'package:flutter/material.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/widgets/screen_wrapper/alert_screen_wrapper.dart';
import 'package:home_care_partner/ui/direct_app/alert_service.dart';
import 'package:home_care_partner/ui/direct_app/alert_dialog_widget.dart';

/// Test để kiểm tra route mapping với alert system
class RouteMappingTest extends StatefulWidget {
  @override
  _RouteMappingTestState createState() => _RouteMappingTestState();
}

class _RouteMappingTestState extends State<RouteMappingTest> {
  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;
  String _testResult = '';

  @override
  void initState() {
    super.initState();
    _runTests();
  }

  Future<void> _runTests() async {
    setState(() {
      _testResult = 'Running tests...';
    });

    // Test với dữ liệu giống nh<PERSON> bạn nhận được
    final testAlerts = [
      AlertModel(feature: 'DETAIL_ORDER', message: 'Detail order alert', isRequired: true),
      AlertModel(feature: 'DEBT', message: 'Debt alert', isRequired: false),
      AlertModel(feature: 'COMMISSION', message: 'Commission alert', isRequired: true),
      AlertModel(feature: 'INVOICE', message: 'Invoice alert', isRequired: false),
      AlertModel(feature: 'CREATE_ORDER', message: 'Create order alert', isRequired: true),
      AlertModel(feature: 'profile_management', message: 'Profile management alert', isRequired: false),
    ];

    await _featureBlockingService.updateAlerts(testAlerts);

    // Test các route mapping
    final testCases = [
      {'route': '/profile_management', 'expected': 'profile_management'},
      {'route': '/create_order', 'expected': 'CREATE_ORDER'},
      {'route': '/debt', 'expected': 'DEBT'},
      {'route': '/order_detail', 'expected': 'DETAIL_ORDER'},
    ];

    String result = 'Test Results:\n\n';
    
    for (var testCase in testCases) {
      String route = testCase['route'];
      String expected = testCase['expected'];
      
      // Test 1: Kiểm tra route key trực tiếp
      String routeKey = route.replaceFirst('/', '');
      AlertModel alert1 = _featureBlockingService.getAlertForFeature(routeKey);
      
      // Test 2: Kiểm tra feature mapping
      String featureName = RouteFeatureMapping.getFeatureForRoute(route);
      AlertModel alert2 = _featureBlockingService.getAlertForFeature(featureName);
      
      result += 'Route: $route\n';
      result += '  Route key ($routeKey): ${alert1 != null ? "✅ Found" : "❌ Not found"}\n';
      result += '  Feature ($featureName): ${alert2 != null ? "✅ Found" : "❌ Not found"}\n';
      result += '  Expected: $expected\n';
      result += '  Status: ${(alert1 != null || alert2 != null) ? "✅ PASS" : "❌ FAIL"}\n\n';
    }

    // Test tất cả alerts hiện có
    result += 'All Available Alerts:\n';
    for (var alert in _featureBlockingService.alerts) {
      result += '  ${alert.feature}: ${alert.message} (required: ${alert.isRequired})\n';
    }

    setState(() {
      _testResult = result;
    });
  }

  /// Test AlertDialogWidget trực tiếp
  void _testAlertDialogWidget(bool isRequired) {
    final testAlert = AlertModel(
      feature: 'TEST',
      message: isRequired
        ? 'Đây là alert bắt buộc (isRequired: true). Bạn không thể đóng bằng back button hoặc tap outside.'
        : 'Đây là alert có thể đóng (isRequired: false). Bạn có thể đóng bằng nút Hủy, back button hoặc tap outside.',
      isRequired: isRequired,
    );

    showDialog(
      context: context,
      barrierDismissible: !isRequired,
      builder: (BuildContext context) {
        return AlertDialogWidget(
          alert: testAlert,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Route Mapping Test'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Route Mapping Test Results',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]),
              ),
              child: Text(
                _testResult,
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: RaisedButton(
                    onPressed: _runTests,
                    child: Text('Run Tests Again'),
                    color: Colors.blue,
                    textColor: Colors.white,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: RaisedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/profile_management');
                    },
                    child: Text('Test Profile Management'),
                    color: Colors.green,
                    textColor: Colors.white,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RaisedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/create_order');
                    },
                    child: Text('Test Create Order'),
                    color: Colors.orange,
                    textColor: Colors.white,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: RaisedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/debt');
                    },
                    child: Text('Test Debt'),
                    color: Colors.red,
                    textColor: Colors.white,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RaisedButton(
                    onPressed: () {
                      _testAlertDialogWidget(false);
                    },
                    child: Text('Test AlertDialog (Dismissible)'),
                    color: Colors.purple,
                    textColor: Colors.white,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: RaisedButton(
                    onPressed: () {
                      _testAlertDialogWidget(true);
                    },
                    child: Text('Test AlertDialog (Required)'),
                    color: Colors.indigo,
                    textColor: Colors.white,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Text(
              'Instructions:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '1. Run tests to see mapping results\n'
              '2. Try navigating to test routes\n'
              '3. Check if popups appear correctly\n'
              '4. Verify route key vs feature mapping',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper để debug route mapping
class RouteMappingDebugger {
  static void debugRouteMapping(String route) {
    print('=== Route Mapping Debug ===');
    print('Route: $route');
    
    // Test route key
    String routeKey = route.replaceFirst('/', '');
    print('Route key: $routeKey');
    
    // Test feature mapping
    String featureName = RouteFeatureMapping.getFeatureForRoute(route);
    print('Mapped feature: $featureName');
    
    // Check alerts
    final service = FeatureBlockingService.instance;
    AlertModel alert1 = service.getAlertForFeature(routeKey);
    AlertModel alert2 = service.getAlertForFeature(featureName);
    
    print('Alert by route key: ${alert1?.message ?? "None"}');
    print('Alert by feature: ${alert2?.message ?? "None"}');
    print('========================');
  }
}
