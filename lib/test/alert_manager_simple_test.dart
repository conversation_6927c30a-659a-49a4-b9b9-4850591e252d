import 'package:flutter/material.dart';
import 'package:home_care_partner/services/alert_manager_service.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/utils/alert_utils.dart';
import 'package:home_care_partner/ui/direct_app/alert_service.dart';

/// Test đơn g<PERSON> đ<PERSON> verify AlertManagerService hoạt động
class AlertManagerSimpleTest extends StatefulWidget {
  @override
  _AlertManagerSimpleTestState createState() => _AlertManagerSimpleTestState();
}

class _AlertManagerSimpleTestState extends State<AlertManagerSimpleTest> {
  String _status = 'Chưa test';
  bool _isLoading = false;
  List<AlertModel> _currentAlerts = [];

  @override
  void initState() {
    super.initState();
    _loadCurrentStatus();
  }

  void _loadCurrentStatus() {
    setState(() {
      _currentAlerts = FeatureBlockingService.instance.alerts;
      _status = 'Loaded ${_currentAlerts.length} alerts from cache';
    });
  }

  Future<void> _testInitialize() async {
    setState(() {
      _isLoading = true;
      _status = 'Initializing AlertManagerService...';
    });

    try {
      await AlertUtils.initialize();
      _loadCurrentStatus();
      setState(() {
        _status = 'Initialize completed. ${_currentAlerts.length} alerts loaded.';
      });
    } catch (e) {
      setState(() {
        _status = 'Initialize failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testRefresh() async {
    setState(() {
      _isLoading = true;
      _status = 'Refreshing alerts from API...';
    });

    try {
      await AlertUtils.refresh();
      _loadCurrentStatus();
      setState(() {
        _status = 'Refresh completed. ${_currentAlerts.length} alerts loaded.';
      });
    } catch (e) {
      setState(() {
        _status = 'Refresh failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testClear() async {
    setState(() {
      _isLoading = true;
      _status = 'Clearing all alerts...';
    });

    try {
      await AlertUtils.clear();
      _loadCurrentStatus();
      setState(() {
        _status = 'Clear completed. ${_currentAlerts.length} alerts remaining.';
      });
    } catch (e) {
      setState(() {
        _status = 'Clear failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _printDebugInfo() {
    AlertUtils.printDebugInfo();
    setState(() {
      _status = 'Debug info printed to console';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('AlertManager Simple Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(_status),
                    if (_isLoading) ...[
                      SizedBox(height: 8),
                      LinearProgressIndicator(),
                    ],
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Current Alerts
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Alerts (${_currentAlerts.length})',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    if (_currentAlerts.isEmpty)
                      Text('No alerts found')
                    else
                      ...(_currentAlerts.map((alert) => Padding(
                        padding: EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                '${alert.feature}: ${alert.message}',
                                style: TextStyle(fontSize: 14),
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: alert.isRequired ? Colors.red : Colors.green,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                alert.isRequired ? 'Required' : 'Optional',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )).toList()),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Test Buttons
            Text(
              'Test Functions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: RaisedButton(
                    onPressed: _isLoading ? null : _testInitialize,
                    child: Text('Initialize'),
                    color: Colors.blue,
                    textColor: Colors.white,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: RaisedButton(
                    onPressed: _isLoading ? null : _testRefresh,
                    child: Text('Refresh'),
                    color: Colors.green,
                    textColor: Colors.white,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: RaisedButton(
                    onPressed: _isLoading ? null : _testClear,
                    child: Text('Clear'),
                    color: Colors.red,
                    textColor: Colors.white,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: RaisedButton(
                    onPressed: _printDebugInfo,
                    child: Text('Debug Info'),
                    color: Colors.orange,
                    textColor: Colors.white,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Navigation Test
            Text(
              'Navigation Test',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: RaisedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/profile_management');
                    },
                    child: Text('Test Profile Management'),
                    color: Colors.purple,
                    textColor: Colors.white,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: RaisedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/home');
                    },
                    child: Text('Test Home'),
                    color: Colors.indigo,
                    textColor: Colors.white,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Instructions
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '1. Tap "Initialize" to setup AlertManagerService\n'
                      '2. Tap "Refresh" to fetch alerts from API\n'
                      '3. Check current alerts list\n'
                      '4. Test navigation to see popups\n'
                      '5. Check console for debug logs',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
