import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:home_care_partner/ui/direct_app/alert_service.dart';

/// Service để quản lý danh sách các chức năng bị chặn
class FeatureBlockingService {
  static const String _BLOCKED_FEATURES_KEY = 'blocked_features';
  static const String _LAST_UPDATE_KEY = 'blocked_features_last_update';
  static const int _CACHE_DURATION_HOURS = 1; // Cache trong 1 giờ

  static FeatureBlockingService _instance;
  static FeatureBlockingService get instance {
    if (_instance == null) {
      _instance = FeatureBlockingService._internal();
    }
    return _instance;
  }

  FeatureBlockingService._internal();

  List<String> _blockedFeatures = [];
  DateTime _lastUpdate;

  /// Lấy danh sách các chức năng bị chặn từ cache
  List<String> get blockedFeatures => List.unmodifiable(_blockedFeatures);

  /// Ki<PERSON>m tra xem một chức năng có bị chặn không
  bool isFeatureBlocked(String feature) {
    return _blockedFeatures.contains(feature) || 
           _blockedFeatures.contains('ALL'); // Nếu chặn ALL thì chặn tất cả
  }

  /// Cập nhật danh sách chức năng bị chặn từ API alerts
  Future<void> updateBlockedFeatures(List<AlertModel> alerts) async {
    try {
      // Lọc ra các alert có isRequired = true (chức năng bị chặn)
      final blockedAlerts = alerts.where((alert) => alert.isRequired).toList();
      
      // Lấy danh sách feature bị chặn
      _blockedFeatures = blockedAlerts.map((alert) => alert.feature).toList();
      _lastUpdate = DateTime.now();

      // Lưu vào SharedPreferences
      await _saveToCache();

      debugPrint('FeatureBlockingService: Updated blocked features: $_blockedFeatures');
    } catch (e) {
      debugPrint('FeatureBlockingService: Error updating blocked features: $e');
    }
  }

  /// Tải danh sách chức năng bị chặn từ cache
  Future<void> loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Kiểm tra thời gian cache
      final lastUpdateStr = prefs.getString(_LAST_UPDATE_KEY);
      if (lastUpdateStr != null) {
        _lastUpdate = DateTime.parse(lastUpdateStr);
        
        // Nếu cache còn hiệu lực
        if (_isCacheValid()) {
          final blockedFeaturesStr = prefs.getString(_BLOCKED_FEATURES_KEY);
          if (blockedFeaturesStr != null) {
            final List<dynamic> decoded = json.decode(blockedFeaturesStr);
            _blockedFeatures = decoded.cast<String>();
            debugPrint('FeatureBlockingService: Loaded from cache: $_blockedFeatures');
            return;
          }
        }
      }
      
      // Nếu không có cache hoặc cache hết hạn, khởi tạo danh sách rỗng
      _blockedFeatures = [];
      debugPrint('FeatureBlockingService: No valid cache found, initialized empty list');
    } catch (e) {
      debugPrint('FeatureBlockingService: Error loading from cache: $e');
      _blockedFeatures = [];
    }
  }

  /// Lưu danh sách vào cache
  Future<void> _saveToCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_BLOCKED_FEATURES_KEY, json.encode(_blockedFeatures));
      await prefs.setString(_LAST_UPDATE_KEY, _lastUpdate.toIso8601String());
    } catch (e) {
      debugPrint('FeatureBlockingService: Error saving to cache: $e');
    }
  }

  /// Kiểm tra cache có còn hiệu lực không
  bool _isCacheValid() {
    if (_lastUpdate == null) return false;

    final now = DateTime.now();
    final difference = now.difference(_lastUpdate);
    return difference.inHours < _CACHE_DURATION_HOURS;
  }

  /// Xóa cache
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_BLOCKED_FEATURES_KEY);
      await prefs.remove(_LAST_UPDATE_KEY);
      _blockedFeatures = [];
      _lastUpdate = null;
      debugPrint('FeatureBlockingService: Cache cleared');
    } catch (e) {
      debugPrint('FeatureBlockingService: Error clearing cache: $e');
    }
  }

  /// Kiểm tra xem có cần refresh từ API không
  bool shouldRefreshFromAPI() {
    return !_isCacheValid();
  }

  /// Lấy thông tin chi tiết về chức năng bị chặn
  Map<String, dynamic> getBlockingInfo() {
    return {
      'blockedFeatures': _blockedFeatures,
      'lastUpdate': _lastUpdate?.toIso8601String(),
      'cacheValid': _isCacheValid(),
      'shouldRefresh': shouldRefreshFromAPI(),
    };
  }
}

/// Enum định nghĩa các chức năng có thể bị chặn
class BlockableFeatures {
  static const String ALL = 'ALL';
  static const String HOME = 'HOME';
  static const String TASK_LIST = 'TASK_LIST';
  static const String ORDER_MANAGEMENT = 'ORDER_MANAGEMENT';
  static const String COMMUNICATION = 'COMMUNICATION';
  static const String PROFILE = 'PROFILE';
  static const String SETTINGS = 'SETTINGS';
  static const String REPORTS = 'REPORTS';
  static const String INVENTORY = 'INVENTORY';
  static const String CUSTOMER_360 = 'CUSTOMER_360';
  static const String DELIVERY_BILL = 'DELIVERY_BILL';
  
  /// Danh sách tất cả các chức năng có thể bị chặn
  static const List<String> allFeatures = [
    HOME,
    TASK_LIST,
    ORDER_MANAGEMENT,
    COMMUNICATION,
    PROFILE,
    SETTINGS,
    REPORTS,
    INVENTORY,
    CUSTOMER_360,
    DELIVERY_BILL,
  ];
  
  /// Map từ feature name sang route name
  static const Map<String, List<String>> featureToRoutes = {
    HOME: ['/home'],
    TASK_LIST: ['/task_list', '/task_list_screen', '/page_task'],
    ORDER_MANAGEMENT: ['/my_order', '/order_detail', '/create_order'],
    COMMUNICATION: ['/communication', '/messages'],
    PROFILE: ['/profile', '/change_password'],
    SETTINGS: ['/settings'],
    REPORTS: ['/reports'],
    INVENTORY: ['/personal_inventory', '/confirmation_bill_list'],
    CUSTOMER_360: ['/customer_360'],
    DELIVERY_BILL: ['/detail_bill', '/create_trans_warehouse'],
  };
  
  /// Lấy danh sách routes bị chặn từ feature name
  static List<String> getBlockedRoutes(String feature) {
    return featureToRoutes[feature] ?? [];
  }
  
  /// Kiểm tra route có bị chặn không
  static bool isRouteBlocked(String route, List<String> blockedFeatures) {
    if (blockedFeatures.contains(ALL)) return true;
    
    for (String feature in blockedFeatures) {
      final routes = getBlockedRoutes(feature);
      if (routes.contains(route)) return true;
    }
    return false;
  }
}
