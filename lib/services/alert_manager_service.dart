import 'package:flutter/foundation.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/ui/direct_app/alert_service.dart';

/// Service quản lý việc gọi API alerts và cập nhật FeatureBlockingService
class AlertManagerService {
  static AlertManagerService _instance;
  static AlertManagerService get instance {
    if (_instance == null) {
      _instance = AlertManagerService._internal();
    }
    return _instance;
  }

  AlertManagerService._internal();

  final AlertService _alertService = AlertService();
  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;
  
  bool _isLoading = false;
  DateTime _lastFetchTime;
  static const int _FETCH_INTERVAL_MINUTES = 30; // Fetch mỗi 30 phút

  /// Khởi tạo và fetch alerts lần đầu
  Future<void> initialize() async {
    debugPrint('AlertManagerService: Initializing...');
    
    // Load cache trước
    await _featureBlockingService.loadFromCache();
    
    // Fetch từ API nếu cần
    await fetchAlertsIfNeeded();
  }

  /// Fetch alerts từ API nếu cần thiết
  Future<void> fetchAlertsIfNeeded({bool forceRefresh = false}) async {
    // Kiểm tra xem có cần fetch không
    if (!forceRefresh && !_shouldFetch()) {
      debugPrint('AlertManagerService: Skip fetch - not needed');
      return;
    }

    if (_isLoading) {
      debugPrint('AlertManagerService: Already fetching');
      return;
    }

    await _fetchAlerts();
  }

  /// Force refresh alerts từ API
  Future<void> refreshAlerts() async {
    debugPrint('AlertManagerService: Force refresh alerts');
    await _fetchAlerts();
  }

  /// Kiểm tra xem có cần fetch từ API không
  bool _shouldFetch() {
    // Nếu chưa có cache hoặc cache hết hạn
    if (_featureBlockingService.shouldRefreshFromAPI()) {
      return true;
    }

    // Nếu đã fetch gần đây thì skip
    if (_lastFetchTime != null) {
      final now = DateTime.now();
      final difference = now.difference(_lastFetchTime);
      if (difference.inMinutes < _FETCH_INTERVAL_MINUTES) {
        return false;
      }
    }

    return true;
  }

  /// Fetch alerts từ API
  Future<void> _fetchAlerts() async {
    _isLoading = true;
    
    try {
      debugPrint('AlertManagerService: Fetching alerts from API...');
      
      final alerts = await _alertService.getAlerts(
        systemName: "HSPartner",
        features: "ALL",
      );

      debugPrint('AlertManagerService: Received ${alerts.length} alerts');
      
      // Cập nhật vào FeatureBlockingService
      await _featureBlockingService.updateAlerts(alerts);
      
      _lastFetchTime = DateTime.now();
      
      debugPrint('AlertManagerService: Successfully updated alerts');
      
    } catch (e) {
      debugPrint('AlertManagerService: Error fetching alerts: $e');
      // Không throw error để không crash app
    } finally {
      _isLoading = false;
    }
  }

  /// Lấy thông tin trạng thái
  Map<String, dynamic> getStatus() {
    return {
      'isLoading': _isLoading,
      'lastFetchTime': _lastFetchTime?.toIso8601String(),
      'shouldFetch': _shouldFetch(),
      'featureBlockingInfo': _featureBlockingService.getBlockingInfo(),
    };
  }

  /// Kiểm tra xem có đang loading không
  bool get isLoading => _isLoading;

  /// Lấy thời gian fetch cuối cùng
  DateTime get lastFetchTime => _lastFetchTime;

  /// Clear tất cả data
  Future<void> clearAll() async {
    await _featureBlockingService.clearCache();
    _lastFetchTime = null;
    debugPrint('AlertManagerService: Cleared all data');
  }
}

/// Mixin để dễ dàng sử dụng AlertManagerService trong widgets
mixin AlertManagerMixin<T extends StatefulWidget> on State<T> {
  AlertManagerService get alertManager => AlertManagerService.instance;

  @override
  void initState() {
    super.initState();
    // Tự động initialize khi widget được tạo
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAlertManager();
    });
  }

  Future<void> _initializeAlertManager() async {
    try {
      await alertManager.initialize();
    } catch (e) {
      debugPrint('AlertManagerMixin: Error initializing: $e');
    }
  }

  /// Refresh alerts manually
  Future<void> refreshAlerts() async {
    try {
      await alertManager.refreshAlerts();
    } catch (e) {
      debugPrint('AlertManagerMixin: Error refreshing: $e');
    }
  }

  /// Fetch alerts if needed
  Future<void> fetchAlertsIfNeeded() async {
    try {
      await alertManager.fetchAlertsIfNeeded();
    } catch (e) {
      debugPrint('AlertManagerMixin: Error fetching: $e');
    }
  }
}

/// Widget wrapper để tự động initialize AlertManagerService
class AlertManagerProvider extends StatefulWidget {
  final Widget child;
  final bool autoInitialize;

  const AlertManagerProvider({
    Key key,
    @required this.child,
    this.autoInitialize = true,
  }) : super(key: key);

  @override
  _AlertManagerProviderState createState() => _AlertManagerProviderState();
}

class _AlertManagerProviderState extends State<AlertManagerProvider> {
  @override
  void initState() {
    super.initState();
    if (widget.autoInitialize) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AlertManagerService.instance.initialize();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Singleton để quản lý lifecycle của AlertManagerService
class AlertManagerLifecycle {
  static bool _initialized = false;

  /// Initialize AlertManagerService một lần duy nhất
  static Future<void> ensureInitialized() async {
    if (_initialized) return;

    try {
      await AlertManagerService.instance.initialize();
      _initialized = true;
      debugPrint('AlertManagerLifecycle: Initialized successfully');
    } catch (e) {
      debugPrint('AlertManagerLifecycle: Error initializing: $e');
    }
  }

  /// Reset trạng thái (dùng cho testing)
  static void reset() {
    _initialized = false;
  }

  /// Kiểm tra đã initialize chưa
  static bool get isInitialized => _initialized;
}
