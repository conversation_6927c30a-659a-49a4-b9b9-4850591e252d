import 'package:flutter/material.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/middleware/route_guard_middleware.dart';
import 'package:home_care_partner/ui/direct_app/alert_service.dart';

/// V<PERSON> dụ sử dụng Feature Blocking System cho Flutter 1.22.6
class FeatureBlockingExample extends StatefulWidget {
  @override
  _FeatureBlockingExampleState createState() => _FeatureBlockingExampleState();
}

class _FeatureBlockingExampleState extends State<FeatureBlockingExample> {
  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;
  final RouteGuardMiddleware _routeGuardMiddleware = RouteGuardMiddleware.instance;
  
  String _status = 'Chưa khởi tạo';
  List<String> _blockedFeatures = [];

  @override
  void initState() {
    super.initState();
    _initializeFeatureBlocking();
  }

  /// Khởi tạo feature blocking
  Future<void> _initializeFeatureBlocking() async {
    setState(() {
      _status = 'Đang khởi tạo...';
    });

    try {
      // Load cache
      await _featureBlockingService.loadFromCache();
      
      // Cập nhật UI
      _updateStatus();
      
      setState(() {
        _status = 'Khởi tạo thành công';
      });
    } catch (e) {
      setState(() {
        _status = 'Lỗi khởi tạo: $e';
      });
    }
  }

  /// Cập nhật trạng thái UI
  void _updateStatus() {
    setState(() {
      _blockedFeatures = _featureBlockingService.blockedFeatures;
    });
  }

  /// Test với dữ liệu mẫu
  Future<void> _testWithSampleData() async {
    setState(() {
      _status = 'Đang test với dữ liệu mẫu...';
    });

    // Tạo dữ liệu test
    final sampleAlerts = [
      AlertModel(
        feature: 'HOME',
        message: 'Trang chủ đang bảo trì',
        isRequired: true,
      ),
      AlertModel(
        feature: 'TASK_LIST', 
        message: 'Danh sách công việc đang cập nhật',
        isRequired: true,
      ),
    ];

    // Cập nhật blocked features
    await _featureBlockingService.updateBlockedFeatures(sampleAlerts);
    
    _updateStatus();
    
    setState(() {
      _status = 'Test hoàn thành';
    });
  }

  /// Test chặn tất cả
  Future<void> _testBlockAll() async {
    setState(() {
      _status = 'Đang test chặn tất cả...';
    });

    final blockAllAlert = [
      AlertModel(
        feature: 'ALL',
        message: 'Ứng dụng đang bảo trì',
        isRequired: true,
      ),
    ];

    await _featureBlockingService.updateBlockedFeatures(blockAllAlert);
    
    _updateStatus();
    
    setState(() {
      _status = 'Test chặn tất cả hoàn thành';
    });
  }

  /// Xóa tất cả blocks
  Future<void> _clearAllBlocks() async {
    setState(() {
      _status = 'Đang xóa tất cả blocks...';
    });

    await _featureBlockingService.clearCache();
    
    _updateStatus();
    
    setState(() {
      _status = 'Đã xóa tất cả blocks';
    });
  }

  /// Test điều hướng
  void _testNavigation(String route) {
    if (_routeGuardMiddleware.canAccessRoute(route)) {
      // Có thể truy cập
      _showSnackBar('Route $route có thể truy cập', Colors.green);
    } else {
      // Bị chặn
      _routeGuardMiddleware.handleBlockedRoute(context, route);
    }
  }

  /// Hiển thị snackbar
  void _showSnackBar(String message, Color color) {
    Scaffold.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Feature Blocking Example'),
        backgroundColor: Colors.blue,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status card
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Trạng thái',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(_status),
                    SizedBox(height: 8),
                    Text(
                      'Chức năng bị chặn: ${_blockedFeatures.join(', ')}',
                      style: TextStyle(
                        color: _blockedFeatures.isEmpty ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Test buttons
            Text(
              'Test Functions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: RaisedButton(
                    onPressed: _testWithSampleData,
                    child: Text('Test Sample'),
                    color: Colors.blue,
                    textColor: Colors.white,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: RaisedButton(
                    onPressed: _testBlockAll,
                    child: Text('Block All'),
                    color: Colors.orange,
                    textColor: Colors.white,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8),
            
            RaisedButton(
              onPressed: _clearAllBlocks,
              child: Text('Clear All Blocks'),
              color: Colors.green,
              textColor: Colors.white,
            ),
            
            SizedBox(height: 16),
            
            // Navigation test
            Text(
              'Test Navigation',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                RaisedButton(
                  onPressed: () => _testNavigation('/home'),
                  child: Text('Test /home'),
                  color: Colors.grey[300],
                ),
                RaisedButton(
                  onPressed: () => _testNavigation('/task_list'),
                  child: Text('Test /task_list'),
                  color: Colors.grey[300],
                ),
                RaisedButton(
                  onPressed: () => _testNavigation('/profile'),
                  child: Text('Test /profile'),
                  color: Colors.grey[300],
                ),
                RaisedButton(
                  onPressed: () => _testNavigation('/messages'),
                  child: Text('Test /messages'),
                  color: Colors.grey[300],
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Debug info
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Debug Info',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Cache valid: ${_featureBlockingService.getBlockingInfo()['cacheValid']}',
                    ),
                    Text(
                      'Should refresh: ${_featureBlockingService.shouldRefreshFromAPI()}',
                    ),
                    Text(
                      'Last update: ${_featureBlockingService.getBlockingInfo()['lastUpdate'] ?? 'Never'}',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget đơn giản để demo navigation với guard
class GuardedNavigationDemo extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Guarded Navigation Demo'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RaisedButton(
              onPressed: () async {
                // Sử dụng extension method (nếu có)
                final success = await context.guardedPushNamed('/home');
                if (!success) {
                  print('Navigation to /home was blocked');
                }
              },
              child: Text('Navigate to Home (Guarded)'),
            ),
            SizedBox(height: 16),
            RaisedButton(
              onPressed: () async {
                // Sử dụng trực tiếp middleware
                final success = await RouteGuardMiddleware.instance.checkAndNavigate(
                  context,
                  '/task_list',
                );
                if (!success) {
                  print('Navigation to /task_list was blocked');
                }
              },
              child: Text('Navigate to Task List (Guarded)'),
            ),
          ],
        ),
      ),
    );
  }
}
