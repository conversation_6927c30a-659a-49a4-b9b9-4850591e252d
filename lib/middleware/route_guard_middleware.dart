import 'package:flutter/material.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/widgets/dialog/blocked_feature_dialog.dart';

/// Middleware để kiểm tra quyền truy cập route
class RouteGuardMiddleware {
  static RouteGuardMiddleware? _instance;
  static RouteGuardMiddleware get instance {
    _instance ??= RouteGuardMiddleware._internal();
    return _instance!;
  }

  RouteGuardMiddleware._internal();

  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;

  /// Kiểm tra xem route có được phép truy cập không
  bool canAccessRoute(String route) {
    final blockedFeatures = _featureBlockingService.blockedFeatures;
    return !BlockableFeatures.isRouteBlocked(route, blockedFeatures);
  }

  /// Xử lý khi route bị chặn
  void handleBlockedRoute(BuildContext context, String route, {String? message}) {
    // Hiển thị dialog thông báo
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return BlockedFeatureDialog(
          route: route,
          message: message ?? _getDefaultBlockedMessage(route),
        );
      },
    );
  }

  /// Lấy thông báo mặc định cho route bị chặn
  String _getDefaultBlockedMessage(String route) {
    final Map<String, String> routeMessages = {
      '/home': 'Chức năng trang chủ hiện đang bị tạm khóa.',
      '/task_list': 'Chức năng danh sách công việc hiện đang bị tạm khóa.',
      '/task_list_screen': 'Chức năng danh sách công việc hiện đang bị tạm khóa.',
      '/page_task': 'Chức năng công việc hiện đang bị tạm khóa.',
      '/my_order': 'Chức năng quản lý đơn hàng hiện đang bị tạm khóa.',
      '/order_detail': 'Chức năng chi tiết đơn hàng hiện đang bị tạm khóa.',
      '/create_order': 'Chức năng tạo đơn hàng hiện đang bị tạm khóa.',
      '/communication': 'Chức năng liên lạc hiện đang bị tạm khóa.',
      '/messages': 'Chức năng tin nhắn hiện đang bị tạm khóa.',
      '/profile': 'Chức năng hồ sơ cá nhân hiện đang bị tạm khóa.',
      '/change_password': 'Chức năng đổi mật khẩu hiện đang bị tạm khóa.',
      '/settings': 'Chức năng cài đặt hiện đang bị tạm khóa.',
      '/reports': 'Chức năng báo cáo hiện đang bị tạm khóa.',
      '/personal_inventory': 'Chức năng kho cá nhân hiện đang bị tạm khóa.',
      '/confirmation_bill_list': 'Chức năng xác nhận hóa đơn hiện đang bị tạm khóa.',
      '/customer_360': 'Chức năng thông tin khách hàng hiện đang bị tạm khóa.',
      '/detail_bill': 'Chức năng chi tiết hóa đơn hiện đang bị tạm khóa.',
      '/create_trans_warehouse': 'Chức năng tạo phiếu xuất kho hiện đang bị tạm khóa.',
    };

    return routeMessages[route] ?? 'Chức năng này hiện đang bị tạm khóa.';
  }

  /// Wrapper cho route builder để kiểm tra quyền truy cập
  Widget guardedRouteBuilder(
    BuildContext context,
    String route,
    WidgetBuilder builder, {
    String? blockedMessage,
  }) {
    // Kiểm tra quyền truy cập
    if (canAccessRoute(route)) {
      return builder(context);
    } else {
      // Nếu bị chặn, hiển thị dialog và return empty widget
      WidgetsBinding.instance.addPostFrameCallback((_) {
        handleBlockedRoute(context, route, message: blockedMessage);
      });
      return Container(); // Return empty container
    }
  }

  /// Kiểm tra và điều hướng route
  Future<bool> checkAndNavigate(
    BuildContext context,
    String route, {
    Object? arguments,
    String? blockedMessage,
  }) async {
    if (canAccessRoute(route)) {
      // Cho phép điều hướng
      Navigator.pushNamed(context, route, arguments: arguments);
      return true;
    } else {
      // Chặn điều hướng và hiển thị thông báo
      handleBlockedRoute(context, route, message: blockedMessage);
      return false;
    }
  }

  /// Kiểm tra và thay thế route
  Future<bool> checkAndPushReplacement(
    BuildContext context,
    String route, {
    Object? arguments,
    String? blockedMessage,
  }) async {
    if (canAccessRoute(route)) {
      // Cho phép điều hướng
      Navigator.pushReplacementNamed(context, route, arguments: arguments);
      return true;
    } else {
      // Chặn điều hướng và hiển thị thông báo
      handleBlockedRoute(context, route, message: blockedMessage);
      return false;
    }
  }

  /// Kiểm tra và điều hướng với xóa tất cả route trước đó
  Future<bool> checkAndPushNamedAndRemoveUntil(
    BuildContext context,
    String route,
    RoutePredicate predicate, {
    Object? arguments,
    String? blockedMessage,
  }) async {
    if (canAccessRoute(route)) {
      // Cho phép điều hướng
      Navigator.pushNamedAndRemoveUntil(context, route, predicate, arguments: arguments);
      return true;
    } else {
      // Chặn điều hướng và hiển thị thông báo
      handleBlockedRoute(context, route, message: blockedMessage);
      return false;
    }
  }

  /// Lấy thông tin debug về trạng thái blocking
  Map<String, dynamic> getDebugInfo() {
    return {
      'featureBlockingService': _featureBlockingService.getBlockingInfo(),
      'canAccessRoutes': _getRouteAccessInfo(),
    };
  }

  /// Lấy thông tin truy cập cho tất cả routes
  Map<String, bool> _getRouteAccessInfo() {
    final Map<String, bool> accessInfo = {};
    
    // Kiểm tra các route chính
    final mainRoutes = [
      '/home',
      '/task_list',
      '/my_order',
      '/communication',
      '/profile',
      '/settings',
    ];

    for (String route in mainRoutes) {
      accessInfo[route] = canAccessRoute(route);
    }

    return accessInfo;
  }

  /// Refresh trạng thái blocking từ service
  Future<void> refreshBlockingState() async {
    await _featureBlockingService.loadFromCache();
  }
}

/// Extension để dễ dàng sử dụng route guard
extension RouteGuardExtension on BuildContext {
  /// Điều hướng với kiểm tra quyền
  Future<bool> guardedPushNamed(
    String route, {
    Object? arguments,
    String? blockedMessage,
  }) {
    return RouteGuardMiddleware.instance.checkAndNavigate(
      this,
      route,
      arguments: arguments,
      blockedMessage: blockedMessage,
    );
  }

  /// Thay thế route với kiểm tra quyền
  Future<bool> guardedPushReplacementNamed(
    String route, {
    Object? arguments,
    String? blockedMessage,
  }) {
    return RouteGuardMiddleware.instance.checkAndPushReplacement(
      this,
      route,
      arguments: arguments,
      blockedMessage: blockedMessage,
    );
  }

  /// Kiểm tra quyền truy cập route
  bool canAccessRoute(String route) {
    return RouteGuardMiddleware.instance.canAccessRoute(route);
  }
}
