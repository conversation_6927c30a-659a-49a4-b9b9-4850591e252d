import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/models/investigation/investigation.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/models/option/option.dart';
import 'package:smart_select/smart_select.dart';

List<Option> gender = [
  Option(label: 'Nam', value: GenreEnum.Male),
  Option(label: 'Nữ', value: GenreEnum.Female),
  Option(label: 'Khác', value: GenreEnum.Other),
];

List<Option> customerNeeds = [
  Option(label: 'Solar Services', value: CatalogTypeQuestion.SolarServices),
  Option(label: 'IT Services', value: CatalogTypeQuestion.ItServices),
  Option(label: 'Viễn thông', value: CatalogTypeQuestion.VienThong),
  Option(label: 'Home Service', value: CatalogTypeQuestion.HomeService),
  Option(label: '<PERSON><PERSON> điện - <PERSON><PERSON><PERSON><PERSON> lạnh - <PERSON><PERSON>n hành tòa nhà', value: CatalogTypeQuestion.CoDien_DienLanh_VanHanhToaNha),
];

List<Option> ageGroup = [
  Option(label: '0-20 tuổi', value: '0-20 tuổi'),
  Option(label: '20-30 tuổi', value: '20-30 tuổi'),
  Option(label: '30-40 tuổi', value: '30-40 tuổi'),
  Option(label: '40- 50 tuổi', value: '40- 50 tuổi'),
  Option(label: 'Trên 50 tuổi', value: 'Trên 50 tuổi'),
];

List<Option> businessModels = [
  Option(label: 'Doanh nghiệp có vốn đầu tư nước ngoài', value: 'Doanh nghiệp có vốn đầu tư nước ngoài'),
  Option(label: 'Bộ/ban ngành', value: 'Bộ/ban ngành'),
  Option(label: 'Doanh nghiệp vừa và nhỏ', value: 'Doanh nghiệp vừa và nhỏ'),
];

List<Option> jobDetail = [
  Option(label: 'Học sinh, sinh viên', value: 'Học sinh, sinh viên'),
  Option(label: 'Nhân viên văn phòng', value: 'Nhân viên văn phòng'),
  Option(label: 'Nội trợ', value: 'Nội trợ'),
  Option(label: 'Công nhân', value: 'Công nhân'),
  Option(label: 'Giám đốc', value: 'Giám đốc'),
  Option(label: 'Khác', value: 'Khác'),
];

// List<Option> businessModel = [
//   Option(label: 'Doanh nghiệp có vốn đầu tư nước ngoài', value: 1),
//   Option(label: 'Bộ/ban ngành', value: 2),
//   Option(label: 'Doanh nghiệp vừa và nhỏ', value: 3),
// ];

List<Option> customerType = [
  Option(
    label: 'Cá nhân',
    value: CustomerType.Personal,
  ),
  Option(
    label: 'Doanh nghiệp',
    value: CustomerType.Business,
  ),
];

List<Option> solutionOption = [
  Option(
    label: 'Đã có phương án',
    value: SolutionOptions.HAS_PLAN,
  ),
  Option(
    label: 'Chưa có phương án',
    value: SolutionOptions.NO_PLAN,
  ),
];

List<Option> saleMemberJob = [
  Option(
    label: 'Ban quản lý, bảo vệ tòa nhà …',
    value: SaleMemberJob.Manager,
  ),
  Option(
    label: 'Công chức, viên chức',
    value: SaleMemberJob.Officer,//
  ),
  Option(
    label: 'Công nhân',
    value: SaleMemberJob.Worker,
  ),
  Option(
    label: 'Công tác XH (hội phụ nữ, thanh niên, trưởng thôn ấp …)',
    value: SaleMemberJob.Society,
  ),
  Option(
    label: 'Cửa hàng (cửa hàng, tạp hóa, quán nước …)',
    value: SaleMemberJob.Store,
  ),
  Option(
    label: 'Cửa hàng VLXD',
    value: SaleMemberJob.ConstructionMaterialsStore,//
  ),
  Option(
    label: 'Kiến trúc sư',
    value: SaleMemberJob.Architect,//
  ),
  Option(
    label: 'Kinh doanh tự do',
    value: SaleMemberJob.FreeBusiness,//
  ),
  Option(
    label: 'NVKD Bất động sản',
    value: SaleMemberJob.RealEstateBusiness,//
  ),
  Option(
    label: 'Nghỉ hưu',
    value: SaleMemberJob.Retire,
  ),
  Option(
    label: 'Người thân CBNV',
    value: SaleMemberJob.RelativesOfOfficialsDndWorkers,//
  ),
  Option(
    label: 'Nhân viên ngân hàng',
    value: SaleMemberJob.BankStaff,//
  ),
  Option(
    label: 'Tư vấn viên',
    value: SaleMemberJob.Counselors,//
  ),
  Option(
    label: 'Khác',
    value: SaleMemberJob.Other,
  ),
];

List<Option> createOrInvite = [
  Option(
    label: 'Tạo nhóm mới',
    value: CreateOrInvite.Create,
  ),
  Option(
    label: 'Tham gia nhóm',
    value: CreateOrInvite.Invite,
  ),
];

List<Option> searchSuppliesByBranch = [
  Option(
    label: 'Tất cả',
    value: SearchSuppliesByBranch.All,
  ),
  Option(
    label: 'Tồn kho Cá nhân',
    value: SearchSuppliesByBranch.PersonalInventory,
  ),
  Option(
    label: 'Tồn kho CNCT',
    value: SearchSuppliesByBranch.TonKhoCNCT,
  ),
];

List<Option> customerType2 = [
  Option(label: 'Cá nhân', value: CustomerType2.B2C),
  Option(
    label: 'Doanh nghiệp',
    value: CustomerType2.B2B,
  ),
];

List<Option> orderTypeCreate = [
  Option(
    label: 'Dịch vụ',
    value: OrderTypeCreate.Normal,
  ),
  Option(
    label: 'Vật tư',
    value: OrderTypeCreate.Supply,
  ),
];

List<Option> supplyType = [
  Option(
    label: 'Không qua kho',
    value: SupplyType.HangKoQuaKho,
  ),
  Option(
    label: 'Qua kho',
    value: SupplyType.HangQuaKho,
  ),
];

List<Option> supplyTypeAll = [
  Option(
    label: 'Không qua kho',
    value: SupplyType.HangKoQuaKho,
  ),
  Option(
    label: 'Qua kho',
    value: SupplyType.HangQuaKho,
  ),
  Option(
    label: 'Vừa qua kho vừa không qua kho',
    value: SupplyType.HangVuaQuaKhoVuaKoQuaKho,
  ),
];

List<Option> reasonCancel = [
  Option(
    label: 'Khách hàng không đồng ý chi phí sửa chữa',
    value: ReasonCancel.Fix,
  ),
  Option(
    label: 'Khách hàng không đồng ý thay thế vật tư',
    value: ReasonCancel.Equipment,
  ),
  Option(
    label: 'Lý do khác',
    value: ReasonCancel.Other,
  ),
];

List<Option> yesNoOption = [
  Option(
    label: 'Có',
    value: YesNoQuestion.Yes,
  ),
  Option(
    label: 'Không',
    value: YesNoQuestion.No,
  ),
];

List<S2Choice> toolTypeChoice = [
  S2Choice(
    title: 'Cá nhân',
    value: PartnerToolType.Personal,
  ),
  S2Choice(
    title: 'Chi nhánh kỹ thuật',
    value: PartnerToolType.Branch,
  ),
  S2Choice(
    title: 'Tổng Công ty',
    value: PartnerToolType.Company,
  ),
];

List<Option> tenantWarrantyOption = [
  Option(
    label: 'Bảo hành',
    value: TenantWarranty.HasWarrantyOrder,
  ),
  Option(
    label: 'Ngoài bảo hành',
    value: TenantWarranty.NotWarrantyOrder,
  ),
];

List<Option> b2bAcceptanceType = [
  Option(
    label: 'Bản cứng',
    value: B2BAcceptanceType.File,
  ),
  Option(
    label: 'Bản điện tử',
    value: B2BAcceptanceType.Digital,
  ),
];

List<Option> purchaseUnitOption = [
  Option(
    label: 'Tỉnh tự mua',
    value: PurchaseUnit.PROVINCE,
  ),
  Option(
    label: 'Tổng công ty mua',
    value: PurchaseUnit.CORP,
  ),
];

List<Option> taxType = [
  Option(
    label: '0%',
    value: '0',
  ),
  Option(
    label: '8%',
    value: '8',
  ),
  Option(
    label: '10%',
    value: '10',
  ),
  Option(
    label: 'Không thuế',
    value: '',
  ),
];

List<Option> paymentType = [
  Option(
    label: 'Tiền mặt',
    value: PaymentType.Cash,
  ),
  Option(
    label: 'Chuyển khoản',
    value: PaymentType.TransferMoney,
  ),
  Option(
    label: 'Tiền mặt/Chuyển khoản',
    value: PaymentType.CashOrTransferMoney,
  ),
];

List<Option> wMSType  = [
  Option(
    label: 'Đơn lẻ',
    value: WMSHSPurpose.NORMAL,
  ),
  Option(
    label: 'Đơn gói',
    value: WMSHSPurpose.PACKAGE,
  ),
];

List<Option> warehouseTypeOption = [
  Option(
    label: 'TCT Nhập',
    value: WarehouseType.TCT_IMPORT,
  ),
  Option(
    label: 'CNCT nhập',
    value: WarehouseType.CNCT_IMPORT,
  ),
];

List<Option> supplyTypeOrderOption = [
  Option(label: Strings.supplyNormal, value: SupplyTypeOrder.Normal),
  Option(
    label: Strings.supplyOEM,
    value: SupplyTypeOrder.OEM,
  ),
];
