// avatar size
const double avatar_small = 25;
const double avatar_middle = 35;
const double avatar_40x = 40;
const double avatar_42x = 42;
const double avatar_medium = 50;
const double avatar_large = 110;

// rating
const MAX_RATING = 5;
const double MIN_RATING = 1;

const SAMPLE_AVATAR = 'https://via.placeholder.com/300';

const RATING_DESCRIPTION_LENGTH = 500;

const PASSWORD_MIN_LENGTH = 6;

const TAX_CODE_LENGTH_MAX = 14;

const TAX_CODE_LENGTH_MIN = 10;

const PHONE_NUMBER_LENGTH = 10;

const PHONE_NUMBER_LENGTH_MIN = 10;

const PHONE_NUMBER_LENGTH_MAX = 11;

const FORGOT_PASSWORD_CODE_LENGTH = 6;
const VERIFICATION_CODE_LENGTH = 6;

const RESEND_CODE_CHANGE_PASS_COUNTDOWN = 300;
const RESEND_CODE_VERIFICATION_COUNTDOWN = 300;

const REFRESH_TOKEN_BEFORE = 60;

const NOTIFICATION_PROMO = 'NOTIFICATION_PROMO';
const NOTIFICATION_JOB = 'NOTIFICATION_JOB';

const TERM_CONDITION = 'WORKER_TERM_CONDITION';
const PRIVACY_POLICY = 'WORKER_PRIVACY_POLICY';
const SUPPORT_PHONE_NUMBER = 'SUPPORT_PHONE_NUMBER';
const SUPPORT_MAIL = 'SUPPORT_MAIL';
const APP_STORE_ID = 'APP_STORE_ID';
const PLAY_STORE_ID = 'PLAY_STORE_ID';
const APPLY_CHECK_OTP = 'APPLY_CHECK_OTP';
const PARTNER_ENABLE_REGISTER = 'PARTNER_ENABLE_REGISTER';
const LIMIT_SERVICE_COMBO = 'LIMIT_SERVICE_COMBO';
const LIMIT_TOTAL_ORDER_COMBO = 'LIMIT_TOTAL_ORDER_COMBO';
const LIMIT_TOTAL_SERVICE_TYPE_COMBO = 'LIMIT_TOTAL_SERVICE_TYPE_COMBO';
const CONTRACT_HIRE_OUTSIDE_WORKER = 'CONTRACT_HIRE_OUTSIDE_WORKER';
const PREFIX_SERVICE_DECIMAL_QUANTITY = 'PREFIX_SERVICE_DECIMAL_QUANTITY';
const OPEN_POPUP_TIME_KEEP = 'OPEN_POPUP_TIME_KEEP';

const DEFAULT_PAGE_SIZE = 50;
const DEFAULT_PAGE_INDEX = 0;
const DEFAULT_PAGE_FIRST = 1;
const DEFAULT_PAGE_SIZE_SELECT = 20;
const DEFAULT_PAGE_SIZE_CUSTOMER_UNFINISHED = 5;

const PAGE_SIZE_AREA = 70;
const SIZE_ICON_20 = 20.0;

const PHONE_CONTACT = '+84';

const MIN_ITEM_QUANTITY = 0;
const MAX_ITEM_QUANTITY = 10;

const ID_LENGTH = [9, 12];

// TIME REPEAT GET ORDER NOTIFICATION
const GET_ORDER_REPEAT_TIME = 15;

const MAX_REQUEST_BUDGET_PRICE = 100000000;

const MINIMUM_B2B_ORDER_TOTAL_REQUIRED_CONTRACT = 20000000;

const SCHEDULER_SERVICE_CODE = "DV_HEN_KHAM";