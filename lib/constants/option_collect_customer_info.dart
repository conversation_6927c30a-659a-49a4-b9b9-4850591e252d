import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/models/option/option.dart';
import 'package:smart_select/smart_select.dart';

List<String> geographicalArea = [
  'Đồng bằng',
  'Trung du',
  '<PERSON><PERSON><PERSON> núi',
  '<PERSON><PERSON> biển',
  '<PERSON><PERSON><PERSON> đảo',
];

List<String> jobCustomer = [
  '<PERSON><PERSON><PERSON> vực An ninh quốc phòng',
  '<PERSON><PERSON><PERSON> vực Tà<PERSON> chính',
  '<PERSON><PERSON><PERSON> vực <PERSON>ộ<PERSON>, Thương Binh và Xã hội',
  '<PERSON><PERSON><PERSON> vực Xây dựng',
  '<PERSON><PERSON><PERSON> vực Giáo dục và Đào tạo',
  '<PERSON><PERSON><PERSON> vựcY tế',
  '<PERSON><PERSON><PERSON> vực Văn hóa, <PERSON>h<PERSON> thao và Du lịch',
  '<PERSON><PERSON><PERSON> vực Ngân hàng',
  '<PERSON><PERSON><PERSON> vực Tư pháp',
  '<PERSON><PERSON><PERSON> vực Công Thương',
  '<PERSON><PERSON><PERSON> vực Giao thông vận tải',
  '<PERSON><PERSON><PERSON> vực Thông tin và Truyền thông',
  '<PERSON><PERSON><PERSON> vự<PERSON> Nông nghiệp và Phát triển nông thôn',
  '<PERSON><PERSON><PERSON> vực Khoa học và Công nghệ',
  'Lĩnh vực Tài nguyên và Môi trường',
];

List<Option> tagetCustomer = [
  Option(label: 'Khách hàng ngoại giao', value: TagetCustomerEnum.KhachHangNgoaiGiao),
  Option(label: 'Khách hàng thường', value: TagetCustomerEnum.KhachHangThuong),
];

List<String> enterpriseSize = [
  'Dưới 100 người',
  'Từ 100 đến 500 người',
  'Từ 500 đến 1000 người',
  'Trên 1000 người',
];

List<String> businessPosition = [
  'Tổng Giám đốc',
  'Phó Tổng GĐ',
  'Giám đốc',
];

List<String> businessPositionContact = [
  'Trưởng phòng/ Phó phòng',
  'Nhân viên/ Chuyên viên',
  'Khác',
];

List<String> positionB2C = [
  'Ban TGĐ Tổng công ty',
  'Giám đốc/ Trưởng Bộ Ban Ngành',
];
