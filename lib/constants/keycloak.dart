import 'package:home_care_partner/constants/environments.dart';
import 'package:home_care_partner/constants/error.dart';

class KeycloakUrl {
  KeycloakUrl._();

  static final String authUrl =
      '${Environments.kcBaseUrl}/realms/${Environments.kcRealm}/protocol/openid-connect/token';

  static final String changePasswordUrl =
      '${Environments.kcBaseUrl}/realms/${Environments.kcRealm}/account/credentials/password';
}

class KeycloakStatusCode {
  static const Map<int, String> clientSideErrorCode = const <int, String>{
    400: ErrorString.kcError400,
    401: ErrorString.kcError401,
    403: ErrorString.kcError403,
    404: ErrorString.kcError404,
    405: ErrorString.kcError405,
    409: ErrorString.kcError409,
    415: ErrorString.kcError415,
  };

  static const Map<String, String> clientSideErrorMessage =
      const <String, String>{
    'emailExistsMessage': ErrorString.kcEmailExistsMessage,
    'invalidPasswordMessage': ErrorString.kcInvalidPasswordMessage,
    'invalidPasswordConfirmMessage':
        ErrorString.kcInvalidPasswordConfirmMessage,
    'invalidPasswordExistingMessage':
        ErrorString.kcInvalidPasswordExistingMessage,
    'invalidUsernameMessage': ErrorString.kcInvalidUsernameMessage,
    'invalidUsernameOrEmailMessage':
        ErrorString.kcInvalidUsernameOrEmailMessage,
    'invalidPasswordMinLengthMessage':
        ErrorString.kcInvalidPasswordMinLengthMessage,
  };
}
