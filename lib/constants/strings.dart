import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:home_care_partner/extensions/enum.dart';
import 'package:home_care_partner/vendors/smart_select/smart_select.dart';

import 'colors.dart';
import 'enum.dart';

class Strings {
  Strings._();

  //General
  static const String appName = "Home Services Worker";

  static final String hashFieldSecretKey = env['HASH_FIELD_SECRET_KEY'];

  // Status
  static const String statusPending = 'Đang chờ';
  static const String statusWaiting = 'Chờ thực hiện';
  static const String statusMoving = 'Đang di chuyển';
  static const String statusArrived = 'Đã đến nơi làm việc';
  static const String statusInvestigating = 'Đang khảo sát';
  static const String statusWaitInvestigationConfirm = 'Chờ xác nhận khảo sát';
  static const String statusInvestigationConfirmed = 'Đã xác nhận khảo sát';
  static const String statusProcessing = 'Đang thực hiện';
  static const String statusWaitProcessingConfirm = 'Chờ xác nhận hoàn thành';
  static const String statusProcessConfirmed = 'Đã xác nhận hoàn thành';
  static const String statusWaitProcessingEditConfirm = 'Chờ xác nhận thay đổi';
  static const String statusWaitingApprove = 'Đang chờ xác nhận';
  static const String statusComplete = 'Hoàn thành';
  static const String statusCancel = 'Đã huỷ';
  static const String statusCompleted = 'Đã hoàn thành';
  static const String statusWaitingInvestigate = 'Chờ khảo sát';
  static const String statusWaitingPayment = 'Chờ thanh toán';
  static const String statusRequesting = 'Yêu cầu điều phối';
  static const String statusWaitingPerform = 'Chờ thực hiện';
  static const String statusProcessedPerform = 'Đã thực hiện';
  static const String statusProcessingPerform = 'Đang thực hiện';
  static const String statusCancelled = 'Đã hủy';
  static const String waiting = 'Chờ';
  static const String statusRequestingForProcess = 'Điều phối thực hiện';
  static const String statusUnProcessingPerform = 'Chưa thực hiện';
  static const String statusUnApprovalPerform = 'Chờ tiếp nhận';
  static const String all = 'Tất cả';
  static const String doNotSave = 'Không lưu';
  static const String save = 'Lưu';

  // Processing Status
  static const String statusSubscribed = 'Đã đăng ký';
  static const String statusWaitingForInvestigateWithPartner = 'Chờ khảo sát';
  static const String processingStatusMoving = 'Thợ đang di chuyển';
  static const String statusWaitingForInvestigate = 'Điều phối khảo sát';
  static const String statusProcessingInvestigating = 'Đang khảo sát';
  static const String statusCompletedForInvestigate = 'Đã khảo sát';
  static const String statusWaitingMaintance = 'Chờ thực hiện';
  static const String statusWaitingForMaintance = 'Yêu cầu điều phối';
  static const String statusProcessingMaintance = 'Đang thực hiện';
  static const String statusCompletedForMaintance = 'Đã thực hiện';

  // Button
  static const String buttonPending = 'Bắt đầu di chuyển';
  static const String buttonManualAssign = 'Trả đơn hàng';
  static const String buttonMoving = 'đến nơi làm việc';
  static const String buttonArrived = 'bắt đầu khảo sát';
  static const String buttonInvestigating = 'xác nhận khảo sát';
  static const String buttonWaitInvestigating = 'Chờ xác nhận khảo sát';
  static const String buttonStart = 'Bắt đầu';
  static const String buttonEdit = 'Sửa';
  static const String buttonComplete = 'Hoàn thành';
  static const String buttonPayment = 'Thanh toán';
  static const String buttonWaitProcessingConfirm = 'XÁC NHẬN ĐÃ Thanh toán';
  static const String buttonProcessingConfirmed = 'Xác nhận đã Thanh toán';
  static const String buttonRatingCustomer = 'đánh giá khách hàng';
  static const String buttonSignatureAcceptance = 'Ký nghiệm thu';
  static const String buttonSignatureContract = 'Ký hợp đồng';

  // Label
  static const String labelCalendar = 'Lịch';
  static const String labelTaskList = 'Đơn triển khai';
  static const String labelSellList = 'Đơn bán hàng';
  static const String labelComplain = 'Khiếu nại - Phản ánh';
  static const String labelAssignment = 'Điều phối';
  static const String labelInvestigateList = 'Khảo sát KH';
  static const String labelGetInvestigateList = 'Nhận khảo sát KH';
  static const String labelMission = 'Nhiệm vụ';
  static const String labelChat = 'Trò chuyện';
  static const String labelNotification = 'Thông báo';
  static const String labelConfirmCommission = 'Xác nhận hoa hồng';
  static const String labelSalePointBuy = 'Phiếu mua hàng';
  static const String labelSalePointOrder = 'Phiếu bán hàng';
  static const String labelEmployeeInvestigate = 'Khảo sát nhân viên';
  static const String labelReceivedInvestigate = 'Nhận KS khách hàng';
  static const String deliveryBillManagement = 'Phiếu xuất kho';
  static const String debt = 'Thống kê công nợ';
  static const String buyTicket = 'Phiếu mua hàng';
  static const String labelComplained = 'Khiếu nại Phản ánh';
  static const String labelSaleShip = 'Phiếu bán hàng';

  static const String labelDebit = 'Nộp tiền';
  static const String labelCredit = 'Nhận tiền';

  static const String labelSalary = 'Lương';
  static const String labelOrder = 'Đơn hàng';
  static const String labelRevenue = 'Doanh thu';
  static const String labelBonus = 'Hoa hồng';

  static const String labelDebt = 'Công nợ';
  static const String labelBill = 'Hóa đơn';
  static const String labelBillViaViettelPay = 'Đơn\nchuyển khoản';
  static const String labelBillNotViaViettelPay  = 'Đơn\nqua CNCT';

  static const String tabLabelBranch = 'CNKT';
  static const String tabLabelCompany = 'TCT';
  static const String tabLabelPersonal = 'Cá nhân';
  static const String tabLabelBusiness = 'Doanh nghiệp';

  static const String deploymentTimeRange = 'Khoảng thời gian hẹn lịch';
  static const String labelPeriodTime= 'Khoảng thời gian triển khai';

  // shipping order
  static const String shippingOrder= 'Đơn giao vận';


  // transfer partner tool
  static const String tabReceivingTransfer = 'Điều chuyển đến';
  static const String tabTransferHistory = 'Điều chuyển đi';

  static const String partnerToolTransferStatusAccept = 'Xác nhận điều chuyển';
  static const String partnerToolTransferStatusPending = 'Đang chờ';
  static const String partnerToolTransferStatusReject = 'Từ chối';

  // order status
  static const String orderRequesting = 'Đang yêu cầu';
  static const String orderPending = 'Đang chờ';
  static const String orderProcessing = 'Đang xử lý';
  static const String orderCompleted = 'Hoàn thành';
  static const String orderCancel = 'Đã hủy';
  static const String orderClose = 'Đã đóng';

  // order type
  static const String orderNormal = 'Đơn lẻ - Chuyển khoản';
  static const String orderPackage = 'Đơn hàng gói theo năm';
  static const String orderMaintance = 'Đơn hàng hỗ trợ kỹ thuật';
  static const String orderMaintanceB2B = 'Đơn hàng hỗ trợ kỹ thuật - Thanh toán qua CNCT';
  static const String orderMaintanceNotB2B = 'Đơn hàng hỗ trợ kỹ thuật - Chuyển khoản';
  static const String orderPartner = 'Đơn đối tác - Chuyển khoản';
  static const String orderB2BPartner = 'Đơn đối tác - Thanh toán qua CNCT';
  static const String orderSupply = 'Đơn vật tư - Chuyển khoản';
  static const String orderSupplyB2B = 'Đơn vật tư - Thanh toán qua CNCT';
  static const String orderB2B = 'Đơn lẻ - Thanh toán qua CNCT';
  static const String orderCombo = 'Đơn Combo - Chuyển khoản';
  static const String orderB2BCombo = 'Đơn Combo - Thanh toán qua CNCT';
  static const String orderPackage1 = 'Đơn hàng gói - Thanh toán qua CNCT';
  static const String orderPackage2 = 'Đơn hàng gói - Chuyển khoản';
  static const String orderSalePoint = 'Đơn điểm bán - Chuyển khoản';
  static const String orderSalePointCombo = 'Gói điểm bán combo - Chuyển khoản';

  // source type
  static const String sourceAdmin = 'Tạo bởi quản trị viên';
  static const String sourceApp = 'Tạo bởi khách hàng';
  static const String sourceAppPartner = 'Tạo bởi cộng tác viên';

  // empty data
  static const String emptyTaskList = 'Không có công việc nào sắp tới';
  static const String emptyStatistic =
      'Không có doanh thu nào trong 7 ngày gần đây';
  static const String emptyChat = 'Không có tin nhắn';
  static const String emptyNotification = 'Không có thông báo';
  static const String emptyOrder = 'Không tìm thấy đơn hàng nào';
  static const String emptyTask = 'Không tìm thấy nhiệm vụ nào';
  static const String emptyComplain =
      'Không tìm thấy ticket khiếu nại phản ánh nào';
  static const String emptyOrderV2 = 'Không tìm thấy đơn hàng nào';
  static const String emptySoldOrder =
      'Không tìm thấy đơn hàng bán thành công nào';
  static const String emptyEditOrder =
      'Không tìm thấy đơn hàng thực hiện thành công nào';
  static const String emptyInvestigation = 'Không có khảo sát';
  static const String emptyProposedPurchase = 'Không tìm thấy phiếu yêu cầu';
  static const String emptySupplyPurchase = 'Vui lòng chọn vật tư';
  static const String emptyInvoice = 'Không tìm thấy hóa đơn';
  static const String emptyCalendar = 'Không tìm thấy công việc nào trong ngày';
  static const String emptyProfile = 'Không lấy được dữ liệu người dùng';
  static const String emptySurvey =
      'Yêu cầu khảo sát, đợt chiến dịch chưa được cấu hình form khảo sát. Vui lòng liên hệ chăm sóc khách hàng!';
  static const String emptyOrderDetail =
      'Đơn hàng không tồn tại hoặc đã bị xoá';
  static const String emptyCheckList = 'Không tìm thấy tiêu chí nào';
  static const String emptyComplainDetail =
      'Khiếu nại phản ánh không tồn tại hoặc đã bị xoá';
  static const String emptyStockTrans = 'Không có phiếu nào';
  static const String emptySupplyStockTrans = 'Không có vật tư nào trong phiếu';
  static const String emptyServicesSearchText =
      'Xin lỗi, từ khóa bạn tìm kiếm hiện không tìm thấy. Vui lòng kiểm tra lại hoặc tìm kiếm với từ khóa khác.';

  static const String emptyInvestigationDetail =
      'Phiếu khảo sát không tồn tại hoặc đã bị xoá';
  static const String emptyExplanationDetail =
      'Phiếu chốt phạt không tồn tại hoặc đã bị xoá';
  static const String afterPay = 'Hoá đơn thường';
  static const String beforePay = 'Hoá đơn gói';

  // investigation
  static const String investigationStatusWaiting = 'Đang chờ';
  static const String investigationStatusRequesting = 'Đang yêu cầu';
  static const String investigationStatusProcessing = 'Đang thực hiện';
  static const String investigationStatusCompleted = 'Đã hoàn thành';
  static const String investigationStatusCanceled = 'Đã huỷ';
  static const String investigationStatusClose = 'Đã đóng';
  static const String buttonCreateContact = 'Tạo lần tiếp xúc';
  static const String buttonCreateNormalOrder = 'Tạo đơn dịch vụ lẻ';
  static const String buttonCreatePackageOrder = 'Tạo đơn hàng gói';
  static const String buttonCreatePackageYear = 'Tạo đơn gói có khảo sát';
  static const String buttonCreatePackageWithoutInvestigation = 'Tạo đơn gói không khảo sát';
  static const String buttonCreateDevice = 'Tạo đơn thiết bị';
  static const String buttonRouteContactRequired = 'Chuyển sang YCTX';
  static const String buttonCreateComboOrder = 'Tạo đơn hàng B2B';
  static const String buttonCloseInvestigation = 'Khảo sát khách hàng';
  static const String buttonCancelInvestigation = 'Huỷ phiếu khảo sát';
  static const String buttonReceiveRequestInvestigation = 'Nhận yêu cầu';
  static const String buttonCreateComboServiceOrder = 'Tạo đơn combo';
  static const String buttonCreateVisitOrder = 'Tạo đơn thăm khám';
  static const String buttonCreateSuppliesOrder = 'Tạo đơn vật tư';
  static const String investigationStatusWaitingDispatch = 'Chờ điều phối';
  static const String surveyWorkerCapacity = 'Khảo sát nhân viên';

  static const String unconfimred = 'Chưa xác nhận';
  static const String unreleased = 'Chưa phát hành';
  static const String published = 'Đã phát hành';
  static const String refuseRelease = 'Từ chối phát hành';
  static const String outOfTermApproval = 'Duyệt ngoài kỳ';

  ///-----quocht5 update label start
  // Order status
  static const String orderLabelRequesting = 'Yêu cầu điều phối';
  static const String orderLabelWaiting = 'Chờ thực hiện';
  static const String orderLabelProcessing = 'Đang thực hiện';
  static const String orderLabelCompleted = 'Hoàn thành';
  static const String orderLabelProcessed = 'Đã thực hiện';
  static const String orderLabelCancel = 'Hủy';
  static const String approved = 'Đã duyệt';
  static const String pending = 'Chờ duyệt';
  static const String refused = 'Đã từ chối';
  static const String awaitingCensorship = 'Chờ kiểm duyệt';

  // shipping order status
  static const String orderWaitingForReception = 'Chờ tiếp nhận';
  static const String orderWaitingForDelivery = 'Chờ giao hàng';
  static const String orderLabelDelivering = 'Đang giao hàng';
  static const String orderLabelDelivered = 'Đã giao hàng';
  static const String orderWaitForPayment = 'Chờ thu tiền';
  static const String orderLabelRefund = 'Hoàn hàng';


  // Processing Status
  static const String processingStatusLabelWaiting = 'Chờ';
  static const String processingStatusLabelWaitingProcessing = 'Chờ thực hiện';
  static const String processingStatusLabelWaitingSurvey = 'Chờ khảo sát';
  static const String processingStatusLabelProcessingSurvey = 'Đang khảo sát';
  static const String processingStatusLabelCompletedSurvey = 'Đã khảo sát';
  static const String processingStatusLabelCoordinatorSurvey =
      'Điều phối khảo sát';
  static const String processingStatusLabelCoordinatorProcessing =
      'Điều phối thực hiện';
  static const String processingStatusLabelProcessing = 'Đang thực hiện';
  static const String processingStatusLabelProcessingConfirmed = 'Đã thực hiện';
  static const String processingStatusLabelCompleted = 'Hoàn thành';
  static const String processingStatusLabelCancelled = 'Hủy';
  static const String emptyServices = 'Không tìm thấy dịch vụ nào';
  static const String emptySupply = 'Không tìm thấy vật tư nào';
  static const String close = 'Đóng';
  static const String appointWorker = 'Chỉ định thợ';

  static const String notExplained = 'Chưa giải trình';
  static const String Explained = 'Đã giải trình';
  static const String closeExplained = 'Phê duyệt giải trình';
  static const String declinedExplained = 'Từ chối giải trình';
  static const String explanation = 'Giải trình';
  static const String sell = 'Bán hàng';
  static const String contactInvestigation = 'Tiếp xúc - Khảo sát';
  static const String deployment = 'Triển khai';
  static const String explanation_report = 'Báo phạt - Giải trình';
  static const String saveInvestigation = 'Lưu khảo sát';
  static const String closeRequestInvestigation = 'Đóng yêu cầu khảo sát';

  static const String NOT_EXPLAINED = 'NOT_EXPLAINED';
  static const String EXPLAINED = 'EXPLAINED';
  static const String CLOSED_EXPLANATION = 'CLOSED_EXPLANATION';
  static const String DECLINED_EXPLANATION = 'DECLINED_EXPLANATION';
  static const String APP = 'APP';
  static const String EXPLANATION_HISTORY = 'Lịch sử giải trình';
  static const String emptyExplanationHistory = 'Lịch sử giải trình không tồn tại';
  static const String createPunishment = 'Tạo chốt phạt';
  static const String sendExplanation = 'Tạo chốt phạt';
  static const String explanationOnAdmin = 'Giải trình trên Admin';
  static const String approvedExplanation = 'Phê duyệt giải trình';
  static const String autoCloseExplanation = 'Tự động đóng giải trình';
  static const String userExplanation = 'Chốt phạt nhân sự';
  static const String employeeExplanation = 'Nhân sự đề xuất bị phạt';
  static const String otherEmployeeExplanation = 'Nhân sự khác';
  static const String reason = 'Lý do lựa chọn';
  static const String reasonRequired = 'Vui lòng nhập lý do lựa chọn';
  static const String inputReason = 'Nhập lý do lựa chọn ...';
  static const String reasonExplanation = 'Lý do giải trình';
  static const String inputReasonExplanation = 'Nhập lý do giải trình ...';
  static const String management = "Quản lý";
  static const String otherStaffExplanation = "Nhân viên vi phạm khác";
  static const String reasonOverGroup = "Nguyên nhân tổng quan";
  static const String selectedEmployee = "Vui lòng chọn nhân sự chốt phạt";
  static const String selectedOtherEmployee = "Vui lòng chọn nhân sự chốt phạt khác";
  static const String completedExplanation = "Chốt phạt thành công";
  static const String successfulExplanation = "Giải trình thành công";
  static const String btnSignAgreeCustomerTicker = 'Đồng ý';
  static const String titleSignAgreeCustomerTicker = 'Bạn có muốn ký BBLV điện tử không?';



  ///-----quocht5 update label end

  static const String router_by_commission_order = 'COMMISSION_ORDER';

  /// ----tanlv update label package without notsurvey
  static const String packageSurveyService = 'Dịch vụ gói có khảo sát';
  static const String packageNotSurveyService = 'Dịch vụ gói không khảo sát';
  static const String packageOrderNotSurvey = 'Đơn gói không khảo sát';

  ///------tanlv bo sun label yeu cau khao sat
  static const String newCustomerSurveyCollected = 'Thu thập thông tin khách hàng mới';

  static const String encryptPhoneNumber = 'SĐT mã hóa bảo mật thông tin KH';

  static const String ERAUTH = '#ERAUTH';

  /// deepLink Data
  static String contactRequestCode;
  static String orderType;
  static String address;
  static String areaId;
  static String wardId;
  static String provinceId;
  static String contactPhoneNumber;
  static String contactName;
  static String isDeepLink = BooleanType.False.getEnumValue();
  static String employeeCode;
  static String module;
  static String id;
  static String ticketCode;
  static String errAccountFromAio = "Tài khoản không trùng nhau. Vui lòng kiểm tra lại";
  static String requiredAccountFromAio = "Yêu cầu bạn nhập đúng thông tin tài khoản trên hệ thống AIO để tạo đơn hàng";
  static String requiredAccountFromTicket = "Bạn vui lòng nhập thông tin tài khoản thực hiện ticket";

  static const String distanceGoogle = 'DISTANCE_GOOGLE';
  static const String distanceInput = 'DISTANCE_INPUT';
  static const String assignOFT3GK = "Chuyển việc OFT3-GK";

  static const String requestChangeNote = "Ghi chú đề xuất thay đổi";
  static const String requestChangeReason = "Lý do đề xuất thay đổi";
  static const String noCollectMoneyFromCustomer = "Không thu tiền khách hàng";
  static const String calendarAppointment = 'Hẹn lịch làm việc trong 3 giờ:';
  static const String disagree = 'Không';
  static const String agree = 'Có';
  static const String notifyOrderCoupon =
      'Đơn hàng hiện tại đang được áp dụng Mã giảm giá, đổi loại đơn sẽ mất Mã giảm giá này. Hành động không thể hoàn tác, bạn xác nhận thay đổi';
  static const String confirm = 'Xác nhận';
  static const String cancel = 'Hủy';
  static const String change = 'Thay đổi';
  static const String labelFastWorkTime= "Thời gian hẹn lịch nóng gấp";
  static const String emptyOrderJourney = 'Hiện chưa có lịch sử hàng hóa';
  static const String returnOrder = 'Đề xuất trả đơn';
  static const String noCollect = 'Không thu';
  static const String passive = 'Thu hộ';
  static const String directive = 'Thu thật';
  static const String total = 'Tổng cộng';
  static const String propose = 'Đề xuất';
  static const String update = 'Cập nhật';
  static const String proposeCompleted = 'Đề xuất thay đổi thành công';
  static const String reasonCancelTitle = 'Lý do đề xuất huỷ đơn hàng';
  static const String noteReasonCancelTitle = 'Ghi chú lý do đề xuất huỷ đơn hàng';
  static const String searchReasonCancelTitle = 'Tìm kiếm lý do đề xuất huỷ';
  static const String requestCanCelOrderComplete = 'Đề xuất huỷ đơn hàng thành công!';
  static const String removeCanCelOrderComplete = 'Hủy đề xuất huỷ đơn hàng thành công!';
  static const String requiredReason = 'Vui lòng chọn lý do';
  static const String imageDevice = 'Hình ảnh thiết bị';
  static const String requiredMaxTwoImage = 'Vui lòng chụp hình ảnh thiết bị(Tối đa 2 ảnh)';
  static const String requiredImage = 'Vui lòng chụp hình ảnh thiết bị';
  static const String removeCancelOrder = 'Hủy đề xuất hủy';
  static const String removeCancelOrderProposal = 'Hủy đề xuất hủy';
  static const String contentRemoveOrderCancel = 'Bạn có chắc chắn hủy đề xuất hủy?';
  static const String contentRemoveOrderProposalCancel = 'Bạn có chắc chắn hủy đề xuất hủy?';
  static const String timeSchedulerOrder = 'Thời gian hẹn lịch';
  static const String titleCancelOrderBy = 'Đơn hàng bị huỷ vì lý do';
  static const String belongsToTheReasonGroup = 'Thuộc nhóm lý do';
  static const String customer = 'Khách hàng';
  static const String yes = "có";
  static const String no = "không";
  static const String payToCheck = "thanh toán tiền kiểm tra";
  static const String extendPay = "thanh toán thêm chi phí khác";
  static const String systemProcessing = "Hệ thống đang xử lý. Vui lòng thử lại sau!";
  static const String inputOrderCode = "Nhập mã đơn hàng";
  static const String orderCode = "Mã đơn hàng";
  static const String note = "Ghi chú";
  static const String authCancelNotify = "Tất cả các trường hợp đề xuất hủy đều được CSKH xác minh, nếu bạn chọn sai lý do, bạn sẽ bị phạt 100.000đ";
  static const String warningValidateOrder = "Nếu lý do hủy đơn là do tạo sai đơn, bạn cần tạo lại đơn đúng trước khi đề xuất hủy. Bạn vui lòng tạo lại đơn đúng và đề xuất hủy sau.";
  static const String notificationCheckOrder =
      'Bạn vui lòng chọn mã đơn hàng đúng. Nếu chọn sai P.CSKH phát hiện sẽ bị phạt 100.000đ';
  static const String supplyNormal = 'Vật tư thường';
  static const String addSupplyItemString = 'Thêm vật tư';
  static const String nameOrCodeSupplyOEM = 'Tên/mã vật tư';

  static const String supplyOEM = 'Vật tư OEM';
  static const String nameSupplyOEM = 'Tên';
  static const String codeSupplyOEM = 'Mã';
  static const String costLabelOEM = 'Giá bán';
  static const String rootPrice = 'Giá vốn';
  static const String inputPrice = 'Nhập giá';
  static const String getSupplies = 'Xin vật tư';
  static const String requiredOEM = 'Vui lòng chọn ít nhất 1 vật tư OEM';
  static const String INVESTIGATION = 'INVESTIGATION';
  static const String TICKET = 'TICKET';
  static const String work = "Công việc";
  static const String result = "Kết quả";
  static const String infoContract = "Thông tin biên bản";
  static const String addWork = "Thêm công việc";
  static const String atLeastAWork = "Phải có 1 công việc được thực hiện";
  static const String contentProcess = "Nội dung xử lý";
  static const String requiredWork = "Bắt buộc nhập đầy đủ nội dung xử lý công việc";
  static const String signCompleted = "Hoàn thành ký";
  static const String updated = 'Cập nhật thành công';
  static const String workPdf = 'Biên bản làm việc';
  static const String signPdf = 'Biên bản nghiệm thu';
  static const String numberExtension = 'Đã gia hạn: ';
  static const String unitExtension = 'lần';
  static const String orderWaitingScheduleReason = 'Đề xuất thay đổi lịch hẹn';
  static const String waitingApproval = 'Đang duyệt';
  static const String cancelSchedule = 'Hủy hẹn lịch';
  static const String quantityScheduled = 'Đề xuất hẹn lịch đã duyệt: ';
  static const String contentCompleteOrderProposalCancel = 'Bạn có chắc chắn hủy gia hạn';
}

class ScreenName {
  ScreenName._();

  static const String homePage = 'Trang chủ';
  static const String taskList = 'Công việc';
  static const String communication = 'Tin nhắn';
  static const String profile = 'Cá nhân';
  static const String documentManagement = 'Quản lý hồ sơ';
  static const String deliveryBillManagement = 'Phiếu xuất kho';
  static const String catalogManagement = 'Ngành nghề';
  static const String abilityManagement = 'Năng lực làm việc';
  static const String toolManagement = 'Trang thiết bị';
  static const String listRequestTransferPartnerTool =
      'Danh sách công cụ điều chuyển';
  static const String certificateManagement = 'Hồ sơ, chứng chỉ';
  static const String transaction = 'Lịch sử giao dịch';
  static const String proposed_purchase = 'Đề nghị mua hàng';
  static const String proposed_purchase_create = 'Tạo đề nghị mua hàng';
  static const String proposed_purchase_detail = 'Đề nghị mua hàng';
  static const String statistic = 'Thống kê doanh thu';
  static const String debt = 'Thống kê công nợ';
  static const String saleGroup = 'Nhóm bán hàng';
  static const String detailSaleGroup = 'Chi tiết nhóm bán hàng';
  static const String censorship = 'Kiểm duyệt';
  static const String invoice = 'Hóa đơn';
  static const String configAccount = 'Cấu hình tài khoản';
  static const String privacy = 'Cài đặt quyền';
  static const String notificationSetting = 'Cài đặt thông báo';
  static const String changePassword = 'Đổi mật khẩu';
  static const String deleteAccount = 'Xóa tài khoản';
  static const String logout = 'Đăng xuất';
  static const String addPackageService = 'Thêm dịch vụ gói';

  static const String help = 'Hỗ trợ';
  static const String passwordPolicy = 'Chính sách bảo mật';
  static const String term = 'Điều khoản dịch vụ';
  static const String ratingApp = 'Đánh giá';
  static const String sendMail = 'Gửi thông tin phản hồi';
  static const String supportCenter = 'Liên hệ tổng đài hỗ trợ';
  static const String reportSystemError = 'Báo lỗi hệ thống';
  static const String contributeIdea = 'Đóng góp ý kiến';
  static const String questions = 'Hướng dẫn xử lý lỗi';
  static const String timeAttendanceManagement = 'Quản lý chấm công';
  static const String listEmployees = 'Danh sách nhân viên';
  static const String errorHandlingInstructions = 'Hướng dẫn xử lý lỗi';

  // order
  static const String orderDetail = 'Thông tin đơn hàng';
  static const String ratingCustomer = 'Đánh giá khách hàng';
  static const String addOrderItem = 'Thêm dịch vụ';
  static const String addSupplyItem = 'Thêm vật tư';
  static const String addSellerOrder = 'Thêm người bán';
  static const String addProfileTool = 'Thêm thiết bị';
  static const String createRequestTransferTool = 'Điều chuyển cho thợ';
  static const String listRequestTransferTool = 'Danh sách công cụ điều chuyển';
  static const String viettelPay = 'Thanh toán chuyển khoản';
  static const String editedOrder = 'Đơn hàng thực hiện';
  static const String soldOrder = 'Đơn hàng đã bán';
  static const String autoCoordination = 'Điều phối tự động';
  static const String switchAcceptJob = 'Tạm dừng nhận việc';
  static const String assignOFT = 'Giao việc';
  static const String assignFT3 = /*'Chuyển việc FT3'*/'Chuyển việc';
  static const String assignFT3Mng = 'Chuyển việc QL FT3';
  static const String assignCTVAgency = 'Chuyển việc QTV đại lý';
  static const String assignCTVAgencyMng = 'Chuyển việc quản lý QTV đại lý';
  static const String assignBoft3 = 'Trả việc';
  static const String request_budget = 'Danh sách yêu cầu ngân sách';
  static const String collectCustomerInfo = 'Thu thập thông tin khách hàng';

  // auth
  static const String forgotPassword = 'Quên mật khẩu';
  static const String verification = 'Xác nhận số điện thoại';

  // delivery bill
  static const String confirmation_bill = 'Phiếu xác nhận';
  static const String my_delivery_bill = 'Phiếu xuất tôi tạo';
  static const String personal_inventory = 'Tồn kho cá nhân';
  static const String sell_punish_export = 'Xuất bán phạt';
  static const String new_dev_warehouse_export = 'Xuất kho phát triển mới';
  static const String transfer_staff_warehouse_export =
      'Xuất điều chuyển kho nhân viên';
  static const String branch_warehouse_export = 'Xuất kho trả hàng chi nhánh';
  static const String detailNotification = 'Chi tiết thông báo';
  ///TODO giải trình
  static const String explanation = 'Giải trình';
  static const String explanation_detail = 'Chi tiết giải trình';
  static const String explanation_history = 'Lịch sử giải trình';
  static const String APP = 'APP';
  static const String deploymentTimeRange = 'Khoảng thời gian hẹn lịch';
  static const String keyMessageSchedule = '#ERR_UPDATE_SCHEDULE_01';
}

const Map<OrderStatusEnum, String> orderStatusLabel =
    const <OrderStatusEnum, String>{
  OrderStatusEnum.Requesting: Strings.orderPending,
  OrderStatusEnum.Waiting: Strings.orderPending,
  OrderStatusEnum.Processing: Strings.orderProcessing,
  OrderStatusEnum.Completed: Strings.orderCompleted,
  OrderStatusEnum.Cancelled: Strings.orderCancel,
};

const Map<InvestigationStatusEnum, String> investigationStatusLabel =
    const <InvestigationStatusEnum, String>{
  InvestigationStatusEnum.Requesting: Strings.investigationStatusRequesting,
  InvestigationStatusEnum.Waiting: Strings.investigationStatusWaitingDispatch,
  InvestigationStatusEnum.Processing: Strings.investigationStatusProcessing,
  InvestigationStatusEnum.Completed: Strings.investigationStatusCompleted,
  InvestigationStatusEnum.Cancelled: Strings.investigationStatusCanceled,
  InvestigationStatusEnum.Close: Strings.investigationStatusClose,
};

const Map<StatusElectronicBill, String> invoiceStatusLabel =
    const <StatusElectronicBill, String>{
  StatusElectronicBill.UnConFimRed: Strings.unconfimred,
  StatusElectronicBill.NotReleasedYet: Strings.unreleased,
  StatusElectronicBill.Published: Strings.published,
  StatusElectronicBill.RefuseToRelease: Strings.refuseRelease,
  StatusElectronicBill.Cancel: Strings.processingStatusLabelCancelled,
  StatusElectronicBill.OffSeasonBrowsing: Strings.outOfTermApproval,
};

const Map<OrderSubStatusEnum, String> orderSubStatusLabel =
    const <OrderSubStatusEnum, String>{
  OrderSubStatusEnum.Waiting: Strings.statusPending,
  OrderSubStatusEnum.Moving: Strings.statusMoving,
  OrderSubStatusEnum.Arrived: Strings.statusArrived,
  OrderSubStatusEnum.Investigating: Strings.statusInvestigating,
  OrderSubStatusEnum.WaitForInvestigationConfirm:
      Strings.statusWaitInvestigationConfirm,
  OrderSubStatusEnum.Investigated: Strings.statusInvestigationConfirmed,
  OrderSubStatusEnum.Processing: Strings.statusProcessing,
  OrderSubStatusEnum.WaitForProcessingConfirm:
      Strings.statusWaitProcessingConfirm,
  OrderSubStatusEnum.ProcessingConfirmed: Strings.statusProcessConfirmed,
  OrderSubStatusEnum.WaitProcessingEditConfirm:
      Strings.statusWaitProcessingEditConfirm,
  OrderSubStatusEnum.WaitingApprove: Strings.statusWaitingApprove,
  OrderSubStatusEnum.Cancel: Strings.statusCancel,
  OrderSubStatusEnum.Completed: Strings.statusCompleted,
};

const Map<OrderProcessingStatusEnum, String> orderProcessingStatusEnumLabel =
    const <OrderProcessingStatusEnum, String>{
  OrderProcessingStatusEnum.Subscribed: Strings.statusSubscribed,
  OrderProcessingStatusEnum.Arrived: Strings.statusArrived,
  OrderProcessingStatusEnum.Moving: Strings.processingStatusMoving,
  OrderProcessingStatusEnum.WaitingForInvestigateWithPartner:
      Strings.statusWaitingForInvestigateWithPartner,
  OrderProcessingStatusEnum.WaitingForInvestigate:
      Strings.statusWaitingForInvestigate,
  OrderProcessingStatusEnum.Investigating:
      Strings.statusProcessingInvestigating,
  OrderProcessingStatusEnum.WaitForInvestigationConfirm:
      Strings.statusWaitInvestigationConfirm,
  OrderProcessingStatusEnum.CompletedForInvestigate:
      Strings.statusCompletedForInvestigate,
  OrderProcessingStatusEnum.Waiting: Strings.statusWaitingMaintance,
  OrderProcessingStatusEnum.WaitingForMaintance:
      Strings.statusWaitingForMaintance,
  OrderProcessingStatusEnum.Processing: Strings.statusProcessingMaintance,
  OrderProcessingStatusEnum.CompletedForMaintance:
      Strings.statusCompletedForMaintance,
  OrderProcessingStatusEnum.Completed: Strings.statusCompleted,
  OrderProcessingStatusEnum.Cancelled: Strings.statusCancel,
};

const Map<int, String> complainStatusLabel = const <int, String>{
  0: Strings.statusUnApprovalPerform,
  1: Strings.statusUnProcessingPerform,
  2: Strings.statusProcessingPerform,
  3: Strings.statusCompleted,
  4: Strings.investigationStatusClose,
  null: ""
};

const Map<int, Color> complainStatusColor = const <int, Color>{
  0: HCColorPending,
  1: HCColorPending,
  2: HCColorProcessing,
  3: HCColorSuccess,
  4: HCColorGrey,
  null: HCColorGrey,
};

const Map<OrderType, String> orderTypeLabel = const <OrderType, String>{
  OrderType.Normal: Strings.orderNormal,
  OrderType.Package: Strings.orderPackage,
  OrderType.Maintenance: Strings.orderMaintance,
  OrderType.Partner: Strings.orderPartner,
  OrderType.Supply: Strings.orderSupply,
  OrderType.PackageWithoutInvestigation: Strings.packageOrderNotSurvey,
};

const Map<OrderSearchStatus, String> orderSearchStatusLabel =
    const <OrderSearchStatus, String>{
  OrderSearchStatus.Requesting: Strings.statusRequesting,
  OrderSearchStatus.Waiting: Strings.statusWaitingPerform,
  OrderSearchStatus.Processing: Strings.statusProcessingPerform,
  OrderSearchStatus.Processed: Strings.statusProcessedPerform,
  OrderSearchStatus.Completed: Strings.statusCompleted,
  OrderSearchStatus.Cancelled: Strings.statusCancelled,
};

const Map<SourceType, String> orderSourceTypeLabel = const <SourceType, String>{
  SourceType.AdminPortal: Strings.sourceAdmin,
  SourceType.App: Strings.sourceApp,
  SourceType.AppPartner: Strings.sourceAppPartner,
};

List<S2Choice<OrderType>> choiceFilterOrderType = [
  S2Choice<OrderType>(
    title: 'Dịch vụ lẻ',
    value: OrderType.Normal,
  ),
  S2Choice<OrderType>(
    title: 'Dịch vụ combo',
    value: OrderType.Combo,
  ),
  S2Choice<OrderType>(
    title: 'Dịch vụ gói có khảo sát',
    value: OrderType.Package,
  ),
  S2Choice<OrderType>(
    title: 'Dịch vụ gói không khảo sát',
    value: OrderType.PackageWithoutInvestigation,
  ),
  S2Choice<OrderType>(
    title: 'Dịch vụ vật tư',
    value: OrderType.Supply,
  ),
  S2Choice<OrderType>(
    title: 'Dịch vụ hỗ trợ kỹ thuật',
    value: OrderType.Maintenance,
  ),
  S2Choice<OrderType>(
    title: 'Dịch vụ đối tác',
    value: OrderType.Partner,
  ),
  S2Choice<OrderType>(
    title: 'Phiếu mua hàng',
    value: OrderType.SalePoint,
  ),
  S2Choice<OrderType>(
    title: 'Đơn bán phạt',
    value: OrderType.FineOrder,
  ),
];

List<S2Choice<OrderStatusEnum>> choiceFilterOrderStatus = [
  S2Choice<OrderStatusEnum>(
    title: Strings.orderPending,
    value: OrderStatusEnum.Waiting,
  ),
  S2Choice<OrderStatusEnum>(
    title: Strings.orderProcessing,
    value: OrderStatusEnum.Processing,
  ),
  S2Choice<OrderStatusEnum>(
    title: Strings.orderCompleted,
    value: OrderStatusEnum.Completed,
  ),
  S2Choice<OrderStatusEnum>(
    title: Strings.orderCancel,
    value: OrderStatusEnum.Cancelled,
  ),
];

List<S2Choice<ComplainStatus>> choiceFilterComplainStatus = [
  // S2Choice<ComplainStatus>(
  //   title: 'Tất cả',
  //   value: null,
  // ),
  S2Choice<ComplainStatus>(
    title: 'Chưa thực hiện',
    value: ComplainStatus.UN_PROCESS,
  ),
  S2Choice<ComplainStatus>(
    title: 'Đang thực hiện',
    value: ComplainStatus.IN_PROCESS,
  ),
  S2Choice<ComplainStatus>(
    title: 'Đã hoàn thành',
    value: ComplainStatus.COMPLETED,
  ),
  S2Choice<ComplainStatus>(
    title: 'Chờ tiếp nhận',
    value: ComplainStatus.UN_APPROVAL,
  ),
  // S2Choice<ComplainStatus>(
  //   title: 'Đóng',
  //   value: ComplainStatus.CLOSED,
  // ),
];

List<S2Choice<ComplainProcessStatus>> choiceFilterTimeLimit = [
  // S2Choice<ComplainProcessStatus>(
  //   title: 'Tất cả',
  //   value: null,
  // ),
  S2Choice<ComplainProcessStatus>(
    title: 'Trong hạn',
    value: ComplainProcessStatus.INTIME,
  ),
  S2Choice<ComplainProcessStatus>(
    title: 'Sắp quá hạn',
    value: ComplainProcessStatus.CLOSEDATE,
  ),
  S2Choice<ComplainProcessStatus>(
    title: 'Quá hạn',
    value: ComplainProcessStatus.OVERTIME,
  ),
];

List<S2Choice<OrderProcessingStatusEnum>> choiceFilterOrderProcessingStatus = [
  S2Choice<OrderProcessingStatusEnum>(
    value: OrderProcessingStatusEnum.Subscribed,
    title: orderProcessingStatusEnumLabel[OrderProcessingStatusEnum.Subscribed],
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[
        OrderProcessingStatusEnum.WaitingForInvestigate],
    value: OrderProcessingStatusEnum.WaitingForInvestigate,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[
        OrderProcessingStatusEnum.WaitingForInvestigateWithPartner],
    value: OrderProcessingStatusEnum.WaitingForInvestigateWithPartner,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[OrderProcessingStatusEnum.Moving],
    value: OrderProcessingStatusEnum.Moving,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[OrderProcessingStatusEnum.Arrived],
    value: OrderProcessingStatusEnum.Arrived,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title:
        orderProcessingStatusEnumLabel[OrderProcessingStatusEnum.Investigating],
    value: OrderProcessingStatusEnum.Investigating,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[
        OrderProcessingStatusEnum.WaitForInvestigationConfirm],
    value: OrderProcessingStatusEnum.WaitForInvestigationConfirm,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[
        OrderProcessingStatusEnum.CompletedForInvestigate],
    value: OrderProcessingStatusEnum.CompletedForInvestigate,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[OrderProcessingStatusEnum.Waiting],
    value: OrderProcessingStatusEnum.Waiting,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[
        OrderProcessingStatusEnum.WaitingForMaintance],
    value: OrderProcessingStatusEnum.WaitingForMaintance,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[OrderProcessingStatusEnum.Processing],
    value: OrderProcessingStatusEnum.Processing,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[
        OrderProcessingStatusEnum.CompletedForMaintance],
    value: OrderProcessingStatusEnum.CompletedForMaintance,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[OrderProcessingStatusEnum.Completed],
    value: OrderProcessingStatusEnum.Completed,
  ),
  S2Choice<OrderProcessingStatusEnum>(
    title: orderProcessingStatusEnumLabel[OrderProcessingStatusEnum.Cancelled],
    value: OrderProcessingStatusEnum.Cancelled,
  ),
];

const Map<PartnerToolTransferStatus, String> partnerToolTransferStatusLabel =
    const <PartnerToolTransferStatus, String>{
  PartnerToolTransferStatus.Accept: Strings.partnerToolTransferStatusAccept,
  PartnerToolTransferStatus.Pending: Strings.partnerToolTransferStatusPending,
  PartnerToolTransferStatus.Reject: Strings.partnerToolTransferStatusReject,
};

const Map<TypeSaleGroup, String> typeSaleGroup = const <TypeSaleGroup, String>{
  TypeSaleGroup.Type1: 'BH-Loại 1',
  TypeSaleGroup.Type2: 'BH-Loại 2',
  TypeSaleGroup.Type3: 'BH-Loại 3',
};

const Map<ExplanationHistoryStatus, String> ExplanationHistoryToLabel = const <ExplanationHistoryStatus, String>{
  ExplanationHistoryStatus.CREATE_PUNISHMENT: Strings.createPunishment,
  ExplanationHistoryStatus.SEND_EXPLANATION: Strings.sendExplanation,
  ExplanationHistoryStatus.REFUSE_TO_EXPLAIN: Strings.declinedExplained,
  ExplanationHistoryStatus.EXPLANATION_ON_ADMIN: Strings.explanationOnAdmin,
  ExplanationHistoryStatus.APPROVE_EXPLANATION: Strings.approvedExplanation,
  ExplanationHistoryStatus.AUTOMATICALLY_CLOSE_EXPLANATION: Strings.autoCloseExplanation,
};

List<S2Choice<OrderSearchStatus>> choiceFilterOrderSearchStatus = [
  S2Choice<OrderSearchStatus>(
    title: Strings.statusRequesting,
    value: OrderSearchStatus.Requesting,
  ),
  S2Choice<OrderSearchStatus>(
    title: Strings.statusWaitingPerform,
    value: OrderSearchStatus.Waiting,
  ),
  S2Choice<OrderSearchStatus>(
    title: Strings.statusProcessingPerform,
    value: OrderSearchStatus.Processing,
  ),
  S2Choice<OrderSearchStatus>(
    title: Strings.statusProcessedPerform,
    value: OrderSearchStatus.Processed,
  ),
  S2Choice<OrderSearchStatus>(
    title: Strings.statusCompleted,
    value: OrderSearchStatus.Completed,
  ),
  S2Choice<OrderSearchStatus>(
    title: Strings.statusCancelled,
    value: OrderSearchStatus.Cancelled,
  ),
];

List<S2Choice<OrderPackageSearchStatus>> choiceFilterOrderPackageSearchStatus =
    [
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusWaitingForInvestigate,
    value: OrderPackageSearchStatus.Requesting,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusWaitingForInvestigateWithPartner,
    value: OrderPackageSearchStatus.WaitingForInvestigate,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusInvestigating,
    value: OrderPackageSearchStatus.Investigating,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusCompletedForInvestigate,
    value: OrderPackageSearchStatus.Investigated,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.waiting,
    value: OrderPackageSearchStatus.Waiting,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusRequestingForProcess,
    value: OrderPackageSearchStatus.RequestingForProcess,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusWaitingPerform,
    value: OrderPackageSearchStatus.WaitingForProcess,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusProcessingPerform,
    value: OrderPackageSearchStatus.Processing,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusProcessedPerform,
    value: OrderPackageSearchStatus.Processed,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusCompleted,
    value: OrderPackageSearchStatus.Completed,
  ),
  S2Choice<OrderPackageSearchStatus>(
    title: Strings.statusCancelled,
    value: OrderPackageSearchStatus.Cancelled,
  )
];
List<S2Choice<ExplanationStatus>> choiceFilterExplanationType = [
  S2Choice<ExplanationStatus>(
    title: 'Chưa giải trình',
    value: ExplanationStatus.NOT_EXPLAINED,
  ),
  S2Choice<ExplanationStatus>(
    title: 'Đã giải trình',
    value: ExplanationStatus.EXPLAINED,
  ),
  S2Choice<ExplanationStatus>(
    title: 'Phê duyệt giải trình',
    value: ExplanationStatus.CLOSED_EXPLANATION,
  ),
  S2Choice<ExplanationStatus>(
    title: 'Từ chối giải trình',
    value: ExplanationStatus.DECLINED_EXPLANATION,
  ),
];


const Map<EmployeeStatusInvestigation, String> investigationEmployeeStatusLabel =
const <EmployeeStatusInvestigation, String>{
  EmployeeStatusInvestigation.Waiting: Strings.statusWaitingInvestigate,
  EmployeeStatusInvestigation.Processing: Strings.statusInvestigating,
  EmployeeStatusInvestigation.Completed: Strings.statusCompletedForInvestigate,
};

List<S2Choice<String>> choiceFieldType = [
  S2Choice<String>(
    title: 'Dịch vụ',
    value: '4',
  ),
  S2Choice<String>(
    title: 'Xây dựng',
    value: '2',
  ),
  S2Choice<String>(
    title: 'Sản phẩm',
    value: '1',
  ),
];