import 'package:flutter/material.dart';
import 'package:home_care_partner/constants/font_family.dart';
import 'package:home_care_partner/constants/font_size.dart';
import 'package:nb_utils/nb_utils.dart';

import 'colors.dart';

class Style {
  static TextStyle typography = TextStyle(
    fontWeight: FontWeight.w400,
    fontSize: FontSize.s14,
    fontFamily: FontFamily.sfUIText,
    color: Colors.black,
  );

  static TextStyle contentTitle = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: FontSize.s16,
    fontFamily: FontFamily.sfUIText,
    color: Colors.black,
  );

  static TextStyle cardTitle = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: FontSize.s16,
    fontFamily: FontFamily.sfCompactText,
    color: Colors.black,
  );

  static TextStyle sfProTextNormal = TextStyle(
    fontSize: FontSize.s14,
    fontWeight: FontWeight.w400,
    fontFamily: FontFamily.sfProText,
  );

  static TextStyle moneyUnit = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: FontSize.s14,
    fontFamily: FontFamily.sfUIText,
  );

  static TextStyle money = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: FontSize.s12,
    fontFamily: FontFamily.sfProText,
  );

  static TextStyle hintText = TextStyle(
    fontWeight: FontWeight.w400,
    fontSize: FontSize.s12,
  );

  static TextStyle textWhiteBold = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: FontSize.s14,
    color: Colors.white,
  );

  static TextStyle textUITextNormal = TextStyle(color: Colors.black, fontSize: FontSize.s14, fontWeight: FontWeight.w600, fontFamily: FontFamily.sfUIText,);

  static TextStyle textWhiteNormal = TextStyle(
    fontWeight: FontWeight.w400,
    fontSize: FontSize.s14,
    color: Colors.white,
  );

  static TextStyle textLabel = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: FontSize.s12,
    fontFamily: FontFamily.sfCompactText,
    color: Colors.white,
  );

  static TextStyle textRequired = TextStyle(
      fontWeight: FontWeight.w400,
      fontSize: FontSize.s14,
      fontFamily: FontFamily.sfUIText,
      color: HCColorPrimary);
  static TextStyle sfProTextNormalWorkShift = TextStyle(
    fontSize: FontSize.s12,
    fontWeight: FontWeight.w400,
    fontFamily: FontFamily.sfProText,
  );
  static BorderRadius radiusAll(double radius) =>
      BorderRadius.all(Radius.circular(radius));

  static BorderRadius radiusTopBottomLeft(double radius) => BorderRadius.only(
        topLeft: Radius.circular(radius),
        bottomLeft: Radius.circular(radius),
      );

  static BorderRadius radiusTopLeft(double radius) => BorderRadius.only(
        topLeft: Radius.circular(radius),
        bottomLeft: Radius.circular(radius),
      );

  static BorderRadius radiusTop(double radius) => BorderRadius.only(
        topLeft: Radius.circular(radius),
        topRight: Radius.circular(radius),
      );

  static BorderRadius radiusRight(double radius) => BorderRadius.only(
        bottomRight: Radius.circular(radius),
        topRight: Radius.circular(radius),
      );

  static BorderRadius radiusBottom(double radius) => BorderRadius.only(
        bottomRight: Radius.circular(radius),
        bottomLeft: Radius.circular(radius),
      );

  static BorderRadius radiusLeft(double radius) => BorderRadius.only(
        bottomLeft: Radius.circular(radius),
        topLeft: Radius.circular(radius),
      );

  static List<BoxShadow> defaultBoxShadow({
    Color shadowColor,
    double blurRadius,
    double spreadRadius,
    Offset offset = const Offset(0.0, 0.0),
  }) {
    return [
      BoxShadow(
        color: shadowColor ?? Colors.black12,
        blurRadius: blurRadius ?? defaultBlurRadius,
        spreadRadius: spreadRadius ?? defaultSpreadRadius,
        offset: offset,
      )
    ];
  }

  static BoxDecoration boxDecoration({
    double radius = 2,
    Color color = Colors.transparent,
    Color bgColor,
    var showShadow = false,
  }) {
    return BoxDecoration(
      color: bgColor ?? Colors.white,
      boxShadow: showShadow
          ? defaultBoxShadow(shadowColor: Colors.black12)
          : [BoxShadow(color: Colors.transparent)],
      border: Border.all(color: color),
      borderRadius: BorderRadius.all(Radius.circular(radius)),
    );
  }

  static InputDecoration inputBorderNone = InputDecoration(
    contentPadding: EdgeInsets.zero,
    enabledBorder: InputBorder.none,
    focusedBorder: InputBorder.none,
  );

  static InputDecoration inputDecoration({
    String labelText,
    String hintText,
    IconData suffixIcon,
    Color colorSuffix,
    Function onTapSuffix,
    String suffixText,
    Color suffixIconColor = HCColorGrey,
    bool isFilled = false,
    Color fillColor = HCColorGreyLightV5,
  }) {
    return InputDecoration(
      filled: isFilled,
      fillColor: fillColor,
      labelText: labelText,
      errorMaxLines: 5,
      labelStyle: TextStyle(fontSize: FontSize.s13),
      isDense: true,
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: HCColorGreyLightV5,
        ),
        borderRadius: Style.radiusAll(6),
      ),
      hintText: hintText,
      hintStyle: Style.typography.copyWith(
        color: HCColorGreyLightV5,
      ),
      errorStyle: Style.typography.copyWith(
        fontSize: FontSize.s13,
        color: HCColorPrimary,
        fontStyle: FontStyle.italic,
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: HCColorPrimary,
        ),
        borderRadius: Style.radiusAll(6),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: HCColorPrimary,
        ),
        borderRadius: Style.radiusAll(6),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: HCColorGreyLightV5,
        ),
        borderRadius: Style.radiusAll(6),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: HCColorGreyLightV5,
        ),
        borderRadius: Style.radiusAll(6),
      ),
      contentPadding: EdgeInsets.symmetric(
        vertical: 12,
        horizontal: 15,
      ),
      suffixText: suffixText,
      suffixIcon: suffixIcon != null
          ? Icon(
              suffixIcon,
              color: colorSuffix ?? suffixIconColor,
              size: 20,
            ).onTap(onTapSuffix ?? (){})
          : null,
    );
  }
}
