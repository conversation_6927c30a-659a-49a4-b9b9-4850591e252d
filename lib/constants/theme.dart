const mainAppName = 'ProKit Flutter';

const fontRegular = 'Regular';
const fontMedium = 'Medium';
const fontSemibold = 'Semibold';
const fontBold = 'Bold';

/* font sizes*/
const textSizeSmall = 12.0;
const textSizeSMedium = 14.0;
const textSizeMedium = 16.0;
const textSizeLargeMedium = 18.0;
const textSizeNormal = 20.0;
const textSizeLarge = 24.0;
const textSizeXLarge = 30.0;
const textSizeXXLarge = 35.0;

// Shared Pref
const appOpenCount = 'appOpenCount';

/// Linux - for linux, you have to change default window width in linux/my_application.cc
const applicationMaxWidth = 500.0;

const profileImage = 'images/widgets/materialWidgets/mwInputSelectionWidgets/Checkbox/profile.png';
const isDarkModeOnPref = 'isDarkModeOnPref';
const dateFormat = 'MMM dd, yyyy';

const BaseUrl = 'https://iqonic.design/themeforest-images/prokit';
const SourceCodeUrl = 'https://codecanyon.net/item/prokit-flutter-app-ui-design-templete-kit/25787190?s_rank=11';
const PlayStoreUrl = 'https://play.google.com/store/apps/details?id=';
const DocumentationUrl = 'https://iqonic.design/docs/product/prokit-flutter/';
const ChangeLogsUrl = 'https://iqonic.design/docs/product/prokit-flutter/updates/change-logs/';
const MimikCloneUrl = 'https://codecanyon.net/item/mimik-multi-category-flutter-app-ui-kit-clone/29382568?s_rank=2';

const bannerAdIdForAndroidRelease = "ca-app-pub-1399327544318575/5026584528";
const bannerAdIdForAndroid = "ca-app-pub-3940256099942544/6300978111";
const bannerAdIdForIos = "ca-app-pub-3940256099942544/2934735716";
const InterstitialAdIdForAndroidRelease = "ca-app-pub-1399327544318575/8774597158";
const InterstitialAdIdForAndroid = "ca-app-pub-3940256099942544/1033173712";
const interstitialAdIdForIos = "ca-app-pub-3940256099942544/4411468910";

const SampleImageUrl = '$BaseUrl/images/defaultTheme/slider/01.jpg';
const SampleImageUrl2 = '$BaseUrl/images/defaultTheme/slider/04.jpg';
const SampleImageUrl3 = '$BaseUrl/images/defaultTheme/slider/03.jpg';
const SampleImageUrl4 = '$BaseUrl/images/defaultTheme/slider/05.jpg';
const SampleProfileImageUrl = 'https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50?s=200';

const ProductImg1 = '$BaseUrl/images/defaultTheme/product/Bag.png';
const ProductImg2 = '$BaseUrl/images/defaultTheme/product/Chair.png';
const ProductImg3 = '$BaseUrl/images/defaultTheme/product/choclate.png';
const ProductImg4 = '$BaseUrl/images/defaultTheme/product/dumble.png';
const ProductImg5 = '$BaseUrl/images/defaultTheme/product/headphone.png';
const ProductImg6 = '$BaseUrl/images/defaultTheme/product/jewellery.png';
const ProductImg7 = '$BaseUrl/images/defaultTheme/product/shoes.png';
const ProductImg8 = '$BaseUrl/images/defaultTheme/product/skincare.png';
const ProductImg9 = '$BaseUrl/images/defaultTheme/product/tshirt.png';

const LoremText = 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.This is simply text';



const BHSender_id = 1;
const BHReceiver_id = 2;

const BHWalkThroughTitle1 = "Find barbershop nearby";
const BHWalkThroughSubTitle1 = "Choose your hair style choose your hair style choose your hair style";
const BHWalkThroughTitle2 = "Attractive Promotions";
const BHWalkThroughSubTitle2 = "Choose your hair style choose your hair style choose your hair style";
const BHWalkThroughTitle3 = "The Professional Specialists";
const BHWalkThroughSubTitle3 = "Choose your hair style choose your hair style choose your hair style";
const BHForgotPasswordSubTitle = "Please enter your Email so we can help you recover your password";
const BHVerificationTitle = "Enter the OTP code from the phone we just sent you";
const BHResetPasswordTitle = "Enter your new password and confirm it.";
const BHAlertDialogTitle = "Your password has been completely reset, login and use the app";
const BHDetailTitle =
    "Time to stop imagining and take advantage of the unique properties of graphene. Paragraf develop and deliver game-changing, commercial-quality, graphene-based electronic devices using contamination-free technology.Read more ";
const BHReview = "Love it !!! /n I had a fun party with my new hair color, Thank you.";
const BHInviteFriendsTitle = "Please share the code below for your friends to join the HASA.";
const BHBtnNext = 'Next';
const BHBtnGetStarted = 'Get Started';
const BHTxtForgetPwd = 'Forgot password ?';
const BHBtnSignIn = 'Sign In';
const BHTxtOrSignIn = 'Or sign in with';
const BHBtnSignUp = 'Sign Up';
const BHTxtAccount = "You have an account? ";
const BHTxtForgotPwd = 'Forgot Password';
const BHBtnSend = 'Send';
const BHTxtVerification = 'Verification';
const BHTxtResendOn = 'Resend on';
const BHBtnContinue = 'Continue';
const BHBtnOk = 'Ok';
const BHBtnNo = 'No';
const BHTxtResetPwd ='Reset Password';
const BHTxtCongratulation ='Congratulation';
const BHTxtBestSpecialists = 'Best Specialists';
const BHTxtSpecialOffers= 'Special Offers';
const BHTxtSalonSpecialLists=  'Salon SpecialLists';
const BHTxtSalonServices=   'Salon Services';
const BHTxtViewAll= 'View all';
const BHBtnOpen=  'Open';
const BHTabAbout=  'About';
const BHTabGallery=  'Gallery';
const BHTabServices=  'Services';
const BHTabReview=  'Review';
const BHTabSalonSpecialList=  'Salon SpecialList';
const BHTxtInformation=  'Information';
const BHTxtContact=  'Contact';
const BHTxtOpeningTime=  'Opening Time';
const BHTxtAddress=  'Address';
const BHTxtReview=  'Your Review';
const BHTxtReviewMsg= 'what are you feel about this salon?';
const BHTxtHairStyle= 'Hair Stylist';
const BHTxtMakeupArtist= 'Makeup Artist';
const BHTxtPackageOffers= 'Package & Offers';
const BHTxtPopularServices= 'Popular Services';
const BHBtnBookAppointment=  'Book Appointment';
const BHTxtTimeOfEvent=  'Time of Event';
const BHTxtServicesInclude=  'Services Include';
const BHStepperBookAppointment= 'Book \n Appointment';
const BHStepperPayment= 'Payment';
const BHStepperFinished= 'Finished';
const BHTxtChooseSpecialList= 'Choose SpecialList';
const BHTxtHairStylists= 'Hair Stylists';
const BHTxtAvailableSlot= 'Available Slot';
const BHBtnYes= 'Yes';
const BHBtnCancel= 'Cancel';
const BHTxtPaymentMethods= 'Payment Methods';
const BHTxtAddMethod=  'Add new method';
const BHTxtBookMoreAppointment=  'Book more Appointment';
const BHTxtGoAppointment=   'Go to Appointment';
const BHTabOngoing=  'Ongoing';
const BHTabHistory=  'History';
const BHTabMessages= 'Messages';
const BHTabCalls=  'Calls';
const BHTxtAccountInformation=  'Account Information';
const BHTxtNotification=   'Notification';
const BHTxtInviteFriends=  'Invite Friends';
const BHTxtSetting=  'Setting';
const BHTxtTermsOfServices=   'Terms of Services';
const BHTxtLogout=   'Logout';
const BHTxtLogoutDialog=   'Confirmation';
const BHTxtEdit= 'Edit';
const BHTxtCopy= 'Copy';
const BHTxtFacebook=  'Facebook';
const BHTxtTwitter=   'Twitter';
const BHTxtInstagram= 'Instagram';
const BHTxtContacts=  'Contacts';
const BHTxtLogoutMsg=  'Are you sure you want to exit!!!?';
// const BHTxtCalendar='Lịch';
// const BHTxtGrid='Danh sách';
// const BHBottomNavHome=  'Trang chủ';
// const BHBottomNavCalendar=  'Công việc';
// const BHBottomNavChat=  'Trao đổi';
// const BHBottomNavMessages= 'Trao đổi';
// const BHBottomNavProfile= 'Cá nhân';


const BHHairSalonLogo = "images/hairSalon/bh_hairSalonLogo.png";
const BHAppLogo = "images/hairSalon/bh_logo.svg";
const BHWalkThroughImg1 = "$BaseUrl/images/hairSalon/bh_walkThroughImg1.jpg";
const BHWalkThroughImg2 = "$BaseUrl/images/hairSalon/bh_walkThroughImg2.jpg";
const BHWalkThroughImg3 = "$BaseUrl/images/hairSalon/bh_walkThroughImg3.jpg";
const BHFacebookIcon = "images/hairSalon/bh_facebookIcon.svg";
const BHTwitterIcon = "images/hairSalon/bh_twitterIcon.svg";
const BHPinterestIcon = "images/hairSalon/bh_pinterestIcon.svg";
const BHInstagramIcon = "images/hairSalon/bh_instagramIcon.jpg";
const BHContactsIcon = "images/hairSalon/bh_contactsIcon.jpg";
const BHProfileImage = "images/hairSalon/bh_userImg.jpeg";
const BHAddIcon = "images/hairSalon/bh_addIcon.png";
const BHLogoutIcon = "images/hairSalon/bh_logoutIcon.png";
const BHPaymentIcon = "images/hairSalon/bh_paymentIcon.png";
const BHInformationIcon = "images/hairSalon/bh_informationIcon.png";
const BHNotificationIcon = "images/hairSalon/bh_notificationIcon.png";
const BHInviteFriendsIcon = "images/hairSalon/bh_inviteFriendsIcon.png";
const BHSettingIcon = "images/hairSalon/bh_settingIcon.png";
const BHTerms_and_ServicesIcon = "images/hairSalon/bh_terms_and_Services.png";
const BHMessageIcon = "images/hairSalon/bh_messageIcon.png";
const BHCallIcon = "images/hairSalon/bh_callIcon.png";
const BHVideoCallIcon = "images/hairSalon/bh_videoCallIcon.png";
const BHBarCodeImg = "images/hairSalon/bh_barCodeImg.png";
const BHDashedBoardImage1 = "images/hairSalon/bh_dashedBoardImg1.jpg";
const BHDashedBoardImage2 = "images/hairSalon/bh_dashedBoardImg2.jpg";
const BHDashedBoardImage3 = "images/hairSalon/bh_dashedBoardImg3.jpg";
const BHDashedBoardImage4 = "$BaseUrl/images/hairSalon/bh_dashedBoardImg4.jpg";
const BHdDashedBoardImage5 = "$BaseUrl/images/hairSalon/bh_dashedBoardImage5.jpg";
const BHDashedBoardImage6 = "images/hairSalon/bh_dashedBoardImg6.jpg";
const BHDashedBoardImage7 = "$BaseUrl/images/hairSalon/bh_dashedBoardImg7.jpg";
const BHMakeUp = "images/hairSalon/bh_makeUp.svg";
const BHInviteFriends = "images/hairSalon/bh_inviteFriends.jpg";
const BHAlertDialogLogo = "images/hairSalon/bh_alertDialogLogo.png";
const BHCallingBackgroundImg = "$BaseUrl/images/hairSalon/bh_callingBackgroundImg.jpg";
const BHVisaCardImg = "images/hairSalon/bh_visaCardImg.webp";
const BHMasterCardImg = "images/hairSalon/bh_masterCardImg.png";
const BHSkinCare = "images/hairSalon/bh_skinCare.svg";
const BHHairColor = "images/hairSalon/bh_hairColor.svg";
const BHForwardMsg = "images/hairSalon/bh_forwardMsg.png";
const BHDotsImg = "images/hairSalon/bh_dotsImg.png";
const BHNotification = "images/hairSalon/bh_notification.png";
const BHCross = "images/hairSalon/bh_cross.png";
const BHMessage = "images/hairSalon/bh_message.png";
const BHLocation = "images/hairSalon/bh_location.png";
const BHUserImg = "images/hairSalon/bh_userImg.jpeg";


