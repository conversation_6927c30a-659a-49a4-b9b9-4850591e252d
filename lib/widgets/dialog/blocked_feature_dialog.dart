import 'dart:io';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:home_care_partner/constants/assets.dart';

/// Dialog thông báo khi chức năng bị chặn
class BlockedFeatureDialog extends StatelessWidget {
  final String route;
  final String message;
  final String updateUrl;
  final bool canDismiss;

  const BlockedFeatureDialog({
    Key key,
    @required this.route,
    @required this.message,
    this.updateUrl,
    this.canDismiss = true,
  }) : super(key: key);

  /// Mở URL cập nhật ứng dụng
  Future<void> _launchUpdateURL() async {
    String url = updateUrl ?? (Platform.isIOS
        ? 'https://apps.apple.com/us/app/aio-partner/id6738956798'
        : 'https://play.google.com/store/apps/details?id=vn.vcc.b2c.app&pli=1');
    
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => canDismiss,
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        contentPadding: EdgeInsets.all(24),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon cảnh báo
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.block,
                size: 32,
                color: Colors.orange,
              ),
            ),
            SizedBox(height: 16),
            
            // Tiêu đề
            Text(
              'Chức năng bị tạm khóa',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 12),
            
            // Nội dung thông báo
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: Colors.black54,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            
            // Thông báo cập nhật
            Text(
              'Vui lòng cập nhật ứng dụng lên phiên bản mới nhất để tiếp tục sử dụng.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black45,
                height: 1.3,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          // Nút Hủy (chỉ hiển thị nếu có thể dismiss)
          if (canDismiss)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Đóng',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          
          // Nút Cập nhật
          RaisedButton(
            onPressed: () {
              _launchUpdateURL();
            },
            color: Colors.blue,
            textColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            child: Text(
              'Cập nhật ứng dụng',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
        actionsPadding: EdgeInsets.fromLTRB(24, 0, 24, 16),
      ),
    );
  }
}

/// Dialog đơn giản chỉ thông báo
class SimpleBlockedFeatureDialog extends StatelessWidget {
  final String title;
  final String message;
  final String buttonText;
  final VoidCallback onPressed;

  const SimpleBlockedFeatureDialog({
    Key key,
    this.title = 'Chức năng bị tạm khóa',
    @required this.message,
    this.buttonText = 'Đã hiểu',
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.orange,
            size: 24,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: Text(
        message,
        style: TextStyle(
          fontSize: 16,
          height: 1.4,
        ),
      ),
      actions: [
        TextButton(
          onPressed: onPressed ?? () => Navigator.of(context).pop(),
          child: Text(
            buttonText,
            style: TextStyle(
              color: Colors.blue,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

/// Dialog với tùy chọn liên hệ hỗ trợ
class BlockedFeatureWithSupportDialog extends StatelessWidget {
  final String message;
  final String supportPhone;
  final String supportEmail;

  const BlockedFeatureWithSupportDialog({
    Key key,
    @required this.message,
    this.supportPhone = '1900 1234',
    this.supportEmail = '<EMAIL>',
  }) : super(key: key);

  Future<void> _callSupport() async {
    final url = 'tel:$supportPhone';
    if (await canLaunch(url)) {
      await launch(url);
    }
  }

  Future<void> _emailSupport() async {
    final url = 'mailto:$supportEmail';
    if (await canLaunch(url)) {
      await launch(url);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            Icons.block,
            color: Colors.red,
            size: 24,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              'Chức năng bị tạm khóa',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              height: 1.4,
            ),
          ),
          SizedBox(height: 16),
          Text(
            'Nếu bạn cần hỗ trợ, vui lòng liên hệ:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.phone, size: 16, color: Colors.blue),
              SizedBox(width: 4),
              Text(
                supportPhone,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          SizedBox(height: 4),
          Row(
            children: [
              Icon(Icons.email, size: 16, color: Colors.blue),
              SizedBox(width: 4),
              Text(
                supportEmail,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Đóng'),
        ),
        TextButton(
          onPressed: _callSupport,
          child: Text('Gọi hỗ trợ'),
        ),
      ],
    );
  }
}
