import 'package:flutter/material.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/ui/direct_app/alert_service.dart';
import 'package:home_care_partner/widgets/dialog/blocked_feature_dialog.dart';

/// Wrapper để tự động kiểm tra và hiển thị popup alert cho mọi màn hình
class AlertScreenWrapper extends StatefulWidget {
  final Widget child;
  final String screenName; // Tên màn hình để map với feature
  final String routeName; // Route name để debug
  
  const AlertScreenWrapper({
    Key key,
    @required this.child,
    @required this.screenName,
    this.routeName,
  }) : super(key: key);

  @override
  _AlertScreenWrapperState createState() => _AlertScreenWrapperState();
}

class _AlertScreenWrapperState extends State<AlertScreenWrapper> {
  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;
  bool _hasShownAlert = false;

  @override
  void initState() {
    super.initState();
    // Kiểm tra alert sau khi widget được build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowAlert();
    });
  }

  /// Kiểm tra và hiển thị alert nếu cần
  Future<void> _checkAndShowAlert() async {
    if (_hasShownAlert) return;

    try {
      // Kiểm tra xem màn hình này có alert không
      if (_featureBlockingService.hasFeatureAlert(widget.screenName)) {
        final alert = _featureBlockingService.getAlertForFeature(widget.screenName);
        
        if (alert != null && mounted) {
          _showAlertDialog(alert);
        }
      }
    } catch (e) {
      debugPrint('AlertScreenWrapper: Error checking alert for ${widget.screenName}: $e');
    }
  }

  /// Hiển thị dialog alert
  void _showAlertDialog(AlertModel alert) {
    setState(() {
      _hasShownAlert = true;
    });

    showDialog(
      context: context,
      barrierDismissible: !alert.isRequired, // isRequired = true thì không thể đóng
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async => !alert.isRequired, // Kiểm soát back button
          child: AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            contentPadding: EdgeInsets.all(24),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon cảnh báo
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.info_outline,
                    size: 32,
                    color: Colors.orange,
                  ),
                ),
                SizedBox(height: 16),
                
                // Tiêu đề
                Text(
                  'Thông báo',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 12),
                
                // Nội dung thông báo
                Text(
                  alert.message,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black54,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                // Thông tin debug (chỉ hiện trong debug mode)
                if (widget.routeName != null) ...[
                  SizedBox(height: 8),
                  Text(
                    'Screen: ${widget.screenName} (${widget.routeName})',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
            actions: [
              // Nút Đóng (chỉ hiển thị nếu isRequired = false)
              if (!alert.isRequired)
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    'Đóng',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              
              // Nút Đã hiểu (luôn hiển thị)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  'Đã hiểu',
                  style: TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
            actionsPadding: EdgeInsets.fromLTRB(24, 0, 24, 16),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Trả về child widget bình thường
    return widget.child;
  }
}

/// Helper function để wrap màn hình với AlertScreenWrapper
Widget wrapWithAlert(
  Widget child, 
  String screenName, {
  String routeName,
}) {
  return AlertScreenWrapper(
    child: child,
    screenName: screenName,
    routeName: routeName,
  );
}

/// Mixin để dễ dàng thêm alert checking vào StatefulWidget
mixin AlertScreenMixin<T extends StatefulWidget> on State<T> {
  String get screenName; // Phải implement trong class con
  String get routeName => null; // Optional
  
  bool _hasShownAlert = false;
  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowAlert();
    });
  }

  Future<void> _checkAndShowAlert() async {
    if (_hasShownAlert) return;

    try {
      if (_featureBlockingService.hasFeatureAlert(screenName)) {
        final alert = _featureBlockingService.getAlertForFeature(screenName);
        
        if (alert != null && mounted) {
          _showAlertDialog(alert);
        }
      }
    } catch (e) {
      debugPrint('AlertScreenMixin: Error checking alert for $screenName: $e');
    }
  }

  void _showAlertDialog(AlertModel alert) {
    setState(() {
      _hasShownAlert = true;
    });

    showDialog(
      context: context,
      barrierDismissible: !alert.isRequired,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async => !alert.isRequired,
          child: AlertDialog(
            title: Text('Thông báo'),
            content: Text(alert.message),
            actions: [
              if (!alert.isRequired)
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('Đóng'),
                ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Đã hiểu'),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Enum mapping screen names với features
class ScreenFeatureMapping {
  static const Map<String, String> screenToFeature = {
    // Main screens
    'HomeScreen': 'HOME',
    'TaskListPage': 'TASK_LIST',
    'TaskListScreen': 'TASK_LIST',
    'PageViewTask': 'TASK_LIST',
    'CommunicationPage': 'COMMUNICATION',
    'ChatScreen': 'COMMUNICATION',
    'ChatDetailScreen': 'COMMUNICATION',
    'ChatScreenBottomBar': 'COMMUNICATION',
    
    // Profile screens
    'ProfilePage': 'PROFILE',
    'ProfileManagement': 'PROFILE',
    'PrivacyScreen': 'PROFILE',
    'ProfileUpdateScreen': 'PROFILE',
    'NotificationSettingScreen': 'PROFILE',
    'ProfileCatalogScreen': 'PROFILE',
    'ProfileAbilityScreen': 'PROFILE',
    'ProfileToolScreen': 'PROFILE',
    'AddProfileToolScreen': 'PROFILE',
    'ProfileDocumentScreen': 'PROFILE',
    
    // Order screens
    'OrderDetailScreen': 'ORDER_MANAGEMENT',
    'CreateOrderScreen': 'ORDER_MANAGEMENT',
    'EditOrderScreen': 'ORDER_MANAGEMENT',
    
    // Inventory screens
    'PersonalInventoryPage': 'INVENTORY',
    'ConfirmationBillList': 'INVENTORY',
    'DetailBill': 'INVENTORY',
    'CreateTransWarehouse': 'INVENTORY',
    
    // Customer screens
    'DetailCustomerPage': 'CUSTOMER_360',
    'ListSurveyPage': 'CUSTOMER_360',
    'DetailSurveyScreen': 'CUSTOMER_360',
    
    // Settings screens
    'SettingsScreen': 'SETTINGS',
    'ChangePasswordScreen': 'SETTINGS',
    
    // Reports screens
    'ReportsScreen': 'REPORTS',
    'StatisticScreen': 'REPORTS',
  };
  
  /// Lấy feature name từ screen name
  static String getFeatureForScreen(String screenName) {
    return screenToFeature[screenName] ?? 'UNKNOWN';
  }
  
  /// Kiểm tra screen có được map không
  static bool isScreenMapped(String screenName) {
    return screenToFeature.containsKey(screenName);
  }
}
