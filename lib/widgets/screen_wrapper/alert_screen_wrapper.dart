import 'package:flutter/material.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/ui/direct_app/alert_service.dart';
import 'package:home_care_partner/ui/direct_app/alert_dialog_widget.dart';

/// Wrapper để tự động kiểm tra và hiển thị popup alert cho mọi màn hình
class AlertScreenWrapper extends StatefulWidget {
  final Widget child;
  final String screenName; // Tên màn hình để map với feature (deprecated)
  final String routeName; // Route name chính để kiểm tra alert

  const AlertScreenWrapper({
    Key key,
    @required this.child,
    @required this.screenName,
    this.routeName,
  }) : super(key: key);

  @override
  _AlertScreenWrapperState createState() => _AlertScreenWrapperState();
}

class _AlertScreenWrapperState extends State<AlertScreenWrapper> {
  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;
  bool _hasShownAlert = false;

  @override
  void initState() {
    super.initState();
    // Kiểm tra alert sau khi widget được build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowAlert();
    });
  }

  /// Kiểm tra và hiển thị alert nếu cần
  Future<void> _checkAndShowAlert() async {
    if (_hasShownAlert) return;

    try {
      AlertModel alert;

      // 1. Ưu tiên kiểm tra với route name (bỏ dấu / ở đầu)
      if (widget.routeName != null) {
        String routeKey = widget.routeName.replaceFirst('/', '');
        alert = _featureBlockingService.getAlertForFeature(routeKey);
        debugPrint('AlertScreenWrapper: Checking route key: $routeKey');
      }

      // 2. Nếu không có, kiểm tra với screen name
      if (alert == null) {
        alert = _featureBlockingService.getAlertForFeature(widget.screenName);
        debugPrint('AlertScreenWrapper: Checking screen name: ${widget.screenName}');
      }

      // 3. Thử với feature name được map từ route name
      if (alert == null && widget.routeName != null) {
        final featureName = RouteFeatureMapping.getFeatureForRoute(widget.routeName);
        if (featureName != 'UNKNOWN') {
          alert = _featureBlockingService.getAlertForFeature(featureName);
          debugPrint('AlertScreenWrapper: Checking route feature name: $featureName');
        }
      }

      // 4. Cuối cùng, thử với feature name được map từ screen name (fallback)
      if (alert == null) {
        final featureName = ScreenFeatureMapping.getFeatureForScreen(widget.screenName);
        if (featureName != 'UNKNOWN') {
          alert = _featureBlockingService.getAlertForFeature(featureName);
          debugPrint('AlertScreenWrapper: Checking screen feature name: $featureName');
        }
      }

      if (alert != null && mounted) {
        debugPrint('AlertScreenWrapper: Found alert for ${widget.routeName ?? widget.screenName}: ${alert.message}');
        _showAlertDialog(alert);
      } else {
        debugPrint('AlertScreenWrapper: No alert found for ${widget.routeName ?? widget.screenName}');
      }
    } catch (e) {
      debugPrint('AlertScreenWrapper: Error checking alert for ${widget.screenName}: $e');
    }
  }

  /// Hiển thị dialog alert sử dụng AlertDialogWidget
  void _showAlertDialog(AlertModel alert) {
    setState(() {
      _hasShownAlert = true;
    });

    showDialog(
      context: context,
      barrierDismissible: !alert.isRequired, // isRequired = true thì không thể đóng
      builder: (BuildContext context) {
        return AlertDialogWidget(
          alert: alert,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Trả về child widget bình thường
    return widget.child;
  }
}

/// Helper function để wrap màn hình với AlertScreenWrapper
Widget wrapWithAlert(
  Widget child, 
  String screenName, {
  String routeName,
}) {
  return AlertScreenWrapper(
    child: child,
    screenName: screenName,
    routeName: routeName,
  );
}

/// Mixin để dễ dàng thêm alert checking vào StatefulWidget
mixin AlertScreenMixin<T extends StatefulWidget> on State<T> {
  String get screenName; // Phải implement trong class con
  String get routeName => null; // Optional
  
  bool _hasShownAlert = false;
  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowAlert();
    });
  }

  Future<void> _checkAndShowAlert() async {
    if (_hasShownAlert) return;

    try {
      // Kiểm tra trực tiếp với screen name trước
      AlertModel alert = _featureBlockingService.getAlertForFeature(screenName);

      // Nếu không có, thử với feature name được map từ screen name
      if (alert == null) {
        final featureName = ScreenFeatureMapping.getFeatureForScreen(screenName);
        if (featureName != 'UNKNOWN') {
          alert = _featureBlockingService.getAlertForFeature(featureName);
        }
      }

      if (alert != null && mounted) {
        _showAlertDialog(alert);
      }
    } catch (e) {
      debugPrint('AlertScreenMixin: Error checking alert for $screenName: $e');
    }
  }

  void _showAlertDialog(AlertModel alert) {
    setState(() {
      _hasShownAlert = true;
    });

    showDialog(
      context: context,
      barrierDismissible: !alert.isRequired,
      builder: (BuildContext context) {
        return AlertDialogWidget(
          alert: alert,
        );
      },
    );
  }
}

/// Mapping route names với features (dựa trên Routes constants)
class RouteFeatureMapping {
  static const Map<String, String> routeToFeature = {
    // Auth routes - không cần alert
    'login': 'AUTH',
    'register': 'AUTH',
    'verification': 'AUTH',
    'change_password_forgot': 'AUTH',
    'phone_number_forgot': 'AUTH',

    // Main routes
    'home': 'HOME',
    'task_list': 'TASK_LIST',
    'task_list_screen': 'TASK_LIST',
    'page_task': 'TASK_LIST',
    'my_order': 'ORDER_MANAGEMENT',

    // Communication
    'communication': 'COMMUNICATION',
    'chat': 'COMMUNICATION',
    'chat_detail': 'COMMUNICATION',
    'messages': 'COMMUNICATION',

    // Profile
    'profile': 'PROFILE',
    'profile_management': 'PROFILE',
    'profile_update': 'PROFILE',
    'profile_privacy': 'PROFILE',
    'profile_notification_setting': 'PROFILE',
    'profile_catalog': 'PROFILE',
    'profile_ability': 'PROFILE',
    'profile_tool': 'PROFILE',
    'add_profile_tool': 'PROFILE',
    'profile_document': 'PROFILE',
    'list_transfer_tool': 'PROFILE',
    'change_password': 'PROFILE',

    // Order Management
    'order_detail': 'ORDER_MANAGEMENT',
    'order_shipping_detail': 'ORDER_MANAGEMENT',
    'order_rating': 'ORDER_MANAGEMENT',
    'create_order': 'ORDER_MANAGEMENT',
    'create_order_by_saler': 'ORDER_MANAGEMENT',
    'deployment_information': 'ORDER_MANAGEMENT',
    'select_service': 'ORDER_MANAGEMENT',
    'select_service_appointment': 'ORDER_MANAGEMENT',
    'success_create_order': 'ORDER_MANAGEMENT',
    'order_change_package': 'ORDER_MANAGEMENT',
    'list_product_use': 'ORDER_MANAGEMENT',
    'list_product_investigation_use': 'ORDER_MANAGEMENT',
    'edit_product_use': 'ORDER_MANAGEMENT',

    // Inventory & Delivery
    'personal_inventory': 'INVENTORY',
    'confirmation_bill': 'INVENTORY',
    'bill_trans_myself': 'INVENTORY',
    'detail_bill': 'INVENTORY',
    'create_trans_warehouse': 'INVENTORY',
    'delivery_bill_menu': 'INVENTORY',
    'detail_supply_warehouse': 'INVENTORY',
    'create_goods_delivery_note_page': 'INVENTORY',

    // Customer 360
    'customer_360': 'CUSTOMER_360',
    'list_survey_cim': 'CUSTOMER_360',
    'detail_survey_cim': 'CUSTOMER_360',
    'list_contract_cim': 'CUSTOMER_360',
    'detail_contract_cim': 'CUSTOMER_360',
    'list_ticket_cim': 'CUSTOMER_360',
    'detail_ticket_cim': 'CUSTOMER_360',
    'list_warranty_cim': 'CUSTOMER_360',
    'detail_warranty_cim': 'CUSTOMER_360',
    'list_warranty_history_cim': 'CUSTOMER_360',
    'detail_warranty_history_cim': 'CUSTOMER_360',

    // Financial
    'transaction': 'FINANCIAL',
    'viettel_pay': 'FINANCIAL',
    'debt': 'FINANCIAL',
    'create_invoice': 'FINANCIAL',
    'view_invoice_pdf': 'FINANCIAL',
    'list_invoice': 'FINANCIAL',
    'request_budget': 'FINANCIAL',
    'one_pay': 'FINANCIAL',

    // Reports
    'edited_order': 'REPORTS',
    'sold_order': 'REPORTS',
    'auto_distribution': 'REPORTS',

    // System & Support
    'report_system_error': 'SYSTEM',
    'report_system_error_detail': 'SYSTEM',
    'create_report_system_error': 'SYSTEM',
    'edit_report_error': 'SYSTEM',
    'report_error_created': 'SYSTEM',
    'contribute_idea': 'SYSTEM',
    'create_contribute_idea': 'SYSTEM',
    'contribute_idea_detail': 'SYSTEM',
    'not_found': 'SYSTEM',

    // Investigation
    'investigation_detail': 'INVESTIGATION',
    'investigation_detail_waiting': 'INVESTIGATION',
    'create_investigation': 'INVESTIGATION',
    'explanation_detail': 'INVESTIGATION',
    'explanation_explain_history': 'INVESTIGATION',
    'explanation': 'INVESTIGATION',

    // Questions & Surveys
    'question_examined': 'SURVEY',
    'employee_investigation_detail': 'SURVEY',
    'list_question': 'SURVEY',
    'detail_question': 'SURVEY',

    // Notifications
    'notification': 'NOTIFICATION',
    'my_notification': 'NOTIFICATION',
    'detail_notification': 'NOTIFICATION',

    // Media
    'play_video': 'MEDIA',
    'view_image': 'MEDIA',
    'view_list_image': 'MEDIA',
    'viewDocumentCim': 'MEDIA',
    'web_view': 'MEDIA',

    // Complain & Tickets
    'complain_detail': 'COMPLAIN',
    'coordinat_screen': 'COMPLAIN',
    'explain_history_screen': 'COMPLAIN',
    'cim_explain_history_screen': 'COMPLAIN',
    'complain_history_screen': 'COMPLAIN',
    'contact_history_screen': 'COMPLAIN',
    'create_ticket': 'COMPLAIN',

    // Mission & Tasks
    'mission_detail': 'MISSION',
    'task_plan': 'MISSION',
    'mission': 'MISSION',

    // Sales
    'create_sale_group': 'SALES',
    'detail_sale_group': 'SALES',
    'censorship': 'SALES',
    'proposed_purchase': 'SALES',
    'proposed_purchase_create': 'SALES',
    'sale_point_order_detail': 'SALES',
    'create_sale_point_order_sell': 'SALES',
    'product_list_screen': 'SALES',

    // HR
    'time_attendance_management': 'HR',
    'list_employees': 'HR',

    // Shipping
    'shipping_order': 'SHIPPING',
  };

  /// Lấy feature name từ route name
  static String getFeatureForRoute(String routeName) {
    // Bỏ dấu / ở đầu nếu có
    String cleanRoute = routeName.startsWith('/') ? routeName.substring(1) : routeName;
    return routeToFeature[cleanRoute] ?? 'UNKNOWN';
  }

  /// Kiểm tra route có được map không
  static bool isRouteMapped(String routeName) {
    String cleanRoute = routeName.startsWith('/') ? routeName.substring(1) : routeName;
    return routeToFeature.containsKey(cleanRoute);
  }
}

/// Enum mapping screen names với features (deprecated - dùng RouteFeatureMapping)
class ScreenFeatureMapping {
  static const Map<String, String> screenToFeature = {
    // Main screens
    'HomeScreen': 'HOME',
    'TaskListPage': 'TASK_LIST',
    'TaskListScreen': 'TASK_LIST',
    'PageViewTask': 'TASK_LIST',
    'CommunicationPage': 'COMMUNICATION',
    'ChatScreen': 'COMMUNICATION',
    'ChatDetailScreen': 'COMMUNICATION',
    'ChatScreenBottomBar': 'COMMUNICATION',

    // Profile screens
    'ProfilePage': 'PROFILE',
    'ProfileManagement': 'PROFILE',
    'profile_management': 'PROFILE', // API format
    'PrivacyScreen': 'PROFILE',
    'ProfileUpdateScreen': 'PROFILE',
    'NotificationSettingScreen': 'PROFILE',
    'ProfileCatalogScreen': 'PROFILE',
    'ProfileAbilityScreen': 'PROFILE',
    'ProfileToolScreen': 'PROFILE',
    'AddProfileToolScreen': 'PROFILE',
    'ProfileDocumentScreen': 'PROFILE',
    'ListTransferTool': 'PROFILE',
    'ChangePasswordScreen': 'PROFILE',

    // Order screens
    'OrderDetailScreen': 'ORDER_MANAGEMENT',
    'OrderShippingDetailScreen': 'ORDER_MANAGEMENT',
    'RatingCustomer': 'ORDER_MANAGEMENT',
    'CreateOrderScreen': 'ORDER_MANAGEMENT',
    'CreateOrderBySaler': 'ORDER_MANAGEMENT',
    'DeploymentInformationPage': 'ORDER_MANAGEMENT',
    'SelectService': 'ORDER_MANAGEMENT',
    'SelectServiceAppointment': 'ORDER_MANAGEMENT',
    'SuccessCreateOrder': 'ORDER_MANAGEMENT',
    'OrderChangePackagePage': 'ORDER_MANAGEMENT',
    'ListProductUse': 'ORDER_MANAGEMENT',
    'ListProductUseInvestigation': 'ORDER_MANAGEMENT',
    'EditProductUse': 'ORDER_MANAGEMENT',

    // Inventory screens
    'PersonalInventoryPage': 'INVENTORY',
    'ListConfirmationBillPage': 'INVENTORY',
    'DetailBillPage': 'INVENTORY',
    'CreateTransWareHousePage': 'INVENTORY',
    'BillTransMySelfPage': 'INVENTORY',
    'DeliveryBillMenuScreen': 'INVENTORY',
    'DetailSupplyWareHouse': 'INVENTORY',
    'CreateGoodsDeliveryNotePage': 'INVENTORY',

    // Customer screens
    'DetailCustomerPage': 'CUSTOMER_360',
    'ListSurveyPage': 'CUSTOMER_360',
    'DetailSurveyScreen': 'CUSTOMER_360',
    'ListContractPage': 'CUSTOMER_360',
    'DetailContractScreen': 'CUSTOMER_360',
    'ListTicketPage': 'CUSTOMER_360',
    'DetailTicketScreen': 'CUSTOMER_360',
    'ListWarrantPage': 'CUSTOMER_360',
    'DetailWarrantScreen': 'CUSTOMER_360',
    'DetailWarrantHistoryScreen': 'CUSTOMER_360',

    // Transaction & Financial
    'TransactionScreen': 'FINANCIAL',
    'ViettelPayScreen': 'FINANCIAL',
    'DebtScreen': 'FINANCIAL',
    'Invoice': 'FINANCIAL',
    'ViewInvoiceScreen': 'FINANCIAL',
    'NavigationInvoiceScreen': 'FINANCIAL',
    'RequestBudgetScreen': 'FINANCIAL',
    'OnePayView': 'FINANCIAL',

    // Reports & Statistics
    'EditedOrderScreen': 'REPORTS',
    'SoldOrderScreen': 'REPORTS',
    'AutoDistributionScreen': 'REPORTS',

    // System & Support
    'ReportSystemError': 'SYSTEM',
    'ReportSystemErrorDetail': 'SYSTEM',
    'CreateReportSystemError': 'SYSTEM',
    'EditReportSystemError': 'SYSTEM',
    'ReportSystemErrorCreated': 'SYSTEM',
    'ContributeIdea': 'SYSTEM',
    'CreateContributeIdea': 'SYSTEM',
    'ContributeIdeaDetail': 'SYSTEM',

    // Investigation & Explanation
    'InvestigationDetailScreen': 'INVESTIGATION',
    'InvestigationWaitingDetailScreen': 'INVESTIGATION',
    'CreateInvestigation': 'INVESTIGATION',
    'ExplanationDetailScreen': 'INVESTIGATION',
    'ExplanationHistory': 'INVESTIGATION',
    'Explanation': 'INVESTIGATION',

    // Questions & Surveys
    'QuestionExaminedScreen': 'SURVEY',
    'EmployeeInvestigationDetailScreen': 'SURVEY',
    'ListQuestion': 'SURVEY',
    'DetailQuestion': 'SURVEY',

    // Notifications
    'NotificationScreen': 'NOTIFICATION',
    'DetailNotificationScreen': 'NOTIFICATION',

    // Media & Documents
    'VideoScreen': 'MEDIA',
    'ViewImage': 'MEDIA',
    'ViewListImage': 'MEDIA',
    'ViewDocumentFileCim': 'MEDIA',
    'WebViewScreen': 'MEDIA',

    // Complain & Tickets
    'ComplainTicketDetail': 'COMPLAIN',
    'CoordinatScreen': 'COMPLAIN',
    'ExplainHistoryScreen': 'COMPLAIN',
    'CimExplainHistoryScreen': 'COMPLAIN',
    'ConplainHistoryScreen': 'COMPLAIN',
    'ContactHistoryScreen': 'COMPLAIN',
    'CreateTicketScreen': 'COMPLAIN',

    // Mission & Tasks
    'MissionDetail': 'MISSION',
    'TaskPlan': 'MISSION',
    'MissionList': 'MISSION',

    // Sales & Purchase
    'CreateSaleGroup': 'SALES',
    'DetailSaleGroup': 'SALES',
    'Censorship': 'SALES',
    'ProposedPurchaseScreen': 'SALES',
    'ProposedPurchaseCreate': 'SALES',
    'SalePointOrderDetailScreen': 'SALES',
    'CreateSalesSlipScreen': 'SALES',
    'ProductListScreen': 'SALES',

    // HR & Management
    'TimeSheetManagement': 'HR',
    'ListEmployeeScreen': 'HR',

    // Shipping
    'ShippingOrderScreen': 'SHIPPING',

    // Other
    'NotFoundScreen': 'SYSTEM',
  };
  
  /// Lấy feature name từ screen name
  static String getFeatureForScreen(String screenName) {
    return screenToFeature[screenName] ?? 'UNKNOWN';
  }
  
  /// Kiểm tra screen có được map không
  static bool isScreenMapped(String screenName) {
    return screenToFeature.containsKey(screenName);
  }
}
