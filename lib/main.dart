import 'dart:async';

import 'package:animations/animations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart' as DotEnv;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:geolocator/geolocator.dart';
import 'package:home_care_partner/constants/app_theme.dart';
import 'package:home_care_partner/constants/assets.dart';
import 'package:home_care_partner/constants/environments.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/constants/time_message.dart';
import 'package:home_care_partner/data/sharedpref/constants/preferences.dart';
import 'package:home_care_partner/di/components/app_component.dart';
import 'package:home_care_partner/di/modules/firebase_module.dart';
import 'package:home_care_partner/di/modules/local_module.dart';
import 'package:home_care_partner/di/modules/network_module.dart';
import 'package:home_care_partner/di/modules/preference_module.dart';
import 'package:home_care_partner/models/auth/auth.arg.dart';
import 'package:home_care_partner/routes.dart';
import 'package:home_care_partner/services/dynamic_link_service.dart';
import 'package:home_care_partner/services/navigation_service.dart';
import 'package:home_care_partner/services/alert_manager_service.dart';
import 'package:home_care_partner/stores/chat/chat_store.dart';
import 'package:home_care_partner/stores/complain/compain_store.dart';
import 'package:home_care_partner/stores/config_setting/config_setting_store.dart';
import 'package:home_care_partner/stores/customer_360/customer_360_store.dart';
import 'package:home_care_partner/stores/entry_store/entry_store.dart';
import 'package:home_care_partner/stores/ga/ga_store.dart';
import 'package:home_care_partner/stores/home/<USER>';
import 'package:home_care_partner/stores/investigation/investigation_store.dart';
import 'package:home_care_partner/stores/invoice/invoice_store.dart';
import 'package:home_care_partner/stores/language/language_store.dart';
import 'package:home_care_partner/stores/notification/notification_store.dart';
import 'package:home_care_partner/stores/order/coupon_store.dart';
import 'package:home_care_partner/stores/order/create_order_store.dart';
import 'package:home_care_partner/stores/order/request_budget_store.dart';
import 'package:home_care_partner/stores/order/sale_point_order_store.dart';
import 'package:home_care_partner/stores/order/supply_store.dart';
import 'package:home_care_partner/stores/order/order_store.dart';
import 'package:home_care_partner/stores/order/reason_store.dart';
import 'package:home_care_partner/stores/order/service_store.dart';
import 'package:home_care_partner/stores/order/catalog_store.dart';
import 'package:home_care_partner/stores/profile/area_store.dart';
import 'package:home_care_partner/stores/profile/partner_ability_store.dart';
import 'package:home_care_partner/stores/profile/profile_catalog.dart';
import 'package:home_care_partner/stores/profile/profile_document.dart';
import 'package:home_care_partner/stores/profile/profile_store.dart';
import 'package:home_care_partner/stores/profile/profile_tool.dart';
import 'package:home_care_partner/stores/profile/seller_store.dart';
import 'package:home_care_partner/stores/profile/sys_group_store.dart';
import 'package:home_care_partner/stores/proposed_purchase/proposed_purchase_store.dart';
import 'package:home_care_partner/stores/question/question_store.dart';
import 'package:home_care_partner/stores/report_error/report_error_store.dart';
import 'package:home_care_partner/stores/search_request/search_request_store.dart';
import 'package:home_care_partner/stores/theme/theme_store.dart';
import 'package:home_care_partner/stores/transaction/transaction_store.dart';
import 'package:home_care_partner/stores/upload/upload_store.dart';
import 'package:home_care_partner/stores/user/user_store.dart';
import 'package:home_care_partner/ui/splash/not_found.dart';
import 'package:home_care_partner/utils/locale/app_localization.dart';
import 'package:inject/inject.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timeago/timeago.dart' as timeAgo;

import 'stores/delivery_bill/delivery_bill_store.dart';
import 'stores/explanation/explanation_store.dart';
import 'stores/feed_back/feed_back_store.dart';
import 'stores/home_page/home_page_store.dart';
import 'stores/sale_group/sale_group_store.dart';
import 'stores/time_sheet/time_sheet_store.dart';

// global instance for app component
AppComponent appComponent;

// This is our global ServiceLocator
GetIt getIt = GetIt.instance;

void cacheSvg() {
  var listSvg = [
    Assets.appLogoSvg,
    Assets.emotionDissatisfied,
    Assets.emotionHappy,
    Assets.star,
    Assets.startHalf,
    Assets.startEmpty,
    Assets.iconEmptyData,
    Assets.icon404,
    Assets.iconDisconneced,
    Assets.iconNoPermission,
    Assets.iconNotFound,
  ];

  Future.wait(listSvg.map(
    (svg) => precachePicture(
      ExactAssetPicture(
        SvgPicture.svgStringDecoder,
        svg,
      ),
      null,
    ),
  ));
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print("FirebaseMessaging: ${message.data.toString()}");
  // MobileCall.handleMessage(message.data);
}

Future<void> requestPermissions() async {
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );
}

Future<void> main() async {
  await DotEnv.load(fileName: ".env");
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );
  await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
  Function originalOnError = FlutterError.onError;
  FlutterError.onError = (FlutterErrorDetails errorDetails) async {
    await FirebaseCrashlytics.instance.recordFlutterError(errorDetails);
    originalOnError(errorDetails);
  };
  requestPermissions();

  // FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage message) {
  //   if (message != null) {
  //     print("nampv FirebaseMessaging: ${message.data.toString()}");
  //     MobileCall.handleMessage(message.data);
  //   }
  // });

  // FirebaseMessaging.onMessage.listen((RemoteMessage message) {
  //   print("nampv onMessage: ${message.data.toString()}");
  //   MobileCall.handleMessage(message.data);
  // });

  // FirebaseMessaging.onMessageOpenedApp.listen((message) {
  //   print("nampv onMessageOpenedApp: ${message.data.toString()}");
  //   MobileCall.handleMessage(message.data);
  // });
  // initialize spanish locale
  timeAgo.setLocaleMessages(
    'vi',
    ViMessages(),
  );

  cacheSvg();

  runZonedGuarded(() async {
    await SentryFlutter.init((SentryFlutterOptions options) {
      options.dsn = Environments.SENTRY_DSN;
      options.environment = Environments.SENTRY_ENVIRONMENT;
      options.useNativeBreadcrumbTracking();
    });

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
        .then((_) async {
      appComponent = await AppComponent.create(
        NetworkModule(),
        LocalModule(),
        PreferenceModule(),
        FirebaseModule(),
      );
      runApp(appComponent.app);
    });
  }, (exception, stackTrace) async {
    await Sentry.captureException(exception, stackTrace: stackTrace);
    await FirebaseCrashlytics.instance.recordError(exception, stackTrace);
  });
}

@provide
class MyApp extends StatefulWidget {
  // This widget is the root of your application.
  // Create your store as a final variable in a base Widget. This works better
  // with Hot Reload than creating it directly in the `build` function.
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final ThemeStore _themeStore =
      ThemeStore(appComponent.getGeneralRepository());

  final LanguageStore _languageStore =
      LanguageStore(appComponent.getGeneralRepository());

  final ChatStore _chatStore = ChatStore(
    appComponent.getFirebaseRepository(),
    appComponent.getNotificationRepository(),
  );

  final GAStore _gaStore = GAStore(appComponent.getFirebaseRepository());

  final UserStore _userStore = UserStore(
    appComponent.getAuthRepository(),
    appComponent.getProfileRepository(),
  );

  final ProfileStore _profileStore = ProfileStore(
    appComponent.getProfileRepository(),
    appComponent.getAuthRepository(),
  );

  final ProfileCatalogStore _profileCatalogStore = ProfileCatalogStore(
    appComponent.getProfileRepository(),
  );

  final ProfileDocumentStore _profileDocumentStore = ProfileDocumentStore(
    appComponent.getProfileRepository(),
  );

  final ProfileToolStore _profileToolStore = ProfileToolStore(
    appComponent.getProfileRepository(),
  );

  final PartnerAbilityStore _partnerAbilityStore = PartnerAbilityStore(
    appComponent.getProfileRepository(),
  );

  final SellerStore _sellerStore = SellerStore(
    appComponent.getProfileRepository(),
  );

  final SysGroupStore _sysGroupStore = SysGroupStore(
    appComponent.getSysGroupRepository(),
  );

  final ConfigSettingStore _configSettingStore = ConfigSettingStore(
    appComponent.getConfigSettingRepository(),
  );

  final OrderStore _orderStore = OrderStore(
    appComponent.getOrderRepository(),
  );

  final InvoiceStore _invoiceStore = InvoiceStore(
    appComponent.getInvoiceRepository(),
  );

  final CreateOrderStore _createOrderStore = CreateOrderStore(
    appComponent.getOrderRepository(),
    appComponent.getCustomerRepository(),
    appComponent.getPackageRepository(),
    appComponent.getServiceRepository(),
    OrderStore(
      appComponent.getOrderRepository(),
    ),
  );

  final TransactionStore _transactionStore = TransactionStore(
    appComponent.getTransactionRepository(),
  );

  final NotificationStore _notificationStore = NotificationStore(
    appComponent.getNotificationRepository(),
  );

  final ReasonStore _reasonStore = ReasonStore(
    appComponent.getReasonRepository(),
  );

  final UploadStore _uploadStore = UploadStore(
    appComponent.getUploadRepository(),
  );

  final ServiceStore _serviceStore = ServiceStore(
    appComponent.getServiceRepository(),
    appComponent.getPriceRepository(),
    appComponent.getContractRepository(),
  );

  final CatalogStore _catalogStore = CatalogStore(
    appComponent.getCatalogRepository(),
  );

  final AreaStore _areaStore = AreaStore(
    appComponent.getAreaRepository(),
  );

  final SupplyStore _supplyStore = SupplyStore(
    appComponent.getSupplyRepository(),
  );

  final RequestBudgetStore _requestBudgetStore = RequestBudgetStore(
    appComponent.getRequestBudgetRepository(),
  );

  final InvestigationStore _investigationStore = InvestigationStore(
    appComponent.getInvestigationRepository(),
    appComponent.getCustomerRepository(),
  );

  final DeliveryBillStore _deliveryBillStore = DeliveryBillStore(
    appComponent.getDeliveryBillRepository(),
  );

  final HomeStore _homeStore = HomeStore(
    appComponent.getHomeRepository(),
  );

  final ReportErrorStore _reportErrorStore = ReportErrorStore(
    appComponent.getErrorSystemRepository(),
  );

  final FeedBackStore _feedBackStore = FeedBackStore(
    appComponent.getFeedBackRepository(),
  );

  final QuestionStore _questionStore = QuestionStore(
    appComponent.getQuestionRepository(),
  );

  final TimeSheetStore _timesheetStore = TimeSheetStore(
    appComponent.getTimeSheetRepository(),
  );

  final ComplainStore _complainStore = ComplainStore(
    appComponent.getComplainRepository(),
  );

  final EntryStore _entryStore = EntryStore(
  );

  final SaleGroupStore _saleGroupStore = SaleGroupStore(
     appComponent.getSaleGroupRepository(),
  );

  final HomePageStore _homePageStore =
    HomePageStore(appComponent.getHomePageRepository());

  final SearchRequestStore _searchRequestStore = SearchRequestStore(
    appComponent.getSearchRequestRepository(),
  );

  final ProposedPurchaseStore _proposedPurchaseStore = ProposedPurchaseStore(
     appComponent.getProposedPurchaseRepository(),
  );

  final ExplanationStore _explanationStore = ExplanationStore(
     appComponent.getExplanationRepository(),
  );

  final Customer360Store _customer360store = Customer360Store(
    appComponent.getCustomer360Repository(),
  );

  final SalePointOrderStore _salePointOrderStore = SalePointOrderStore(
    appComponent.getSalePointOrderRepository(),
  );

  final CouponStore _couponStore = CouponStore(
    appComponent.getOrderRepository(),
  );

  Timer _timerUserLocation;
  Timer _timerOrder;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    repeatJob();
  }

  @override
  void initState() {
    super.initState();
    initDynamicLink();
    initPlatformState();
    _gaStore.getAnalyticsObserver();
  }

  void setUpLocator() {
    getIt.registerLazySingleton(() => NavigationService());
    getIt.registerSingleton<UserStore>(
      _userStore,
      signalsReady: true,
    );
    getIt.registerSingleton<NotificationStore>(
      _notificationStore,
      signalsReady: true,
    );
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initPlatformState() async {
    if (!mounted) return;

    _saveOneNotificationsPopup();

    setUpLocator();

    OneSignal.shared.setLogLevel(OSLogLevel.verbose, OSLogLevel.none);

    OneSignal.shared.setRequiresUserPrivacyConsent(true);

    var settings = {
      OSiOSSettings.autoPrompt: false,
      OSiOSSettings.promptBeforeOpeningPushUrl: false,
      OSiOSSettings.inAppLaunchUrl: false
    };

    OneSignal.shared
        .setNotificationReceivedHandler((OSNotification notification) {
      print(
          "Received notification: \n${notification.jsonRepresentation().replaceAll("\\n", "\n")}");
    });

    OneSignal.shared
        .setNotificationOpenedHandler((OSNotificationOpenedResult result) {
      print('[notification_service - _handleNotificationOpened()');
      print(
          "Opened notification: ${result.notification.jsonRepresentation().replaceAll("\\n", "\n")}");

      // Since the only thing we can get current are new Alerts -- go to the Alert screen
      print(
          'result.notification.payload.launchUrl---------- ${result.notification.payload.launchUrl}');
      Uri parse = Uri.parse(result.notification.payload.launchUrl);
      print("AFTER PARSE IS ${parse.host}");
      print("AFTER PARSE IS ${parse.path}");
      print("AFTER PARSE IS ${parse.queryParameters.toString()}");

      var link = result.notification.payload.launchUrl;

      DeeplinkRoute(link).redirect();
    });

    OneSignal.shared
        .setInAppMessageClickedHandler((OSInAppMessageAction action) {
      print(
          "In App Message Clicked: \n${action.jsonRepresentation().replaceAll("\\n", "\n")}");
    });

    OneSignal.shared
        .setSubscriptionObserver((OSSubscriptionStateChanges changes) {
      print("SUBSCRIPTION STATE CHANGED: ${changes.jsonRepresentation()}");

      _handleGetPlayId();
    });

    OneSignal.shared.setPermissionObserver((OSPermissionStateChanges changes) {
      print("PERMISSION STATE CHANGED: ${changes.jsonRepresentation()}");
    });

    // OneSignal.shared.setEmailSubscriptionObserver(
    //         (OSEmailSubscriptionStateChanges changes) {
    //       print("EMAIL SUBSCRIPTION STATE CHANGED ${changes.jsonRepresentation()}");
    //     });

    // NOTE: Replace with your own app ID from https://www.onesignal.com
    await OneSignal.shared
        .init(Environments.oneSignalAppId, iOSSettings: settings);

    OneSignal.shared
        .setInFocusDisplayType(OSNotificationDisplayType.notification);

    OneSignal.shared.consentGranted(true);

    // await OneSignal.shared.setRequiresUserPrivacyConsent(false);

    await OneSignal.shared.requiresUserPrivacyConsent().then((accepted) {
      print("Requires user privacy consent: $accepted");
    });

    await OneSignal.shared
        .promptUserForPushNotificationPermission()
        .then((accepted) {
      print("Accepted permission: $accepted");
      // _handleGetPlayId();
    });

    // Some examples of how to use In App Messaging public methods with OneSignal SDK
    // oneSignalInAppMessagingTriggerExamples();

    // Some examples of how to use Outcome Events public methods with OneSignal SDK
    // oneSignalOutcomeEventsExamples();
  }

  void _handleGetPlayId() async {
    SharedPreferences.getInstance().then((prefs) async {
      var status = await OneSignal.shared.getPermissionSubscriptionState();

      var playerId = status.subscriptionStatus.userId;

      print('playerId------------------------ $playerId');

      prefs.setString(Preferences.one_signal_playid, playerId);
    });
  }

  void _saveOneNotificationsPopup() async {
    SharedPreferences.getInstance().then((prefs) async {
      prefs.setInt(Preferences.one_notifications_popup, 1);
    });
  }

  @override
  void dispose() {
    _timerUserLocation.cancel();
    _timerOrder.cancel();
    super.dispose();
  }

  void repeatJob() {
    if (_timerUserLocation != null) {
      _timerUserLocation.cancel();
    }
    // _timerUserLocation = Timer.periodic(
    //   Duration(
    //     minutes: 1,
    //   ),
    //   (Timer t) => updateUserLocation(),
    // );

    if (_timerOrder != null) {
      _timerOrder.cancel();
    }
    // _timerOrder = Timer.periodic(
    //   Duration(
    //     seconds: GET_ORDER_REPEAT_TIME,
    //   ),
    //   (Timer t) => getNewOrder(),
    // );
  }

  void updateUserLocation() async {
    if ( _userStore.isLoggedIn != true) return;
    var _permissionGranted = await Geolocator.checkPermission();
    if (_permissionGranted == LocationPermission.always ||
        _permissionGranted == LocationPermission.whileInUse) {
      var _position = await Geolocator.getCurrentPosition();

      var isActive = _userStore.user?.isActive == true;

      await _userStore.updateActiveStatus(
        AuthUpdateActiveStatusInput(
          isActive: isActive,
          latitude: _position.latitude,
          longitude: _position.longitude,
        ),
      );
    }
  }

  void getNewOrder() async {
    print("HOME_SERVICE");
    log("HOME_SERVICE");

    if (_userStore.isLoggedIn != true) return;
    await _notificationStore.fetchNewOrder();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<ThemeStore>(create: (_) => _themeStore),
        Provider<LanguageStore>(create: (_) => _languageStore),
        Provider<ChatStore>(
          create: (_) => _chatStore,
        ),
        Provider<GAStore>(create: (_) => _gaStore),
        Provider<UserStore>(create: (_) => _userStore),
        Provider<ProfileStore>(
          create: (_) => _profileStore,
        ),
        Provider<ProfileCatalogStore>(
          create: (_) => _profileCatalogStore,
        ),
        Provider<ProfileDocumentStore>(
          create: (_) => _profileDocumentStore,
        ),
        Provider<ProfileToolStore>(
          create: (_) => _profileToolStore,
        ),
        Provider<PartnerAbilityStore>(
          create: (_) => _partnerAbilityStore,
        ),
        Provider<SysGroupStore>(
          create: (_) => _sysGroupStore,
        ),
        Provider<ConfigSettingStore>(
          create: (_) => _configSettingStore,
        ),
        Provider<OrderStore>(
          create: (_) => _orderStore,
        ),
        Provider<InvoiceStore>(
          create: (_) => _invoiceStore,
        ),
        Provider<CreateOrderStore>(
          create: (_) => _createOrderStore,
        ),
        Provider<TransactionStore>(
          create: (_) => _transactionStore,
        ),
        Provider<NotificationStore>(
          create: (_) => _notificationStore,
        ),
        Provider<ReasonStore>(
          create: (_) => _reasonStore,
        ),
        Provider<UploadStore>(
          create: (_) => _uploadStore,
        ),
        Provider<ServiceStore>(
          create: (_) => _serviceStore,
        ),
        Provider<CatalogStore>(
          create: (_) => _catalogStore,
        ),
        Provider<AreaStore>(
          create: (_) => _areaStore,
        ),
        Provider<SupplyStore>(
          create: (_) => _supplyStore,
        ),
        Provider<SellerStore>(
          create: (_) => _sellerStore,
        ),
        Provider<RequestBudgetStore>(
          create: (_) => _requestBudgetStore,
        ),
        Provider<InvestigationStore>(
          create: (_) => _investigationStore,
        ),
        Provider<DeliveryBillStore>(
          create: (_) => _deliveryBillStore,
        ),
        Provider<HomeStore>(
          create: (_) => _homeStore,
        ),
        Provider<ReportErrorStore>(
          create: (_) => _reportErrorStore,
        ),
        Provider<FeedBackStore>(
          create: (_) => _feedBackStore,
        ),
        Provider<QuestionStore>(
          create: (_) => _questionStore,
        ),
        Provider<EntryStore>(
          create: (_) => _entryStore,
        ),
        Provider<SaleGroupStore>(
          create: (_) => _saleGroupStore,
        ),
        Provider<TimeSheetStore>(
          create: (_) => _timesheetStore,
        ),
        Provider<SearchRequestStore>(
          create: (_) => _searchRequestStore,
        ),
        Provider<ProposedPurchaseStore>(
          create: (_) => _proposedPurchaseStore,
        ),
        Provider<ComplainStore>(
          create: (_) => _complainStore,
        ),
        Provider<ExplanationStore>(
          create: (_) => _explanationStore,
        ),
        Provider<Customer360Store>(
          create: (_) => _customer360store,
        ),
        Provider<SalePointOrderStore>(
          create: (_) => _salePointOrderStore,
        ),
        Provider<CouponStore>(
          create: (_) => _couponStore,
        ),
      ],
      child: Observer(
        name: 'global-observer',
        builder: (context) {
          return RefreshConfiguration(
            footerTriggerDistance: 15,
            dragSpeedRatio: 0.91,
            headerBuilder: () => MaterialClassicHeader(),
            footerBuilder: () => ClassicFooter(),
            enableLoadingWhenNoData: false,
            enableRefreshVibrate: false,
            enableLoadMoreVibrate: false,
            shouldFooterFollowWhenNotFull: (state) {
              // If you want load more with noMoreData state ,may be you should return false
              return false;
            },
            child: AlertManagerProvider(
              child: MaterialApp(
                debugShowCheckedModeBanner: false,
                title: Strings.appName,
                theme: _themeStore.darkMode ? themeDataDark : themeData,
                navigatorKey: getIt<NavigationService>().navigatorKey,
              // routes: Routes.routes,
              navigatorObservers: [
                // _gaStore.analyticsObserver,
                SentryNavigatorObserver(),
              ],
              onGenerateRoute: (routeSettings) {
                final String routeName = routeSettings.name;
                return PageRouteBuilder(
                  settings: routeSettings,
                  reverseTransitionDuration: Duration(
                    milliseconds: 500,
                  ),
                  transitionDuration: Duration(
                    milliseconds: 500,
                  ),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return SharedAxisTransition(
                      child: child,
                      animation: animation,
                      secondaryAnimation: secondaryAnimation,
                      transitionType: SharedAxisTransitionType.horizontal,
                    );
                  },
                  pageBuilder: (context, animation, secondaryAnimation) {
                    _gaStore.setCurrentScreen(
                        screenName: 'WORKER_ROUTE___$routeName');
                    return Routes.routes[routeName] != null
                        ? Routes.routes[routeName](context)
                        : NotFoundScreen();
                  },
                );
              },
              locale: Locale(_languageStore.locale),
              supportedLocales: _languageStore.supportedLanguages
                  .map((language) => Locale(language.locale, language.code))
                  .toList(),
              localizationsDelegates: [
                RefreshLocalizations.delegate,
                // A class which loads the translations from JSON files
                AppLocalizations.delegate,
                // Built-in localization of basic text for Material widgets
                GlobalMaterialLocalizations.delegate,
                // Built-in localization for text direction LTR/RTL
                GlobalWidgetsLocalizations.delegate,
                // Built-in localization of basic text for Cupertino widgets
                GlobalCupertinoLocalizations.delegate,
              ],
              initialRoute: Routes.splash,
              // home: _userStore.isLoggedIn ? HomeScreen() : LoginScreen(),
              ),
            ),
          );
        },
      ),
    );
  }
}
initDynamicLink(){
  DynamicLinksService.initDynamicLinks((deepLink) {
    handleReceiveDynamicLink(deepLink);
  });
}
