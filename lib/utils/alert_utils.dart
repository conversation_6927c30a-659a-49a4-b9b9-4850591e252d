import 'package:flutter/foundation.dart';
import 'package:home_care_partner/services/alert_manager_service.dart';

/// Utility class để dễ dàng gọi alert functions từ bất kỳ đâu
class AlertUtils {
  /// Initialize alerts (gọi khi app khởi động hoặc user login)
  static Future<void> initialize() async {
    try {
      await AlertManagerService.instance.initialize();
      debugPrint('AlertUtils: Initialized successfully');
    } catch (e) {
      debugPrint('AlertUtils: Error initializing: $e');
    }
  }

  /// Refresh alerts từ API (gọi khi cần force refresh)
  static Future<void> refresh() async {
    try {
      await AlertManagerService.instance.refreshAlerts();
      debugPrint('AlertUtils: Refreshed successfully');
    } catch (e) {
      debugPrint('AlertUtils: Error refreshing: $e');
    }
  }

  /// Fetch alerts nếu cần (gọ<PERSON> định kỳ hoặc khi vào app)
  static Future<void> fetchIfNeeded() async {
    try {
      await AlertManagerService.instance.fetchAlertsIfNeeded();
      debugPrint('AlertUtils: Fetch if needed completed');
    } catch (e) {
      debugPrint('AlertUtils: Error fetching: $e');
    }
  }

  /// Force refresh alerts (gọi khi user pull to refresh)
  static Future<void> forceRefresh() async {
    try {
      await AlertManagerService.instance.fetchAlertsIfNeeded(forceRefresh: true);
      debugPrint('AlertUtils: Force refresh completed');
    } catch (e) {
      debugPrint('AlertUtils: Error force refreshing: $e');
    }
  }

  /// Clear tất cả alerts (gọi khi user logout)
  static Future<void> clear() async {
    try {
      await AlertManagerService.instance.clearAll();
      debugPrint('AlertUtils: Cleared successfully');
    } catch (e) {
      debugPrint('AlertUtils: Error clearing: $e');
    }
  }

  /// Lấy trạng thái hiện tại
  static Map<String, dynamic> getStatus() {
    return AlertManagerService.instance.getStatus();
  }

  /// Kiểm tra có đang loading không
  static bool get isLoading => AlertManagerService.instance.isLoading;

  /// Lấy thời gian fetch cuối cùng
  static DateTime get lastFetchTime => AlertManagerService.instance.lastFetchTime;

  /// Print debug info
  static void printDebugInfo() {
    final status = getStatus();
    debugPrint('=== AlertUtils Debug Info ===');
    debugPrint('Is Loading: ${status['isLoading']}');
    debugPrint('Last Fetch: ${status['lastFetchTime']}');
    debugPrint('Should Fetch: ${status['shouldFetch']}');
    debugPrint('Feature Blocking Info: ${status['featureBlockingInfo']}');
    debugPrint('=============================');
  }
}

/// Extension để dễ dàng gọi alert functions từ StatefulWidget
extension AlertUtilsExtension on State {
  /// Initialize alerts
  Future<void> initializeAlerts() => AlertUtils.initialize();

  /// Refresh alerts
  Future<void> refreshAlerts() => AlertUtils.refresh();

  /// Fetch alerts if needed
  Future<void> fetchAlertsIfNeeded() => AlertUtils.fetchIfNeeded();

  /// Force refresh alerts
  Future<void> forceRefreshAlerts() => AlertUtils.forceRefresh();

  /// Clear alerts
  Future<void> clearAlerts() => AlertUtils.clear();

  /// Get alert status
  Map<String, dynamic> getAlertStatus() => AlertUtils.getStatus();

  /// Print alert debug info
  void printAlertDebugInfo() => AlertUtils.printDebugInfo();
}

/// Mixin để tự động quản lý alerts trong widget
mixin AutoAlertMixin<T extends StatefulWidget> on State<T> {
  bool _autoInitialized = false;

  @override
  void initState() {
    super.initState();
    _autoInitializeAlerts();
  }

  Future<void> _autoInitializeAlerts() async {
    if (_autoInitialized) return;
    
    try {
      await AlertUtils.fetchIfNeeded();
      _autoInitialized = true;
    } catch (e) {
      debugPrint('AutoAlertMixin: Error auto initializing: $e');
    }
  }

  /// Override để tùy chỉnh khi nào fetch alerts
  bool shouldAutoFetchAlerts() => true;

  /// Override để tùy chỉnh interval fetch
  Duration get autoFetchInterval => Duration(minutes: 30);
}

/// Widget helper để hiển thị trạng thái loading của alerts
class AlertLoadingIndicator extends StatefulWidget {
  final Widget child;
  final Widget loadingWidget;
  final bool showLoadingOverlay;

  const AlertLoadingIndicator({
    Key key,
    @required this.child,
    this.loadingWidget,
    this.showLoadingOverlay = false,
  }) : super(key: key);

  @override
  _AlertLoadingIndicatorState createState() => _AlertLoadingIndicatorState();
}

class _AlertLoadingIndicatorState extends State<AlertLoadingIndicator> {
  @override
  void initState() {
    super.initState();
    // Listen to loading state changes
    _startListening();
  }

  void _startListening() {
    // Có thể implement stream để listen loading state
    // Hiện tại chỉ rebuild khi setState được gọi
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = AlertUtils.isLoading;
    
    if (widget.showLoadingOverlay && isLoading) {
      return Stack(
        children: [
          widget.child,
          Container(
            color: Colors.black26,
            child: Center(
              child: widget.loadingWidget ?? CircularProgressIndicator(),
            ),
          ),
        ],
      );
    }

    return widget.child;
  }
}

/// Debug widget để hiển thị thông tin alerts
class AlertDebugWidget extends StatefulWidget {
  @override
  _AlertDebugWidgetState createState() => _AlertDebugWidgetState();
}

class _AlertDebugWidgetState extends State<AlertDebugWidget> {
  Map<String, dynamic> _status = {};

  @override
  void initState() {
    super.initState();
    _updateStatus();
  }

  void _updateStatus() {
    setState(() {
      _status = AlertUtils.getStatus();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(8),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Alert Manager Status',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text('Loading: ${_status['isLoading'] ?? false}'),
            Text('Last Fetch: ${_status['lastFetchTime'] ?? 'Never'}'),
            Text('Should Fetch: ${_status['shouldFetch'] ?? false}'),
            SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RaisedButton(
                    onPressed: () async {
                      await AlertUtils.refresh();
                      _updateStatus();
                    },
                    child: Text('Refresh'),
                    color: Colors.blue,
                    textColor: Colors.white,
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: RaisedButton(
                    onPressed: () async {
                      await AlertUtils.clear();
                      _updateStatus();
                    },
                    child: Text('Clear'),
                    color: Colors.red,
                    textColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
