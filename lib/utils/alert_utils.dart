import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:home_care_partner/services/alert_manager_service.dart';

/// Utility class để dễ dàng gọi alert functions từ bất kỳ đâu
class AlertUtils {
  /// Initialize alerts (g<PERSON>i khi app khởi động hoặc user login)
  static Future<void> initialize() async {
    try {
      await AlertManagerService.instance.initialize();
      debugPrint('AlertUtils: Initialized successfully');
    } catch (e) {
      debugPrint('AlertUtils: Error initializing: $e');
    }
  }

  /// Refresh alerts từ API (gọi khi cần force refresh)
  static Future<void> refresh() async {
    try {
      await AlertManagerService.instance.refreshAlerts();
      debugPrint('AlertUtils: Refreshed successfully');
    } catch (e) {
      debugPrint('AlertUtils: Error refreshing: $e');
    }
  }

  /// Fetch alerts nế<PERSON> cần (g<PERSON><PERSON> đ<PERSON>nh kỳ hoặc khi vào app)
  static Future<void> fetchIfNeeded() async {
    try {
      await AlertManagerService.instance.fetchAlertsIfNeeded();
      debugPrint('AlertUtils: Fetch if needed completed');
    } catch (e) {
      debugPrint('AlertUtils: Error fetching: $e');
    }
  }

  /// Force refresh alerts (gọi khi user pull to refresh)
  static Future<void> forceRefresh() async {
    try {
      await AlertManagerService.instance.fetchAlertsIfNeeded(forceRefresh: true);
      debugPrint('AlertUtils: Force refresh completed');
    } catch (e) {
      debugPrint('AlertUtils: Error force refreshing: $e');
    }
  }

  /// Clear tất cả alerts (gọi khi user logout)
  static Future<void> clear() async {
    try {
      await AlertManagerService.instance.clearAll();
      debugPrint('AlertUtils: Cleared successfully');
    } catch (e) {
      debugPrint('AlertUtils: Error clearing: $e');
    }
  }

  /// Lấy trạng thái hiện tại
  static Map<String, dynamic> getStatus() {
    return AlertManagerService.instance.getStatus();
  }

  /// Kiểm tra có đang loading không
  static bool get isLoading => AlertManagerService.instance.isLoading;

  /// Lấy thời gian fetch cuối cùng
  static DateTime get lastFetchTime => AlertManagerService.instance.lastFetchTime;

  /// Print debug info
  static void printDebugInfo() {
    final status = getStatus();
    debugPrint('=== AlertUtils Debug Info ===');
    debugPrint('Is Loading: ${status['isLoading']}');
    debugPrint('Last Fetch: ${status['lastFetchTime']}');
    debugPrint('Should Fetch: ${status['shouldFetch']}');
    debugPrint('Feature Blocking Info: ${status['featureBlockingInfo']}');
    debugPrint('=============================');
  }
}

/// Extension để dễ dàng gọi alert functions từ bất kỳ đâu
extension AlertUtilsExtension on Object {
  /// Initialize alerts
  Future<void> initializeAlerts() => AlertUtils.initialize();

  /// Refresh alerts
  Future<void> refreshAlerts() => AlertUtils.refresh();

  /// Fetch alerts if needed
  Future<void> fetchAlertsIfNeeded() => AlertUtils.fetchIfNeeded();

  /// Force refresh alerts
  Future<void> forceRefreshAlerts() => AlertUtils.forceRefresh();

  /// Clear alerts
  Future<void> clearAlerts() => AlertUtils.clear();

  /// Get alert status
  Map<String, dynamic> getAlertStatus() => AlertUtils.getStatus();

  /// Print alert debug info
  void printAlertDebugInfo() => AlertUtils.printDebugInfo();
}
