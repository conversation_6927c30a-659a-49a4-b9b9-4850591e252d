import 'package:flutter/foundation.dart';
import 'package:home_care_partner/services/alert_manager_service.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/ui/direct_app/alert_service.dart';

/// Helper class để test AlertManagerService
class AlertTestHelper {
  /// Test initialize AlertManagerService
  static Future<void> testInitialize() async {
    try {
      debugPrint('=== Testing AlertManagerService Initialize ===');
      await AlertManagerService.instance.initialize();
      debugPrint('✅ Initialize completed successfully');
    } catch (e) {
      debugPrint('❌ Initialize failed: $e');
    }
  }

  /// Test refresh alerts từ API
  static Future<void> testRefresh() async {
    try {
      debugPrint('=== Testing AlertManagerService Refresh ===');
      await AlertManagerService.instance.refreshAlerts();
      debugPrint('✅ Refresh completed successfully');
    } catch (e) {
      debugPrint('❌ Refresh failed: $e');
    }
  }

  /// Test clear alerts
  static Future<void> testClear() async {
    try {
      debugPrint('=== Testing AlertManagerService Clear ===');
      await AlertManagerService.instance.clearAll();
      debugPrint('✅ Clear completed successfully');
    } catch (e) {
      debugPrint('❌ Clear failed: $e');
    }
  }

  /// Print debug info
  static void printDebugInfo() {
    debugPrint('=== AlertManagerService Debug Info ===');
    final status = AlertManagerService.instance.getStatus();
    debugPrint('Is Loading: ${status['isLoading']}');
    debugPrint('Last Fetch: ${status['lastFetchTime']}');
    debugPrint('Should Fetch: ${status['shouldFetch']}');
    
    final alerts = FeatureBlockingService.instance.alerts;
    debugPrint('Current Alerts: ${alerts.length}');
    for (var alert in alerts) {
      debugPrint('  - ${alert.feature}: ${alert.message} (required: ${alert.isRequired})');
    }
    debugPrint('=====================================');
  }

  /// Test với dữ liệu mẫu
  static Future<void> testWithSampleData() async {
    try {
      debugPrint('=== Testing with Sample Data ===');
      
      final sampleAlerts = [
        AlertModel(
          feature: 'profile_management',
          message: 'Profile management feature alert',
          isRequired: false,
        ),
        AlertModel(
          feature: 'CREATE_ORDER',
          message: 'Create order feature alert',
          isRequired: true,
        ),
        AlertModel(
          feature: 'DEBT',
          message: 'Debt feature alert',
          isRequired: false,
        ),
      ];

      await FeatureBlockingService.instance.updateAlerts(sampleAlerts);
      debugPrint('✅ Sample data loaded: ${sampleAlerts.length} alerts');
      
      printDebugInfo();
    } catch (e) {
      debugPrint('❌ Sample data test failed: $e');
    }
  }

  /// Test route mapping
  static void testRouteMapping() {
    debugPrint('=== Testing Route Mapping ===');
    
    final testRoutes = [
      '/profile_management',
      '/create_order', 
      '/debt',
      '/home',
      '/task_list',
    ];

    for (var route in testRoutes) {
      final routeKey = route.replaceFirst('/', '');
      final alert = FeatureBlockingService.instance.getAlertForFeature(routeKey);
      
      debugPrint('Route: $route');
      debugPrint('  Route key: $routeKey');
      debugPrint('  Alert found: ${alert != null ? "✅ ${alert.message}" : "❌ None"}');
    }
    debugPrint('===============================');
  }

  /// Chạy tất cả tests
  static Future<void> runAllTests() async {
    debugPrint('🚀 Starting AlertManagerService Tests...');
    
    await testInitialize();
    await Future.delayed(Duration(seconds: 1));
    
    await testWithSampleData();
    await Future.delayed(Duration(seconds: 1));
    
    testRouteMapping();
    await Future.delayed(Duration(seconds: 1));
    
    await testRefresh();
    await Future.delayed(Duration(seconds: 1));
    
    printDebugInfo();
    
    debugPrint('🎉 All tests completed!');
  }
}

/// Extension để dễ dàng gọi test functions từ bất kỳ đâu
extension AlertTestExtension on Object {
  /// Test AlertManagerService
  Future<void> testAlertManager() => AlertTestHelper.runAllTests();
  
  /// Test initialize
  Future<void> testAlertInitialize() => AlertTestHelper.testInitialize();
  
  /// Test refresh
  Future<void> testAlertRefresh() => AlertTestHelper.testRefresh();
  
  /// Test clear
  Future<void> testAlertClear() => AlertTestHelper.testClear();
  
  /// Print debug info
  void printAlertDebug() => AlertTestHelper.printDebugInfo();
}

/// Mixin để dễ dàng test trong widgets
mixin AlertTestMixin {
  /// Test AlertManagerService
  Future<void> testAlertManager() => AlertTestHelper.runAllTests();
  
  /// Test với sample data
  Future<void> testWithSampleData() => AlertTestHelper.testWithSampleData();
  
  /// Test route mapping
  void testRouteMapping() => AlertTestHelper.testRouteMapping();
  
  /// Print debug info
  void printAlertDebug() => AlertTestHelper.printDebugInfo();
}
