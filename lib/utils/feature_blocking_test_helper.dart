import 'package:flutter/material.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';
import 'package:home_care_partner/middleware/route_guard_middleware.dart';
import 'package:home_care_partner/ui/direct_app/alert_service.dart';

/// Helper class để test và debug feature blocking
class FeatureBlockingTestHelper {
  static FeatureBlockingTestHelper? _instance;
  static FeatureBlockingTestHelper get instance {
    _instance ??= FeatureBlockingTestHelper._internal();
    return _instance!;
  }

  FeatureBlockingTestHelper._internal();

  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;
  final RouteGuardMiddleware _routeGuardMiddleware = RouteGuardMiddleware.instance;

  /// Test với dữ liệu mẫu
  Future<void> testWithSampleData() async {
    print('=== Feature Blocking Test ===');
    
    // Tạo dữ liệu test
    final sampleAlerts = [
      AlertModel(
        feature: 'HOME',
        message: 'Trang chủ đang bảo trì',
        isRequired: true, // Chặn
      ),
      AlertModel(
        feature: 'TASK_LIST',
        message: 'Danh sách công việc đang cập nhật',
        isRequired: true, // Chặn
      ),
      AlertModel(
        feature: 'COMMUNICATION',
        message: 'Có cập nhật mới cho tính năng liên lạc',
        isRequired: false, // Không chặn, chỉ thông báo
      ),
    ];

    // Cập nhật blocked features
    await _featureBlockingService.updateBlockedFeatures(sampleAlerts);
    
    // Test kiểm tra blocking
    _testFeatureBlocking();
    
    // Test route access
    _testRouteAccess();
    
    print('=== Test completed ===');
  }

  /// Test chặn ALL features
  Future<void> testBlockAllFeatures() async {
    print('=== Test Block ALL Features ===');
    
    final blockAllAlert = [
      AlertModel(
        feature: 'ALL',
        message: 'Ứng dụng đang bảo trì',
        isRequired: true,
      ),
    ];

    await _featureBlockingService.updateBlockedFeatures(blockAllAlert);
    
    _testFeatureBlocking();
    _testRouteAccess();
    
    print('=== Block ALL Test completed ===');
  }

  /// Test không có feature nào bị chặn
  Future<void> testNoBlockedFeatures() async {
    print('=== Test No Blocked Features ===');
    
    final noBlockAlerts = [
      AlertModel(
        feature: 'COMMUNICATION',
        message: 'Thông báo thông thường',
        isRequired: false,
      ),
    ];

    await _featureBlockingService.updateBlockedFeatures(noBlockAlerts);
    
    _testFeatureBlocking();
    _testRouteAccess();
    
    print('=== No Block Test completed ===');
  }

  /// Test kiểm tra feature blocking
  void _testFeatureBlocking() {
    print('\n--- Feature Blocking Status ---');
    
    final testFeatures = [
      'HOME',
      'TASK_LIST',
      'ORDER_MANAGEMENT',
      'COMMUNICATION',
      'PROFILE',
      'ALL',
    ];

    for (String feature in testFeatures) {
      final isBlocked = _featureBlockingService.isFeatureBlocked(feature);
      print('$feature: ${isBlocked ? "BLOCKED" : "ALLOWED"}');
    }
    
    print('Blocked features: ${_featureBlockingService.blockedFeatures}');
  }

  /// Test kiểm tra route access
  void _testRouteAccess() {
    print('\n--- Route Access Status ---');
    
    final testRoutes = [
      '/home',
      '/task_list',
      '/my_order',
      '/communication',
      '/profile',
      '/messages',
      '/login', // Route không bị chặn
    ];

    for (String route in testRoutes) {
      final canAccess = _routeGuardMiddleware.canAccessRoute(route);
      print('$route: ${canAccess ? "ACCESSIBLE" : "BLOCKED"}');
    }
  }

  /// Lấy thông tin debug chi tiết
  Map<String, dynamic> getDebugInfo() {
    return {
      'featureBlockingService': _featureBlockingService.getBlockingInfo(),
      'routeGuardMiddleware': _routeGuardMiddleware.getDebugInfo(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// In thông tin debug
  void printDebugInfo() {
    print('\n=== DEBUG INFO ===');
    final debugInfo = getDebugInfo();
    
    print('Feature Blocking Service:');
    final fbsInfo = debugInfo['featureBlockingService'] as Map<String, dynamic>;
    fbsInfo.forEach((key, value) {
      print('  $key: $value');
    });
    
    print('\nRoute Guard Middleware:');
    final rgmInfo = debugInfo['routeGuardMiddleware'] as Map<String, dynamic>;
    rgmInfo.forEach((key, value) {
      print('  $key: $value');
    });
    
    print('\nTimestamp: ${debugInfo['timestamp']}');
    print('=== END DEBUG INFO ===\n');
  }

  /// Clear tất cả blocked features
  Future<void> clearAllBlocks() async {
    await _featureBlockingService.clearCache();
    print('All blocks cleared');
  }

  /// Test với context để hiển thị dialog
  void testBlockedDialog(BuildContext context, String route) {
    if (!_routeGuardMiddleware.canAccessRoute(route)) {
      _routeGuardMiddleware.handleBlockedRoute(context, route);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Route $route is accessible'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// Test navigation với guard
  Future<void> testGuardedNavigation(BuildContext context, String route) async {
    final success = await context.guardedPushNamed(route);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(success 
          ? 'Navigation to $route successful' 
          : 'Navigation to $route blocked'),
        backgroundColor: success ? Colors.green : Colors.red,
      ),
    );
  }
}

/// Widget để test feature blocking trong UI
class FeatureBlockingTestWidget extends StatefulWidget {
  @override
  _FeatureBlockingTestWidgetState createState() => _FeatureBlockingTestWidgetState();
}

class _FeatureBlockingTestWidgetState extends State<FeatureBlockingTestWidget> {
  final FeatureBlockingTestHelper _testHelper = FeatureBlockingTestHelper.instance;
  String _debugInfo = '';

  @override
  void initState() {
    super.initState();
    _updateDebugInfo();
  }

  void _updateDebugInfo() {
    setState(() {
      _debugInfo = _testHelper.getDebugInfo().toString();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Feature Blocking Test'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Feature Blocking Test Panel',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            
            // Test buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () async {
                    await _testHelper.testWithSampleData();
                    _updateDebugInfo();
                  },
                  child: Text('Test Sample Data'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    await _testHelper.testBlockAllFeatures();
                    _updateDebugInfo();
                  },
                  child: Text('Test Block ALL'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    await _testHelper.testNoBlockedFeatures();
                    _updateDebugInfo();
                  },
                  child: Text('Test No Blocks'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    await _testHelper.clearAllBlocks();
                    _updateDebugInfo();
                  },
                  child: Text('Clear All'),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Route test buttons
            Text('Test Routes:', style: Theme.of(context).textTheme.titleMedium),
            SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () => _testHelper.testBlockedDialog(context, '/home'),
                  child: Text('Test /home'),
                ),
                ElevatedButton(
                  onPressed: () => _testHelper.testBlockedDialog(context, '/task_list'),
                  child: Text('Test /task_list'),
                ),
                ElevatedButton(
                  onPressed: () => _testHelper.testBlockedDialog(context, '/profile'),
                  child: Text('Test /profile'),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Debug info
            Text('Debug Info:', style: Theme.of(context).textTheme.titleMedium),
            SizedBox(height: 8),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _debugInfo,
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
