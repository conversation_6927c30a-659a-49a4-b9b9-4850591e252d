import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/svg.dart';
import 'package:home_care_partner/constants/assets.dart';
import 'package:home_care_partner/constants/colors.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/font_size.dart';
import 'package:home_care_partner/constants/spacing.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/constants/style.dart';
import 'package:home_care_partner/models/investigation/args/investigation.arg.dart';
import 'package:home_care_partner/stores/investigation/investigation_store.dart';
import 'package:home_care_partner/ui/direct_app/alert_checker_widget.dart';
import 'package:home_care_partner/widgets/card/employee_investigation_card.dart';
import 'package:home_care_partner/widgets/empty_widget.dart';
import 'package:home_care_partner/widgets/infinity_scroll.dart';
import 'package:home_care_partner/widgets/skeleton/skeleton_list.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class EmployeeInvestigationList extends StatefulWidget {
  final EmployeeInvestigationFilterArgs employeeListFilterArgs;

  const EmployeeInvestigationList({
    this.employeeListFilterArgs,
  });

  @override
  _EmployeeInvestigationListState createState() =>
      _EmployeeInvestigationListState();
}

class _EmployeeInvestigationListState extends State<EmployeeInvestigationList>
    with AutomaticKeepAliveClientMixin<EmployeeInvestigationList> {
  InvestigationStore _investigationStore;

  RefreshController _refreshController =
      RefreshController(initialRefresh: true);


  TextEditingController _keywordController = TextEditingController();

  Timer _debounce;

  String _keyword = '';


  List<EmployeeStatusInvestigationStatusModel> _statusTypes;

  EmployeeStatusInvestigation _currentEmployeeInvestigationStatus;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    _investigationStore = Provider.of<InvestigationStore>(context);
    _keywordController.addListener(_onSearchChanged);
    // _onRefresh();

    _statusTypes = [
      EmployeeStatusInvestigationStatusModel(
        statusLabel: 'Tất cả',
        icon: Assets.icSearchAll,
        status: null,
      ),
      EmployeeStatusInvestigationStatusModel(
        statusLabel: '${Strings.statusWaitingInvestigate}',
        icon: Assets.icClock,
        status: EmployeeStatusInvestigation.Waiting,
      ),
      EmployeeStatusInvestigationStatusModel(
        statusLabel: '${Strings.statusInvestigating}',
        icon: Assets.icCalendar2,
        status: EmployeeStatusInvestigation.Processing,
      ),
      EmployeeStatusInvestigationStatusModel(
        statusLabel: '${Strings.statusCompletedForInvestigate}',
        icon: Assets.icCalculator,
        status: EmployeeStatusInvestigation.Completed,
      ),
    ];
  }

  @override
  void dispose() async {
    super.dispose();
    _keywordController.removeListener(_onSearchChanged);
    _keywordController.dispose();
    _investigationStore.listEmployeeInvestigation.clear();
  }

  _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce.cancel();
    _debounce = Timer(
      const Duration(milliseconds: 750),
      () {
        if (_keyword != _keywordController.text) {
          FocusScope.of(context).unfocus();
          _onRefresh();
          _keyword = _keywordController.text;
        }
      },
    );
  }

  void _onRefresh() async {
    _investigationStore.listEmployeeInvestigation.clear();
    await _investigationStore.getInitialListEmployeeInvestigation(
      clear: true,
      status: _currentEmployeeInvestigationStatus,
      keyword: _keywordController.text,
    );

    _refreshController.refreshCompleted(resetFooterState: true);
  }

  void _onLoading() async {
    _refreshController.requestLoading();
    if (_investigationStore.maxToLoadMoreInvestigation == false) {
      await _investigationStore.getInitialListEmployeeInvestigation(
          clear: false,
          status: _currentEmployeeInvestigationStatus,
          keyword: _keywordController.text);
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Observer(
      builder: (BuildContext context) {
        return Scaffold(
          body: InfinityScroll(
            refreshController: _refreshController,
            onLoading: _onLoading,
            onRefresh: _onRefresh,
            child: Column(
              children: [
                // AlertChecker(
                //   features: 'SURVEY',
                // ),
                _buildFilter(),
                _renderInvestigationEmployeeList(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _renderInvestigationEmployeeList() {
    if (_investigationStore.loadingListInvestigation) {
      return SkeletonList();
    } else {
      if (_investigationStore.listEmployeeInvestigation.length == 0) {
        return EmptyDataWidget(
          state: StatePage.NotFound,
          title: Strings.emptyInvestigation,
        );
      } else {
        return ListView.separated(
          scrollDirection: Axis.vertical,
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            return EmployeeInvestigationCard(
              investigationEmployee:
                  _investigationStore.listEmployeeInvestigation[index],
              callBack: (){
                _onRefresh();
              },
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return 0.height;
          },
          itemCount: _investigationStore.listEmployeeInvestigation.length,
        );
      }
    }
  }

  Widget _buildFilter() {
    return Column(
      children: [
        searchBox(),
        quickFilter(),
      ],
    );
  }
  Widget searchBox(){
    return Container(
      margin: EdgeInsets.fromLTRB(0, 20, 0, 10),
      width: double.infinity,
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        border: Border.all(
          color: HCColorGreyLightV3,
          width: 1,
        ),
        borderRadius: Style.radiusAll(spacing_standard),
        color: HCColorGreyLightV3,
      ),
      child: Row(
        children: [
          TextField(
            autofocus: false,
            controller: _keywordController,
            textInputAction: TextInputAction.search,
            decoration: InputDecoration.collapsed(
              hintText: 'Gõ để tìm kiếm',
              hintStyle: Style.typography.copyWith(
                color: HCColorGreyLightV5,
              ),
            ),
            textAlign: TextAlign.left,
          ).paddingLeft(0).expand(),
        ],
      ),
    );
  }
  Widget quickFilter(){
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: _statusTypes.map((status) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SvgPicture.asset(
              status.icon,
              width: 24,
              height: 24,
              color: _currentEmployeeInvestigationStatus == status.status
                  ? HCColorPrimary
                  : HCColorIcon,
            ),
            8.height,
            Text(
              '${status.statusLabel}',
              style: Style.sfProTextNormal.copyWith(
                fontSize: FontSize.s10,
                color: _currentEmployeeInvestigationStatus == status.status
                    ? HCColorPrimary
                    : HCColorIcon,
                fontWeight:
                _currentEmployeeInvestigationStatus == status.status
                    ? FontWeight.w600
                    : FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ).center(),
          ],
        ).onTap(() {
          _currentEmployeeInvestigationStatus = status.status;
          _onRefresh();
        });
      }).toList(),
    ).paddingSymmetric(
        vertical: padding_12, horizontal: spacing_standard_new);
  }
  @override
  bool get wantKeepAlive => true;
}

class EmployeeStatusInvestigationStatusModel {
  String statusLabel;

  String icon;

  Color color;

  EmployeeStatusInvestigation status;

  EmployeeStatusInvestigationStatusModel({
    this.icon,
    this.statusLabel,
    this.color,
    this.status,
  });
}
