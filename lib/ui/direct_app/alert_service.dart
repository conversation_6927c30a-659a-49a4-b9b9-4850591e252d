import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;

class AlertModel {
  final String feature;
  final String message;
  final bool isRequired;

  AlertModel({
    @required this.feature,
    @required this.message,
    @required this.isRequired,
  });

  factory AlertModel.fromJson(Map<String, dynamic> json) {
    return AlertModel(
      feature: json['feature'] as String,
      message: json['message'] as String,
      isRequired:
          json['isRequired'] == null ? false : (json['isRequired'] as bool),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'feature': feature,
      'message': message,
      'isRequired': isRequired,
    };
  }
}

class AlertService {
  static const String baseUrl =
      'https://gw-public.congtrinhviettel.com.vn/resource-service';

  Future<List<AlertModel>> getAlerts({
    @required String systemName,
    @required String features,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/app-versions/alerts/v1?systemName=$systemName&features=$features'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json; charset=utf-8',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData.map((data) => AlertModel.fromJson(data)).toList();
      } else {
        throw Exception('Failed to load alerts: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching alerts: $e');
    }
  }
}
