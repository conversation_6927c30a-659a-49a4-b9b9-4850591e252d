import 'package:flutter/material.dart';
import 'alert_service.dart';
import 'alert_dialog_widget.dart';
import 'package:home_care_partner/services/feature_blocking_service.dart';

class <PERSON><PERSON><PERSON>hecker extends StatefulWidget {
  final String features;
  final bool showOnce;

  const Alert<PERSON><PERSON><PERSON>({
    Key key,
    @required this.features,
    this.showOnce = true,
  }) : super(key: key);

  @override
  State<AlertChecker> createState() => _AlertCheckerState();
}

class _AlertCheckerState extends State<AlertChecker> {
  final AlertService _alertService = AlertService();
  final FeatureBlockingService _featureBlockingService = FeatureBlockingService.instance;
  bool _hasShownAlert = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeFeatureBlocking();
    });
  }

  /// Khởi tạo feature blocking service và kiểm tra alerts
  Future<void> _initializeFeatureBlocking() async {
    // Load cache trước
    await _featureBlockingService.loadFromCache();

    // <PERSON><PERSON><PERSON> tra alerts
    await _checkForAlerts();
  }

  Future<void> _checkForAlerts() async {
    if (widget.showOnce && _hasShownAlert) {
      return;
    }

    try {
      final alerts = await _alertService.getAlerts(
        systemName: "HSPartner",
        features: widget.features,
      );

      if (alerts.isNotEmpty && mounted) {
        // Cập nhật danh sách chức năng bị chặn
        await _featureBlockingService.updateBlockedFeatures(alerts);

        // Hiển thị dialog cho alerts không bị chặn (isRequired = false)
        final nonBlockingAlerts = alerts.where((alert) => !alert.isRequired).toList();
        if (nonBlockingAlerts.isNotEmpty) {
          _showAlertDialog(nonBlockingAlerts);
        }

        // Log thông tin debug
        debugPrint('AlertChecker: Updated blocked features: ${_featureBlockingService.blockedFeatures}');
      }
    } catch (e) {
      debugPrint('Error checking for alerts: $e');
    }
  }

  void _showAlertDialog(List<AlertModel> alerts) {
    setState(() {
      _hasShownAlert = true;
    });

    showDialog(
      context: context,
      barrierDismissible: !alerts.first.isRequired,
      builder: (BuildContext context) {
        return AlertDialogWidget(
          alert: alerts.first,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox.shrink();
  }
}

