import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:home_care_partner/constants/assets.dart';
import 'package:home_care_partner/constants/colors.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/font_family.dart';
import 'package:home_care_partner/constants/font_size.dart';
import 'package:home_care_partner/constants/hc_icon.dart';
import 'package:home_care_partner/constants/index.dart';
import 'package:home_care_partner/constants/spacing.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/constants/style.dart';
import 'package:home_care_partner/extensions/date_time.dart';
import 'package:home_care_partner/layout/layout_sliver_app_bar.dart';
import 'package:home_care_partner/main.dart';
import 'package:home_care_partner/models/base/base_error_model.dart';
import 'package:home_care_partner/models/order/args/order.arg.dart';
import 'package:home_care_partner/models/order/order.dart';
import 'package:home_care_partner/models/profile/profile.dart';
import 'package:home_care_partner/models/route/route.arg.dart';
import 'package:home_care_partner/models/search_request/search_request.arg.dart';
import 'package:home_care_partner/services/navigation_service.dart';
import 'package:home_care_partner/stores/home/<USER>';
import 'package:home_care_partner/stores/order/create_order_store.dart';
import 'package:home_care_partner/stores/order/order_store.dart';
import 'package:home_care_partner/stores/search_request/search_request_store.dart';
import 'package:home_care_partner/stores/time_sheet/time_sheet_store.dart';
import 'package:home_care_partner/stores/transaction/transaction_store.dart';
import 'package:home_care_partner/stores/user/user_store.dart';
import 'package:home_care_partner/ui/direct_app/alert_checker_widget.dart';
import 'package:home_care_partner/ui/home_page/assign_task/assign_task_history.dart';
import 'package:home_care_partner/ui/home_page/deposit.dart';
import 'package:home_care_partner/ui/home_page/statistic/statistic.dart';
import 'package:home_care_partner/ui/home_page/statistic/statistic_order.dart';
import 'package:home_care_partner/ui/home_page/task/task_history.dart';
import 'package:home_care_partner/ui/task_list/task_list_screen.dart';
import 'package:home_care_partner/widgets/bottom_sheet/modal_bottom_sheet.dart';
import 'package:home_care_partner/widgets/button/submit_button.dart';
import 'package:home_care_partner/widgets/calendar/month_picker.dart';
import 'package:home_care_partner/widgets/card/ticket_knpa_card.dart';
import 'package:home_care_partner/widgets/dialog/emotion_dialog/emotion_dialog.dart';
import 'package:home_care_partner/widgets/infinity_scroll.dart';
import 'package:home_care_partner/widgets/list_device_in_order_quick.dart';
import 'package:home_care_partner/widgets/list_service_in_order_quick.dart';
import 'package:home_care_partner/widgets/skeleton/skeleton_list.dart';
import 'package:home_care_partner/widgets/toast/error.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../routes.dart';
import 'item_catalog.dart';
import 'time_sheet_dialog.dart';
import 'package:home_care_partner/extensions/enum.dart';

class HomePageScreen extends StatefulWidget {
  static String tag = 'HomePageBottomNavigationBarScreen';

  // get listDevice => ['Điều hòa', 'Máy giặt', 'Bình nóng lạnh', 'Máy lọc nước', 'Quạt điện Quạt hơi nước', 'Camera', 'Tivi', 'Tủ lạnh','Khác'];

  get listService => ['Bảo dưỡng', 'Lắp đặt', 'Sửa chữa'];

  @override
  HomePageScreenState createState() => HomePageScreenState();
}

class HomePageScreenState extends State<HomePageScreen> {
  //stores:---------------------------------------------------------------------
  UserStore _userStore;
  TransactionStore _transactionStore;
  OrderStore _orderStore;
  HomeStore _homeStore;
  CreateOrderStore _createOrderStore;
  SearchRequestStore _searchRequestStore;
  TimeSheetStore _timeSheetStore;

  RefreshController _refreshController = RefreshController(initialRefresh: false);

  DateTime statisticStartDate;

  TextEditingController _descriptionTextEditingController = new TextEditingController();

  int indexDevice = 0;
  int indexService = 0;

  BuildContext dialogContext;

  @override
  void initState() {
    var currentDate = DateTime.now();
    statisticStartDate = DateTime(currentDate.year, currentDate.month);
    super.initState();
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    _userStore = Provider.of<UserStore>(context);
    _userStore.loadingProfile = true;
    _transactionStore = Provider.of<TransactionStore>(context);
    _orderStore = Provider.of<OrderStore>(context);
    _homeStore = Provider.of<HomeStore>(context);
    _createOrderStore = Provider.of<CreateOrderStore>(context);
    _searchRequestStore = Provider.of<SearchRequestStore>(context);
    _timeSheetStore = Provider.of<TimeSheetStore>(context);
    if(_timeSheetStore.showPopup != 1) {
      await _timeSheetStore.checkOpenPopupTimeSheet();
    }
    if(Strings.isDeepLink == BooleanType.True.getEnumValue()){
      navigationCreateOrder();
    }
    if (!this.mounted) return;
    _onRefresh();
  }

  @override
  void dispose() async {
    _userStore.dispose();

    _transactionStore.dispose();

    _orderStore.dispose();

    _timeSheetStore.dispose();

    // _homeStore.dispose();

    super.dispose();
  }

  void _onRefresh() async {
    // monitor network fetch
    fetchData();
    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    fetchData();
  }

  Future fetchData() async {
    Profile profile = await _userStore.getProfile();
    if (profile?.isPartner == true) {
      getCheckedOrders();
      getTaskList();
      getStatisticTransactionOutput();
      fetStatisticOrder();
      if(profile?.isPartnerTypeFT3M == true || profile?.isPartnerTypeFT3 == true || profile?.isPartnerTypeIFT == true){
        getAssignTaskList();
      }
    }
    getDeposit();
    getStatisticTransaction();
    getRequestEquipmentAndService(null, null, null);
    _refreshController.loadNoData();
  }

  Future fetStatisticOrder() async {
    getStatisticNormalOrder();
    getStatisticPartnerOrder();
    // getStatisticPackageOrder();
    getStatisticPackageWithoutInvestOrderApp();
    getStatisticOrderMaintenance();
  }

  Future getDeposit() {
    return Future.delayed(Duration(milliseconds: 0), () async {
      if (_userStore.loadingUserDebit == true) return;
      // get profile
      await _userStore.getUserBasicInfo();
    });
  }

  Future getStatisticTransaction() {
    return Future.delayed(Duration(milliseconds: 0), () async {
      if (_transactionStore.loadingStatistic == true) return;
      await _transactionStore.getTransactionStatistic(_userStore.user.username, statisticStartDate);
    });
  }

  Future getStatisticTransactionOutput() {
    return Future.delayed(Duration(milliseconds: 0), () async {
      if (_transactionStore.loadingStatisticOutput == true) return;
      await _transactionStore.getTransactionStatisticOutput(_userStore.user.username, statisticStartDate);
    });
  }

  Future getStatisticNormalOrder() {
    return Future.delayed(Duration(milliseconds: 0), () async {
      if (_homeStore.loadingStatisticNormalOrder == true) return;
      await _homeStore.getStatisticNormalOrder(statisticStartDate);
    });
  }

  Future getStatisticPackageWithoutInvestOrderApp() {
    return Future.delayed(Duration(milliseconds: 0), () async {
      if (_homeStore.loadingStatisticPackageWithoutInvestOrder == true) return;
      await _homeStore.getStatisticPackageWithoutInvestOrderApp(statisticStartDate);
    });
  }

  Future getStatisticPartnerOrder() {
    return Future.delayed(Duration(milliseconds: 0), () async {
      if (_homeStore.loadingStatisticPartnerOrder == true) return;
      await _homeStore.getStatisticPartnerOrder(statisticStartDate);
    });
  }

  Future getStatisticPackageOrder() {
    return Future.delayed(Duration(milliseconds: 0), () async {
      if (_homeStore.loadingStatisticPackageOrder == true) return;
      await _homeStore.getStatisticPackageOrder(statisticStartDate);
    });
  }

  Future getStatisticOrderMaintenance() {
    return Future.delayed(Duration(milliseconds: 0), () async {
      if (_homeStore.loadingStatisticOrderMaintenance == true) return;
      await _homeStore.getStatisticOrderMaintenance(statisticStartDate);
    });
  }

  Future getTaskList() {
    return Future.delayed(Duration(milliseconds: 0), () {
      if (_orderStore.isLoading == true) return;
      var startDate = DateTime.now();
      var endDate = startDate.add(
        Duration(days: 7),
      );

      var where = OrderWhere(
        startDate: startDate,
        endDate: endDate,
        pageSize: 10,
        pageIndex: DEFAULT_PAGE_INDEX,
        sortDirection: SortDirection.DESC,
        status: [
          OrderStatusEnum.Waiting,
        ],
        partnerName: _userStore.user.username,
        userType: _userStore.user.userType,
      );
      _orderStore.getOrderHomePage(where);
    });
  }
  Future getAssignTaskList() {
    return Future.delayed(Duration(milliseconds: 0), () {
      if (_orderStore.isLoadingShipping == true) return;
      var startDate = DateTime.now();
      var endDate = startDate.add(
        Duration(days: 7),
      );

      var where = OrderWhere(
        startDate: startDate,
        endDate: endDate,
        pageSize: 10,
        pageIndex: DEFAULT_PAGE_INDEX,
        sortDirection: SortDirection.DESC,
        status: [
          OrderStatusEnum.Waiting,
        ],
        partnerName: _userStore.user.username,
        userType: _userStore.user.userType,
      );
      _orderStore.getAssignOrderHomePage(where);
    });
  }

  Future getCheckedOrders() async {
    if (_timeSheetStore.showPopup != 1) {
      return Future.delayed(Duration(milliseconds: 0), () {
        if (_orderStore.isLoadingCheckedOrders) return;
        _orderStore.checkOrders(
          OrderWhere(
            pageIndex: DEFAULT_PAGE_INDEX,
            pageSize: 10,
            userType: _userStore.user.userType,
          ),
        );
        _orderStore.checkOverduePaymentDay();
        _timeSheetStore.checkIsLockTimeKeep();
        _orderStore.checkOrdersOutDatePackage();
      });
    }
  }

  void getRequestEquipmentAndService(int pageIndex, int pageSize, String keyword) async {
    await _searchRequestStore.getRequestEquipments(
      RequestEquipmentWhere(
        keyword: keyword,
        pageIndex: pageIndex,
        pageSize: pageSize,
        status: [RequestEquipmentStatusEnum.Active],
      ),
    );
    await _searchRequestStore.getRequestServices(
      RequestServiceWhere(
        keyword: keyword,
        pageIndex: pageIndex,
        pageSize: pageSize,
        status: [RequestServiceStatusEnum.Active],
      ),
    );
    await _orderStore.getSchedulerService();
  }

  Widget showAssignNeededWaring() {
    if (_userStore.profile != null &&
        _userStore.profile.isBoft3OrFT3OrAgencyManagerOrFT3M &&
        _orderStore.successGetCheckedOrders &&
        _orderStore.checkedOrders != null &&
        _orderStore.checkedOrders.length > 0 &&
        _orderStore.checkedOrders.any((checkedOrder) =>
            checkedOrder.status == OrderStatusEnum.Requesting ||
            checkedOrder.processingStatus == OrderProcessingStatusEnum.WaitingForMaintance ||
            (checkedOrder.processingStatus == OrderProcessingStatusEnum.WaitingForInvestigate && checkedOrder.userMarketing == null))) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          child: EmotionDialog(
            acceptedButtonText: 'thực hiện',
            content1: 'Trong danh sách quản lý có đơn hàng chưa được gán thợ thực hiện.',
            content2: 'Yêu cầu bạn tiến hành điều phối!',
            emotion: Assets.emotionDissatisfied,
            isTwoActions: true,
            onAccept: () {
              BuildContext currentContext = getIt<NavigationService>().navigatorKey.currentContext;
              finish(currentContext);
              Navigator.of(currentContext).push(CupertinoPageRoute(
                builder: (currentContext) => TaskListScreen(
                  initialTab: _userStore.profile != null && _userStore.profile.isRoleOrder ? 0 : 1,
                  taskListFilterArgs: TaskListFilterArgs(),
                  isNavigation: true,
                  taskGroup: TaskGroupStatus.EMPLOYMENT,
                ),
              ));
            },
          ),
        );
        _orderStore.resetCheckedOrders();
      });
    }

    if ((_orderStore.checkedOrdersOutDatePackage ?? []).length > 0 &&
        _orderStore.successCheckedOrdersOutDatePackage == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          child: _dialogWarning(),
        );
        _orderStore.successCheckedOrdersOutDatePackage = false;
      });
    }
    return 0.height;
  }

  _dialogWarning() {
    return EmotionDialog(
      content1: 'Đơn hàng gói đến hạn kết thúc hợp đồng.',
      content2: 'Bạn vui lòng liên hệ khách hàng để triển khai đơn hàng!',
      isActions: false,
      content3: Container(
        height: _orderStore.checkedOrdersOutDatePackage.length > 5 ? 150 : null,
        child: _orderStore.checkedOrdersOutDatePackage.length <= 5
            ? Wrap(
                children: _orderStore.checkedOrdersOutDatePackage
                    .map((order) => _itemWarningOrder(order)).toList(),
              )
            : SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: _orderStore.checkedOrdersOutDatePackage
                      .map((order) => _itemWarningOrder(order))
                      .toList(),
                ),
              ),
      ),
    );
  }

  Widget _itemWarningOrder(Order order) {
    return ListTile(
      contentPadding:
      EdgeInsets.symmetric(vertical: 0, horizontal: 0),
      title: Text(
        order.code,
        style: TextStyle(fontSize: 13),
      ),
      trailing: ElevatedButton(
        onPressed: () {
          Navigator.of(context).popAndPushNamed(
            Routes.order_detail,
            arguments: OrderDetailArgRoute(id: order.id),
          );
        },
        style: ElevatedButton.styleFrom(
          padding:
          EdgeInsets.symmetric(vertical: 0, horizontal: 0),
          textStyle: TextStyle(fontSize: 13),
        ),
        child: Text('Xem'),
      ),
    ).paddingSymmetric(vertical: 5).onTap(() {
      Navigator.of(context).popAndPushNamed(
        Routes.order_detail,
        arguments: OrderDetailArgRoute(id: order.id),
      );
    });
  }

  Widget showTimekeeping() {
    if (_userStore.profile != null && _userStore.profile.partnerType == PartnerType.FT3 && _timeSheetStore.showPopup == 1) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
            context: context,
            builder: (BuildContext contextDialog) {
              dialogContext = contextDialog;
              return WillPopScope(
                onWillPop: () async {
                  // if(_timeSheetStore.showPopup == 2){
                  //   Navigator.of(context).pop();
                  // }
                  return;
                },
                child: TimeSheetDialog(
                  dialogContext: contextDialog,
                ),
              );
            }).whenComplete(() {
          getCheckedOrders();
        });
        // _timeSheetStore.showPopup = 2;
      });
      _timeSheetStore.showPopup = 2;
    }
    return 0.height;
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (BuildContext context) {
        Widget view;
        if (_userStore.loadingProfile == true) {
          view = SkeletonList();
        } else if (_userStore.profile != null && _userStore.profile.isPartner) {
          view = viewPartner();
        } else if (_userStore.profile != null &&
            _userStore.profile?.userType == UserType.Partner &&
            _userStore.profile?.partnerType == PartnerType.GDCN) {
          view = viewDirector();
        } else {
          view = viewCTV();
        }

        return LayoutSliverAppBar(
          isOnline: _userStore.user?.isActive ?? false,
          child: InfinityScroll(
            refreshController: _refreshController,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            padding: EdgeInsets.all(0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // AlertChecker(
                //   features: 'ALL',
                // ),
                view ?? 0.height,
              ],
            ),
            // child: view,
          ),
        );
      },
    );
  }

  Widget viewCTV() {
    return Container(
      color: HCBackground,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(spacing_standard_new),
            margin: EdgeInsets.only(bottom: 8, top: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Bán hàng',
                      style: Style.textUITextNormal.copyWith(fontSize: FontSize.s18, fontWeight: FontWeight.w600),
                    ).expand(flex: 1),
                    10.width,
                    InkWell(
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: HCBackground,
                          border: Border.all(color: HCColorBorder),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Tháng ${statisticStartDate.getFormat(MONTH_YEAR)}').paddingSymmetric(horizontal: spacing_standard_new),
                            Icon(
                              HCIcon.calendar,
                              size: 20,
                              color: HCColorIcon,
                            ).paddingRight(spacing_standard_new),
                          ],
                        ),
                      ),
                      onTap: () {
                        showDialog(
                          context: context,
                          child: Dialog(
                            child: MonthSelect(
                              selectedMonth: statisticStartDate,
                              onChangeMonth: (date) {
                                setState(() {
                                  statisticStartDate = DateTime(date.year, date.month);
                                  fetStatisticOrder();
                                  getStatisticTransaction();
                                  getStatisticTransactionOutput();
                                });
                              },
                            ),
                          ),
                        );
                      },
                    ).expand(flex: 2),
                  ],
                ),
                16.height,
                Statistic(
                  displayTitle: false,
                ),
              ],
            ),
          ),
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(spacing_standard_new),
            child: _catalog(),
          ),
        ],
      ),
    );
  }

  Widget viewDirector() {
    return Container(
      color: HCBackground,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(spacing_standard_new),
            margin: EdgeInsets.only(bottom: 8, top: 8),
            child: Deposit(
              margin: EdgeInsets.zero,
            ),
          ),
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(spacing_standard_new),
            margin: EdgeInsets.only(bottom: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Bán hàng',
                      style: Style.textUITextNormal.copyWith(fontSize: FontSize.s18, fontWeight: FontWeight.w600),
                    ).expand(flex: 1),
                    10.width,
                    InkWell(
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: HCBackground,
                          border: Border.all(color: HCColorBorder),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Tháng ${statisticStartDate.getFormat(MONTH_YEAR)}').paddingSymmetric(horizontal: spacing_standard_new),
                            Icon(
                              HCIcon.calendar,
                              size: 20,
                              color: HCColorIcon,
                            ).paddingRight(spacing_standard_new),
                          ],
                        ),
                      ),
                      onTap: () {
                        showDialog(
                          context: context,
                          child: Dialog(
                            child: MonthSelect(
                              selectedMonth: statisticStartDate,
                              onChangeMonth: (date) {
                                setState(() {
                                  statisticStartDate = DateTime(date.year, date.month);
                                  fetStatisticOrder();
                                  getStatisticTransaction();
                                  getStatisticTransactionOutput();
                                });
                              },
                            ),
                          ),
                        );
                      },
                    ).expand(flex: 2),
                  ],
                ),
                16.height,
                Statistic(
                  displayTitle: false,
                  margin: EdgeInsets.zero,
                ),
              ],
            ),
          ),
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(spacing_standard_new),
            child: _catalog(),
          ),
        ],
      ),
    );
  }

  Container _catalog() {
    var height = MediaQuery.of(context).size.height;
    var width = MediaQuery.of(context).size.width;
    var area = height / width;
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Dịch vụ thăm khám",
            style: Style.textUITextNormal.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: FontSize.s18,
            ),
          ),
          // 16.height,
          Observer(builder: (context) {
            if (_searchRequestStore.requestEquipments == null ||
                _searchRequestStore.requestEquipments.totalCount == null ||
                _searchRequestStore.requestEquipments.totalCount == 0) {
              return 0.height;
            } else {
              return Container(
                height: height * 0.46,
                alignment: Alignment.center,
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 3, crossAxisSpacing: 1, childAspectRatio: area * 0.49),
                  physics: const ClampingScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: _searchRequestStore.requestEquipments?.totalCount,
                  itemBuilder: (context, position) {
                    var itemEquipment = _searchRequestStore.requestEquipments;
                    return ItemCatalog(
                      item: itemEquipment.requestEquipments[position],
                      ontap: () {
                        // setState(() {
                        //   _searchRequestStore.serviceSelected = _searchRequestStore.requestServices.requestServices[0];
                        //   _searchRequestStore.devicesSelected = itemEquipment.requestEquipments[position];
                        // });
                        // _descriptionTextEditingController.clear();
                        // _searchRequestStore.descriptionOtherDevice = '';
                        // _searchRequestStore.descriptionOtherService = '';
                        // buildQuickRetailBottomSheet(context, position);

                        Navigator.of(context).pushNamed(
                          Routes.select_service_appointment,
                          arguments: CreateOrderArgRoute(
                            orderType: OrderType.Saler,
                            schedulerService: _orderStore.schedulerService,
                            requestEquipment: itemEquipment.requestEquipments[position],
                          ),
                        ).whenComplete(() => getRequestEquipmentAndService(null, null, null));
                      },
                    );
                  },
                ),
              );
            }
          }),
        ],
      ),
    );
  }

  Widget viewPartner() {
    return Container(
      color: HCBackground,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          if(Strings.isDeepLink == BooleanType.False.getEnumValue())
          showAssignNeededWaring(),
          if(Strings.isDeepLink == BooleanType.False.getEnumValue())
          showTimekeeping(),
          // Activity(),
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(spacing_standard_new),
            margin: EdgeInsets.only(bottom: 8, top: 8),
            child: Column(
              children: [
                Deposit(
                  margin: EdgeInsets.only(bottom: padding_24),
                ),
                TaskHistory(),
              ],
            ),
          ),
          if ((_userStore.profile?.isPartnerTypeFT3M == true || _userStore.profile?.isPartnerTypeFT3 == true || _userStore.profile?.isPartnerTypeIFT == true) &&
              (_orderStore.schedulerAssignOrders ?? []).length != 0)
            Container(
            color: Colors.white,
            padding: EdgeInsets.all(spacing_standard_new),
            margin: EdgeInsets.only(bottom: 8),
            child: AssignTaskHistory(),
          ),

          Container(
            color: Colors.white,
            padding: EdgeInsets.all(spacing_standard_new),
            child: Column(
              children: [
                StatisticOrderHome(
                  statisticStartDate: statisticStartDate,
                  onChangeMonth: (date) {
                    setState(() {
                      statisticStartDate = DateTime(date.year, date.month);
                      fetStatisticOrder();
                      getStatisticTransaction();
                      getStatisticTransactionOutput();
                    });
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void buildQuickRetailBottomSheet(context, int pos) {
    bottomSheet(
      context: context,
      child: Container(
        padding: EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 24.0),
        height: MediaQuery.of(context).size.height * 2 / 3,
        child: Observer(
          builder: (context) => SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Stack(
                  children: [
                    Positioned(
                        right: 5.0,
                        top: 0.0,
                        child: GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Icon(
                              Icons.close,
                              size: 20,
                            ))),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Text(
                        "Tạo nhu cầu khách hàng",
                        style: TextStyle(fontWeight: FontWeight.w600, fontSize: FontSize.s18, fontFamily: FontFamily.sfUIText, letterSpacing: 0.002),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
                28.height,
                Observer(
                  builder: (BuildContext context) {
                    return Column(
                      children: [
                        ListDeviceInOrderQuick(
                          list: _searchRequestStore.requestEquipments?.requestEquipments ?? [],
                          indexSelected: pos,
                        ),
                        ListServiceInOrderQuick(
                          list: _searchRequestStore.requestServices?.requestServices ?? [],
                          indexSelected: 0,
                        ),
                      ],
                    );
                  },
                ),
                28.height,
                Row(
                  children: [_buildDescriptionError()],
                ),
                16.height,
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(12.0)), color: HCColorGreyLight),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Lưu ý',
                        style: Style.typography
                            .copyWith(fontStyle: FontStyle.normal, fontWeight: FontWeight.bold, fontSize: FontSize.s15, color: HCColorPrimary),
                        textAlign: TextAlign.left,
                      ).paddingSymmetric(vertical: 5),
                      Text(
                        'Chúng tôi sẽ thu phí 70.000 VNĐ/lần khi thợ đến nhà kiểm tra nếu không phát sinh dịch vụ hoặc vật tư khác.',
                        style: Style.typography.copyWith(fontStyle: FontStyle.normal, fontSize: FontSize.s13),
                        textAlign: TextAlign.left,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 10,
                      ).paddingSymmetric(vertical: 5),
                    ],
                  ),
                ),
                20.height,
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Flexible(
                      child: SubmitButton(
                          buttonText: 'TIẾP TỤC',
                          bgColor: HCColorPrimary,
                          textColor: Colors.white,
                          onPressed: () async {
                            if ((_searchRequestStore.devicesSelected.isAvailableAll == true &&
                                    _searchRequestStore.descriptionOtherDevice.isEmptyOrNull) ||
                                (_searchRequestStore.serviceSelected.isAvailableAll == true &&
                                    _searchRequestStore.descriptionOtherService.isEmptyOrNull)) {
                              return;
                            } else {
                              Navigator.of(context).popAndPushNamed(
                                Routes.create_order_by_saler,
                                arguments: CreateOrderArgRoute(
                                    orderType: OrderType.Saler,
                                    device: _searchRequestStore.devicesSelected,
                                    service: _searchRequestStore.serviceSelected,
                                    schedulerService: _orderStore.schedulerService,
                                    descriptionDevice: _orderStore.descriptionDevice),
                              );
                            }
                          }),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDescriptionError() {
    return Expanded(
        child: Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Mô tả trạng thái thiết bị của bạn",
            style: TextStyle(fontFamily: FontFamily.sfUIText, fontSize: FontSize.s16, color: HCColorBlack, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  child: TextFormField(
                readOnly: false,
                controller: _descriptionTextEditingController,
                maxLines: 10,
                minLines: 7,
                keyboardType: TextInputType.text,
                inputFormatters: [
                  FilteringTextInputFormatter.deny(RegExp(r'[/\\]')),
                ],
                style: TextStyle(fontFamily: FontFamily.sfUIText, fontSize: FontSize.s13, color: blackColor),
                onTap: null,
                onChanged: (String value) {
                  _orderStore.setDescriptionDevice(value);
                  // setState(() {});
                },
                validator: null,
                decoration: InputDecoration(
                    prefixIcon: null,
                    hintText: "Mô tả trạng thái hỏng hóc của thiết bị",
                    filled: true,
                    fillColor: HCColorGreyLightV3,
                    hintStyle: TextStyle(fontSize: FontSize.s14, fontWeight: FontWeight.w400, fontFamily: FontFamily.sfUIText),
                    contentPadding: EdgeInsets.all(10.0),
                    enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(style: BorderStyle.none, color: HCColorGreyLightV9), borderRadius: BorderRadius.circular(10.0)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(style: BorderStyle.none, color: HCColorGreyLightV9), borderRadius: BorderRadius.circular(10.0)),
                    alignLabelWithHint: false,
                    counterStyle: TextStyle(height: double.minPositive),
                    counterText: ""),
              ))
            ],
          )
        ],
      ),
    ));
  }

  void navigationCreateOrder() async {
    Future.delayed(Duration(milliseconds: 700), () async {
      if (Strings.employeeCode != null && Strings.employeeCode == _userStore.user.employeeCode) {
        if (Strings.module == Strings.TICKET) {
          Navigator.of(context).pushNamed(
              Routes.complain_detail,
              arguments: TicketArg(
                id: Strings.id,
                ticketCode: Strings.ticketCode,
              ),
          );
        } else if (Strings.module == Strings.INVESTIGATION){
          Navigator.of(context).pushNamed(
            Routes.investigation_detail,
            arguments: InvestigationDetailArgRoute(
              id: Strings.id ?? '',
            ),
          );
        } else {
          final result = await _orderStore.getAddressDeepLink();
          if (result != null) {
            print('navigatorDeepLink');
            print('employeeCode : ${Strings.employeeCode}');
            print('userEmployee: ${_userStore.user.employeeCode}');
            print('trueEmployee');
            getIt<NavigationService>().pushNamed(
              Routes.create_order,
              arguments: CreateOrderArgRoute(
                investigationId: '',
                orderType: BaseErrorModel.parseOrderType(Strings.orderType),
                address: splitAddress(Strings.address),
                contactRequestCode: Strings.contactRequestCode,
                areaId: result.data.processGetAioByName.areaId,
                wardId: result.data.processGetAioByName.wardId,
                provinceId: result.data.processGetAioByName.provinceId,
                contactName: Strings.contactName,
                contactPhoneNumber: Strings.contactPhoneNumber,
                customerType: CustomerType2.B2C,
                isFromInvestigation: true,
                appPartner: true,
              ),
            );
          }
        }
      } else {
        await _userStore.logout();
        _userStore.resetProfile();
        ToastError.showErrorMessage(
          context,
          message: Strings.module == Strings.TICKET
              ? Strings.requiredAccountFromTicket
              : Strings.requiredAccountFromAio,
        );
      }
    });
  }

  String splitAddress(String address){
    List<String> parts = address.split(',');
    if (parts.length > 3) {
      parts.removeRange(parts.length - 3, parts.length);
    }
    print('${parts.join(',')}');
    return parts.join(',');
  }
}