import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:home_care_partner/constants/assets.dart';
import 'package:home_care_partner/constants/colors.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/spacing.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/constants/style.dart';
import 'package:home_care_partner/data/sharedpref/constants/preferences.dart';
import 'package:home_care_partner/extensions/button_maintenance_completed.dart';
import 'package:home_care_partner/extensions/number.dart';
import 'package:home_care_partner/layout/layout_page.dart';
import 'package:home_care_partner/models/contract/contract.arg.dart';
import 'package:home_care_partner/models/order/args/order.arg.dart';
import 'package:home_care_partner/models/order/order.dart';
import 'package:home_care_partner/models/order/order_status.dart';
import 'package:home_care_partner/models/profile/profile.dart';
import 'package:home_care_partner/models/route/route.arg.dart';
import 'package:home_care_partner/models/upload/upload.arg.dart';
import 'package:home_care_partner/routes.dart';
import 'package:home_care_partner/stores/customer_360/customer_360_store.dart';
import 'package:home_care_partner/stores/delivery_bill/delivery_bill_store.dart';
import 'package:home_care_partner/stores/order/create_order_store.dart';
import 'package:home_care_partner/stores/order/order_store.dart';
import 'package:home_care_partner/stores/order/service_store.dart';
import 'package:home_care_partner/stores/upload/upload_store.dart';
import 'package:home_care_partner/stores/user/user_store.dart';
import 'package:home_care_partner/ui/direct_app/alert_checker_widget.dart';
import 'package:home_care_partner/ui/order/order_detail/cancel_reason.dart';
import 'package:home_care_partner/ui/order/order_detail/information.dart';
import 'package:home_care_partner/ui/order/order_detail/order_context.dart';
import 'package:home_care_partner/ui/order/order_detail/request_budget.dart';
import 'package:home_care_partner/ui/order/payment/payment_info.dart';
import 'package:home_care_partner/ui/order/signature/signature_order_page.dart';
import 'package:home_care_partner/ui/task_list/task_list_page.dart';
import 'package:home_care_partner/utils/device/device_utils.dart';
import 'package:home_care_partner/utils/mobile_call.dart';
import 'package:home_care_partner/utils/string.dart';
import 'package:home_care_partner/widgets/dialog/emotion_dialog/emotion_dialog.dart';
import 'package:home_care_partner/widgets/empty_widget.dart';
import 'package:home_care_partner/widgets/infinity_scroll.dart';
import 'package:home_care_partner/widgets/modal/alert_modal.dart';
import 'package:home_care_partner/widgets/skeleton/skeleton_order.dart';
import 'package:home_care_partner/widgets/toast/error.dart';
import 'package:modal_progress_hud/modal_progress_hud.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rive/rive.dart';

import 'order_waiting_schedule_reason.dart';

class OrderDetailScreen extends StatefulWidget {
  final OrderDetailArgRoute args;

  const OrderDetailScreen({
    Key key,
    this.args,
  }) : super(key: key);

  @override
  _OrderDetailScreenState createState() => _OrderDetailScreenState();
}

class _OrderDetailScreenState extends State<OrderDetailScreen>
    with WidgetsBindingObserver {
  //stores:---------------------------------------------------------------------
  OrderStore _orderStore;

  UploadStore _uploadStore;

  UserStore _userStore;

  ServiceStore _serviceStore;

  Customer360Store _customer360store;

  DeliveryBillStore _deliveryBillStore;

  String _contractCode;

  CompleteOrderInput inputStaff;

  RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  Artboard _riveArtboard;
  RiveAnimationController _controller;
  CreateOrderStore _createOrderStore;

  bool isShowWarring = false;

  @override
  void initState() {
    super.initState();
    fetchData();
    WidgetsBinding.instance.addObserver(this);
    _contractCode = '';
    rootBundle.load('assets/images/star_cry.riv').then((data) async {
      final file = RiveFile();

      if (file.import(data)) {
        final artboard = file.mainArtboard;
        artboard.addController(_controller = SimpleAnimation('1'));
        setState(() {
          _riveArtboard = artboard;
        });
      }
    });
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    _orderStore = Provider.of<OrderStore>(context);
    _uploadStore = Provider.of<UploadStore>(context);
    _userStore = Provider.of<UserStore>(context);
    _serviceStore = Provider.of<ServiceStore>(context);
    _createOrderStore = Provider.of<CreateOrderStore>(context);
    _customer360store = Provider.of<Customer360Store>(context);
    _deliveryBillStore = Provider.of<DeliveryBillStore>(context);
  }

  @override
  void didUpdateWidget(OrderDetailScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.args.id != widget.args.id) {
      _onRefresh();
    }
  }

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   super.didChangeAppLifecycleState(state);
  //
  //   if (state == AppLifecycleState.resumed &&
  //       _uploadStore.isUploadAction == false &&
  //       widget.args.id == _orderStore.currentOrder?.id) {
  //     print('widget.args.id.................${widget.args.id}');
  //
  //     _onRefresh();
  //   }
  // }

  @override
  void dispose() async {
    _orderStore.successValidateAio = false;
    WidgetsBinding.instance.removeObserver(this);
    _orderStore.dispose();
    _customer360store.reset();
    super.dispose();
  }

  void _onRefresh() async {
    if (_orderStore.isLoading == true) return;
    // monitor network fetch
    fetchData();
    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    fetchData();
  }

  // fetchData
  fetchData() {
    Future.delayed(Duration(milliseconds: 0), () async {
      _orderStore.resetLoadingStatus();
      await _orderStore.getOrderDetail(widget.args.id,
          onSuccess: (Order order) async {
        var isPopup = await _userStore.allowPopupMobileCall(widget.args.id);
        if (isPopup == true) {
          showDialog(
            context: context,
            child: EmotionDialog(
              acceptedButtonText: 'gọi',
              content1:
                  'Thời gian liên hệ khách hàng là 60p kể từ khi đơn hàng được tạo, '
                  'yêu cầu bạn liên hệ khách hàng (không ghi nhận liên hệ bằng số cá nhân). Nếu không đúng hạn KPI 60p, bạn sẽ bị phạt 500.000đ!',
              isTwoActions: true,
              onAccept: () {
                finish(context);
                MobileCall.connectMobileCall(
                  calleeNumber: order?.contactPhoneNumber,
                  username: _userStore.profile?.phoneNumber,
                  displayName: order?.contactName ?? '',
                  orderCode: order?.code ?? '',
                  sender: _userStore.profile?.fullName ??
                      _userStore.profile?.username ??
                      '',
                  orderDetail: order?.getAddressDetail,
                  cod: order.isOrderPackage &&
                          order.subPhase == SubOrderPhase.PhaseDeploy
                      ? (order.totalDeploy ?? 0).formatCurrency()
                      : (order.total ?? 0).formatCurrency(),
                  callback: (result) {
                    result.orderId = order.id;
                    result.callFrom = CallFrom.FromPartner;
                    result.type = MobileCallSaveType.order;
                    _userStore.updatePhoneCall(result);
                  },
                );
              },
            ),
          );
        }
      });
      // await _orderStore.getOrderItemsUsing(widget.args.id, widget.args.orderType);
      _uploadStore.getMultiFile(
        MultiFileWhere(referenceId: widget.args.id),
      );
      _getConfig();
      _serviceStore.getConfigByGroup();
      _refreshController.loadNoData();
    });
  }

  void _getConfig() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    setState(() {
      _contractCode =
          preferences.getString(Preferences.contract_hire_outside_worker);
    });
    await _serviceStore.getContracts(SaveContractArg(code: _contractCode));
  }

  void openDialog(ButtonStatusModel button, dynamic values) {
    if (button.buttonName == Strings.buttonManualAssign) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertModal(
            onCancel: () {
              Navigator.of(context).pop();
            },
            onAccept: () {
              Navigator.of(context).pop();
              handleSubmit(button, values);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TaskListPage(
                      args: NavBottomArgRoute(initialTab: 1),
                    ),
                  ));
            },
            child: Column(
              children: [
                // 10.height,
                // SvgPicture.asset(Assets.emotionDissatisfied),
                Rive(
                  artboard: _riveArtboard,
                ).withSize(
                  height: 150,
                  width: 200,
                ),
                // 30.height,
                Text(
                  'Bạn có chắc chắn muốn ${button.buttonName.toLowerCase()}',
                  textAlign: TextAlign.center,
                  style: Style.cardTitle,
                ),
              ],
            ),
          );
        },
      );

      return;
    } else {
      bool singleTap = false;
      if (!singleTap) {
        singleTap = true;
        DeviceUtils.hideKeyboard(context);
        handleSubmit(button, values);
        Future.delayed(const Duration(seconds: 3))
            .then((value) => singleTap = false);
      }
    }
  }

  // submit
  void handleSubmit(ButtonStatusModel button, dynamic values) {
    var currentOrder = _orderStore.currentOrder;
    if (currentOrder == null) return;

    var orderId = currentOrder.id;

    // validate order xac nhan khao sat, ky nghiem thu, hoan thanh
    if (button.buttonName == Strings.buttonInvestigating ||
        button.buttonName == Strings.buttonSignatureAcceptance ||
        button.buttonName == Strings.buttonComplete) {
      var quantitySupply = 0.0;
      currentOrder.supplyItems.forEach((element) {
        quantitySupply += element.quantity;
      });
      var quantitySupplyBroken = 0.0;
      currentOrder.supplyBrokensItems.forEach((element) {
        quantitySupplyBroken += element.quantity;
      });

      // validate order partner, ngoai bao hanh, vat tu thay the va vat tu loi hong
      // if (currentOrder.isOrderPartner &&
      //     currentOrder.tenantWarranty == TenantWarranty.NotWarrantyOrder &&
      //     quantitySupply != quantitySupplyBroken) {
      //   ToastError.showErrorMessage(
      //     context,
      //     message:
      //         'Số lượng vật tư phải bằng số lượng vật tư lỗi hỏng !',
      //   );
      //   return;
      // }

      // if ((button.buttonName == Strings.buttonSignatureAcceptance ||
      //         button.buttonName == Strings.buttonComplete) &&
      //     currentOrder.gatherInformationCustomerId.isEmptyOrNull) {
      //   ToastError.showErrorMessage(
      //     context,
      //     message: 'Bạn chưa thu thập thông tin khách hàng !',
      //   );
      //   return;
      // }
    }

    // if (currentOrder.orderType == OrderType.Normal &&
    //     (button.buttonName == Strings.buttonSignatureAcceptance ||
    //         button.buttonName == Strings.buttonComplete) &&
    //     _uploadStore.orderFiles.length <= 1) {
    //   ToastError.showErrorMessage(
    //     context,
    //     message: 'Số lượng ảnh chưa đủ!',
    //   );
    //   return;
    // }
    //
    // if (currentOrder.orderFrom == OrderFrom.GPTH &&
    //     button.buttonName == Strings.buttonComplete &&
    //     _uploadStore.orderFiles.length <= 1) {
    //   ToastError.showErrorMessage(
    //     context,
    //     message: 'Số lượng ảnh chưa đủ!',
    //   );
    //   return;
    // }

    if (currentOrder != null &&
        currentOrder.isOrderEcommerceAio &&
        currentOrder.isConfirmedAio &&
        (_uploadStore.orderFiles ?? []).length < 1) {
      ToastError.showErrorMessage(
        context,
        message:
            'Đơn hàng thay đổi số lượng thiết bị. Yêu cầu bạn cung cấp hình ảnh hàng hoá hoàn trả về kho',
      );
      return;
    }

    if (currentOrder != null &&
        currentOrder.isOrderEcommerceAio &&
        (_orderStore.listProductEcommerce ?? []).firstWhere(
                (element) =>
                    element.isNeedAttach == BooleanType.True &&
                    (element.listAttachment ?? []).length == 0,
                orElse: () => null) !=
            null) {
      ToastError.showErrorMessage(
        context,
        message: 'Thay đổi dịch vụ, yêu cầu chụp ảnh chứng minh',
      );
      return;
    }

    if (currentOrder.orderFrom == OrderFrom.GPTH &&
        button.buttonName == Strings.buttonComplete) {
      for (var item in currentOrder.supplyItems) {
        if (/*item.supplyType != SupplyType.HangQuaKho || */ item
                .goodsIsSerial !=
            GoodsIsSerial.HangSerial) continue;

        if (item.serials == null || item.serials == "") {
          ToastError.showErrorMessage(
            context,
            message: 'Vui lòng chọn serial!',
          );
          return;
        }

        if (item.serials.split(",").length < item.quantity) {
          ToastError.showErrorMessage(
            context,
            message: 'Chưa chọn đủ serial!',
          );
          return;
        }
      }
    }

    if (currentOrder.shouldSignaturePackageOrder &&
        button.nextStatus == OrderSubStatusEnum.WaitForInvestigationConfirm &&
        button.nextProcessingStatus ==
            OrderProcessingStatusEnum.WaitForInvestigationConfirm) {
      // hop dong
      _onCheckBalance(
        currentOrder: currentOrder,
        values: values,
        hasSeller: false,
        isSignContract: true,
        isCheckBalance: currentOrder.isCheckBalance,
      );
    } else if (button.nextStatus ==
        OrderSubStatusEnum.WaitForProcessingConfirm) {
      var validateNumSerialSupply = false;
      _orderStore.currentOrder.supplyItems.forEach((supply) {
        /**Check serial 2621*/
        if ((supply.supplyType == SupplyType.HangQuaKho &&
            supply.goodsIsSerial == GoodsIsSerial.HangSerial &&
            ((supply.serials.isEmptyOrNull ||
                supply.serials.split(',').length != supply.quantity.toInt()) && !currentOrder.isOrderPackage))) {
          ToastError.showErrorMessage(
            context,
            message:
                'Bạn phải chọn số lượng serial bằng số lượng vật tư ${supply.displayName}',
          );
          validateNumSerialSupply = true;
          return;
        }
      });
      if (_orderStore.currentOrder?.isOrderEcommerceAio ?? false) {
        (_orderStore.listProductEcommerce ?? []).forEach((supply) {
          if (supply.isSerial == 1 && ((supply.serials ?? '').isEmpty || supply.serials.split(',').length != supply.quantity.toInt())) {
            ToastError.showErrorMessage(
              context,
              message:
              'Bạn phải chọn số lượng serial bằng số lượng vật tư ${supply.supplyName}',
            );
            validateNumSerialSupply = true;
            return;
          }
        });
      }
      if(currentOrder.isOrderPackage){
        _orderStore.orderItemUsing.orderSupplyItems.forEach((supply) {
          /**Check serial 2621*/
          if ((supply.supplyType == SupplyType.HangQuaKho &&
              supply.supply?.goodsIsSerial == GoodsIsSerial.HangSerial &&
              (supply.serials.isEmptyOrNull ||
                  supply.serials.split(',').length != supply.quantity.toInt()))) {
            ToastError.showErrorMessage(
              context,
              message:
              'Bạn phải chọn số lượng serial bằng số lượng vật tư ${supply.displayName}',
            );
            validateNumSerialSupply = true;
            return;
          }
        });
      }
      if (validateNumSerialSupply) return;

      if ((currentOrder.isOrderPartner ||
              currentOrder.isB2B &&
                  currentOrder.isB2B && widget.args?.orderType != OrderType.PackageWithoutInvestigation  &&
                  _orderStore.b2bAcceptanceType == B2BAcceptanceType.File) &&
          currentOrder.orderFrom != OrderFrom.GPTH) {
        if ((_uploadStore.orderAcceptanceFiles ?? []).length <= 0) {
          ToastError.showErrorMessage(
            context,
            message: 'Bạn phải tải lên biên bản nghiệm thu!',
          );
          return;
        }
      }

      _onCheckBalance(
        currentOrder: currentOrder,
        values: values,
        hasSeller: true,
        isSignContract: false,
        isCheckBalance: currentOrder.isCheckBalance,
      );
    } else {
      _orderStore.updateOrderStatus(
        UpdateOrderStatusInput(
          status: button.nextMainStatus,
          subStatus: button.nextStatus,
          processingStatus: button.nextProcessingStatus,
          orderId: orderId,
          description: values['description'],
          needAssignPartner: button.needAssignPartner,
          orderType: currentOrder.orderType,
          isInvestigating: button.isInvestigating,
          partnerUpdateAt: currentOrder.partnerUpdateAt,
        ),
      );
    }
  }

  Future<bool> _checkBalance(
    Order currentOrder,
    bool isCheckBalance,
  ) async {
    bool isAvailableBalance = isCheckBalance
        ? await _userStore.checkAvailableBalance(currentOrder.id)
        : true;
    print(
        'isAvailableBalance-------------------------------------------------- $isAvailableBalance');
    if (isAvailableBalance == null) {
      ToastError.showErrorMessage(context,
          message: _userStore.errorStore.errorMessage);
      return false;
    }
    if (!isAvailableBalance ?? false) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertModal(
            onCancel: () {
              Navigator.of(context).pop();
            },
            onAccept: () {
              Navigator.of(context).pushReplacement(
                CupertinoPageRoute(
                  builder: (context) => RequestBudgetWidget(
                    orderId: currentOrder.id,
                  ),
                  fullscreenDialog: true,
                ),
              );
            },
            child: Column(
              children: [
                Text(
                  'Hạn mức khả dụng của bạn không đủ để nghiệm thu đơn hàng. Vui lòng chọn người cấp thêm hạn mức để tiếp tục thực hiện công việc',
                  textAlign: TextAlign.center,
                  style: Style.cardTitle,
                ),
              ],
            ),
          );
        },
      );
      return true;
    }
    return true;
  }

  void _onCheckBalance({
    Order currentOrder,
    Map<String, dynamic> values,
    bool hasSeller,
    bool isSignContract,
    bool isCheckBalance,
  }) async {
    // partners
    bool checkBalance = false;
    List<SellerInput> _partners = values['partners'] ?? [];
    List<CreateOrderPartnerInput> inputPartner =
        List<CreateOrderPartnerInput>();
    if ((_partners ?? []).length > 0) {
      List<CreateOrderPartnerInput> _partnersInput = _partners.map((seller) {
        return CreateOrderPartnerInput(
          // id: seller.id,
          percent: seller.rate,
          username: seller.name,
          type: OrderPartnerType.Partner,
        );
      }).toList();

      inputPartner.addAll(_partnersInput);
    }

    // Check tổng tiền hóa đơn và đơn hàng
    bool checkTotalOrderInvoice =
        await _orderStore.checkAcceptOrder(currentOrder.id);
    if (!checkTotalOrderInvoice) {
      return;
    }

    bool checkPercentOrderPartner =
        await _orderStore.checkPercentOrderPartner(inputPartner);
    if (!checkPercentOrderPartner) {
      return;
    }
    bool isCompleteChecklist = currentOrder.userMarketing == null
        ? await _orderStore.checkChecklistOrderServices(currentOrder.id)
        : true;
    if (isCompleteChecklist) {
      bool isValidProcessingTime =
          await _orderStore.checkCompleteOrderTime(currentOrder.id);
      if ((currentOrder.isOrderPackage &&
              currentOrder.userMarketing != null &&
              currentOrder.userMarketing == _userStore.user.username) ||
          isValidProcessingTime) {
        if (currentOrder.isOrderPackage &&
            currentOrder.partner != null &&
            currentOrder.partner == _userStore.user.username) {
          // await _orderStore.getPartnerLocation();
          checkBalance = await _checkBalance(currentOrder, isCheckBalance);
        } else {
          bool isNotOverDue = await _orderStore.checkOverduePaymentDay();
          if (isNotOverDue) {
            // await _orderStore.getPartnerLocation();
            checkBalance = await _checkBalance(currentOrder, isCheckBalance);
          } else {
            return;
          }
        }
      } else {
        return;
      }
    } else {
      return;
    }
    if (checkBalance ?? false)
      await _onCompleteOrder(
        orderId: currentOrder.id,
        values: values,
        isSignContract: isSignContract,
      );
  }

  Future<void> _onCompleteOrder({
    String orderId,
    Map<String, dynamic> values,
    bool isSignContract,
  }) async {
    var order = _orderStore.currentOrder;

    CompleteOrderInput input = CompleteOrderInput(
      id: orderId,
      isBill: values['isBill'] == true,
      description: values['description'],
      timeExportBill: values['timeExportBill'],
      businessName: values['businessName'],
      businessAddress: values['businessAddress'],
      businessCMT: values['businessCMT'],
      businessPhoneNumber: values['businessPhoneNumber'],
      businessContent: values['businessContent'],
      collectMoney: values['collectMoney'],
      lat: _orderStore.partnerPosition.latitude,
      lon: _orderStore.partnerPosition.longitude,
      placeId: _orderStore.partnerPlaceId,
      address: _orderStore.partnerAddress,
    );

    List<CreateOrderPartnerInput> inputPartner =
        List<CreateOrderPartnerInput>();

    // partners
    List<SellerInput> _partners = values['partners'] ?? [];

    if ((_partners ?? []).length > 0) {
      List<CreateOrderPartnerInput> _partnersInput = _partners.map((seller) {
        return CreateOrderPartnerInput(
          // id: seller.id,
          orderId: orderId,
          percent: seller.rate,
          username: seller.name,
          type: OrderPartnerType.Partner,
        );
      }).toList();

      inputPartner.addAll(_partnersInput);
    }

    // thợ khảo sát
    List<SellerInput> _partnersInvestigator = values['investigator'] ?? [];

    if ((_partnersInvestigator ?? []).length > 0) {
      List<CreateOrderPartnerInput> _partnersInput =
          _partnersInvestigator.map((seller) {
        return CreateOrderPartnerInput(
          // id: seller.id,
          orderId: orderId,
          percent: seller.rate,
          username: seller.name,
          type: OrderPartnerType.Investigator,
        );
      }).toList();

      inputPartner.addAll(_partnersInput);
    }

    input.partners = inputPartner;

    inputStaff = input;

    if ((order.isOrderPartner ||
            order.isB2B &&
                _orderStore.b2bAcceptanceType == B2BAcceptanceType.File) &&
        order.orderFrom != OrderFrom.GPTH) {
      input.partnerUpdateAt = order?.partnerUpdateAt;
      _orderStore.completeOrder(input);
    } else {
      if (order.orderFrom != null && order.orderFrom == OrderFrom.GPTH) {
        await _orderStore.validateAio(UpdateOrderStatusInput(
            orderId: order.id, status: null, subStatus: null));
      } else {
        showDialogSign(order, input, isSignContract);
      }
    }
  }

  Widget showDialogSign(
      Order order, CompleteOrderInput input, bool isSignContract) {
    _orderStore.successValidateAio = false;
    Future.delayed(
      Duration(milliseconds: 0),
      () {
        return showDialog(
          context: context,
          useSafeArea: true,
          child: Dialog(
            backgroundColor: Colors.white,
            insetPadding: EdgeInsets.all(20),
            shape: RoundedRectangleBorder(
              borderRadius: Style.radiusAll(spacing_standard),
            ),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 15,
              ),
              child: SignatureOrderScreen(
                order: order,
                input: input,
                isSignContract: isSignContract,
                createdByCustomer: (order.isCreatedByCustomer &&
                    (order.signingMethod == null ||
                        order.signingMethod == SigningMethod.Default)),
              ),
            ),
          ),
        );
      },
    );

    return Container(
      width: 0,
    );
  }

  Widget showDialogWarning(
      Order order) {
    Future.delayed(
      Duration(milliseconds: 0),
      () {
        setState(() {
          isShowWarring = true;
        });
        return showDialog(
          context: context,
          child: EmotionDialog(
            warningText: 'Thông báo',
            content1:
                'Đơn hàng chưa có chi phí dịch vụ, \nĐề nghị bạn liên hệ với người tạo đơn để thống nhất chi phí dịch vụ.',
            acceptedButtonText: 'Đóng',
            emotion: Assets.emotionDissatisfied,
          ),
        );
      },
    );

    return Container(
      width: 0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (BuildContext context) {
        var order = _orderStore.currentOrder ?? null;

        if (order?.id != widget.args.id) {
          order = null;
        }

        return WillPopScope(
          onWillPop: () async {
            if (widget.args.isViewCreateOrder == true) {
              Navigator.of(context)
                  .pushNamedAndRemoveUntil(Routes.home, (route) => false);
              return false;
            }

            return true;
          },
          child: LayoutPage(
            title: '#${order?.code ?? ''}',
            isFullPage: true,
            isScrollView: false,
            onBackPressed: () {
              if (widget.args.isViewCreateOrder == true) {
                Navigator.of(context)
                    .pushNamedAndRemoveUntil(Routes.home, (route) => false);
                return;
              }

              if (Navigator.of(context).canPop()) {
                var step = 0;
                Navigator.of(context).popUntil(
                  (route) {
                    var currentRoute = route.settings.name;
                    if (currentRoute != Routes.order_detail &&
                        currentRoute != Routes.splash) {
                      step = 1;
                    } else {
                      step = 0;
                    }
                    return step > 0;
                  },
                );
                if (step <= 0) {
                  Navigator.of(context).pushNamed(Routes.my_order);
                }
              } else {
                Navigator.of(context).pushNamed(Routes.my_order);
              }
            },
            child: ModalProgressHUD(
              inAsyncCall: _orderStore.isLoading ||
                  _orderStore.loadingUpdate ||
                  _orderStore.loadingRequestSupplyAio ||
                  _createOrderStore.loading ||
                  _customer360store.loading ||
                  _deliveryBillStore.loading ||
                  _orderStore.isLoadingProduct,
              child: InfinityScroll(
                refreshController: _refreshController,
                onRefresh: _onRefresh,
                onLoading: _onLoading,
                padding: EdgeInsets.all(0),
                child: _buildScreen(order),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildScreen(Order order) {
    if (_orderStore.isLoading) {
      return SkeletonOrder();
    }
    if (order == null) {
      return EmptyDataWidget(
        state: StatePage.Page404,
        title: Strings.emptyOrderDetail,
      );
    }

    bool isPackAndNotAssign = order.isPackAndNotAssign(
      Profile(
        username: _userStore.user?.username,
      ),
    );
    print('isPackAndNotAssign..................$isPackAndNotAssign');
    // var orderButton = isPackAndNotAssign
    var orderButton = order.isOrderPackage
        ? OrderPackButtonModel().getStatus(
            status: order.status,
            subStatus: order.subStatus,
            processingStatus: order.processingStatus,
            isAutoConfirm: order.autoConfirm,
            isCreatedByCustomer: order.isCreatedByCustomer,
            isAssigned: isPackAndNotAssign,
          )
        : OrderButtonStatusModel().getStatus(
            status: order.status,
            subStatus: order.subStatus,
            isB2B: order.isB2B,
            instalmentStatus: order.instalmentStatus,
            paymentStatus: order.paymentStatus,
            isAutoConfirm: order.autoConfirm,
            isOrderSupply: order.isOrderSupply,
          );

    var isCancel = order.isOrderSubStatusCancel || order.isOrderStatusCancel;
    bool isShowProposedSchedule = _orderStore.currentOrder.isShowProposedSchedule ?? false;

    // ky nghiem thu don hang

    if (orderButton.canSignAcceptanceOrder) {
      String buttonName = '';

      if (order.isOrderPartner) {
        buttonName = Strings.buttonComplete;
      } else if (order.isB2B) {
        if (_orderStore.b2bAcceptanceType == B2BAcceptanceType.Digital && widget.args?.orderType != OrderType.PackageWithoutInvestigation) {
          buttonName = Strings.buttonSignatureAcceptance;
        } else {
          buttonName = Strings.buttonComplete;
        }
      } else {
        buttonName = Strings.buttonComplete;
      }

      orderButton.buttons.first.buttonName = buttonName;

      if (order.isEmployeeSigned) {
        // tho da ky
        orderButton.buttons = [];
      }
    }

    // ký HD don hang goi
    print(
        'orderButton.canSignPackageOrder.....................${orderButton.canSignPackageOrder}');
    if (order.shouldSignaturePackageOrder && orderButton.canSignPackageOrder) {
      print('ky hop dong...............');
      orderButton.buttons.first.buttonName = Strings.buttonSignatureContract;
      if (order.isEmployeeContractSigned) {
        // tho da ky
        orderButton.buttons = [];
      }
    }

    String labelStatusCommissionOrder = '';
    Color colorStatusCommissionOrder = HCColorPending;
    if (order.inSellAccepted == InSellAcceptStep.InAcceptStep) {
      var confirmPercent = (order.orderPartners ?? [])
          .firstWhere(
              (element) => element.username == _userStore.user?.username,
              orElse: () => null)
          ?.confirmPercent;
      switch (confirmPercent) {
        case OrderSellerStatus.Waiting:
          labelStatusCommissionOrder = 'Chờ xác nhận';
          colorStatusCommissionOrder = HCColorPending;
          break;
        case OrderSellerStatus.Approved:
          labelStatusCommissionOrder = 'Xác nhận';
          colorStatusCommissionOrder = HCColorSuccess;
          break;
        case OrderSellerStatus.Deny:
          labelStatusCommissionOrder = 'Từ chối';
          colorStatusCommissionOrder = HCColorPrimary;
          break;
        default:
          labelStatusCommissionOrder = 'Chờ xác nhận';
          colorStatusCommissionOrder = HCColorPending;
      }
    } else if (order.inSellAccepted == InSellAcceptStep.OverAcceptStep) {
      labelStatusCommissionOrder = 'Xác nhận';
      colorStatusCommissionOrder = HCColorSuccess;
    } else if (order.inSellAccepted == InSellAcceptStep.AllowChangeAccept) {
      labelStatusCommissionOrder = 'Từ chối';
      colorStatusCommissionOrder = HCColorPrimary;
    }

    OrderProcessingStatusEnum processingStatus = order.userMarketing != null && order.processingStatus == OrderProcessingStatusEnum.WaitingForInvestigate
        ? OrderProcessingStatusEnum.WaitingForInvestigateWithPartner
        : order.processingStatus;

    return Column(
      children: [
        // AlertChecker(
        //   features: 'DETAIL_ORDER',
        // ),
        if (widget.args?.routerOrder != Strings.router_by_commission_order)
          Container(
            padding: EdgeInsets.symmetric(
              vertical: 15,
            ),
            decoration: BoxDecoration(
              color: order.periodTime != null &&
                      processingStatus != null &&
                      order.orderType == OrderType.Package
                  ? order.schedulerDate != null
                      ? orderStatusColor[order.status] ?? HCColorPending
                      : orderProcessingStatusEnumColor[processingStatus]
                  : orderStatusColor[order.status] ?? HCColorPending,
            ),
            child: Center(
              child: Text(
                order.periodTime != null &&
                    order.orderType == OrderType.Package
                    ? orderProcessingStatusEnumLabelNew(order) ?? ''
                    : orderStatusLabelNew(order) ?? '',
                style: Style.textWhiteBold,
              ),
            ),
          ),
        // if (!order.isOrderPackage &&
        //     widget.args?.routerOrder != Strings.router_by_commission_order)
        //   OrderStatus(
        //     orderStatus: orderButton,
        //   ),
        if (widget.args?.routerOrder == Strings.router_by_commission_order)
          Container(
            padding: EdgeInsets.symmetric(
              vertical: 15,
            ),
            decoration: BoxDecoration(
              color: colorStatusCommissionOrder ?? HCColorPending,
            ),
            child: Center(
              child: Text(
                labelStatusCommissionOrder ?? '',
                style: Style.textWhiteBold,
              ),
            ),
          ),
        isCancel && !_orderStore.currentOrder.isShowRemoveCancelProposalButton
            ? OrderCancelNote(
              orderCancel: order.orderCancel,
              isOrder49Or2280: _orderStore.currentOrder.isOrder49And2280,
              )
            : 0.height,
        if (_orderStore.currentOrder.isShowRemoveCancelProposalButton)
          OrderWaitingScheduleReason(
            title: "Đề xuất hủy",
            backGroundColor: HCColorPrimaryV2,
              orderWaitingScheduleReason: order.orderCancel?.reason?.name ?? ''),
        // todo xu ly do
        if (isShowProposedSchedule)
          OrderWaitingScheduleReason(
              orderWaitingScheduleReason: order.partnerExtendSchedulingTime?.reasonLv3?.name ?? ''),
        if(_orderStore.currentOrder.isShowCancelProposedExtension)
          OrderWaitingScheduleReason(
               title: "Đề xuất gia hạn triển khai",
              backGroundColor: appColorPrimary.withOpacity(0.2),
              orderWaitingScheduleReason: order.partnerExtendWorkingTime?.extendReason ?? ''),
        OrderContext(
          orderAcceptanceFiles: _uploadStore.orderAcceptanceFiles ?? [],
          orderFiles: _uploadStore.orderFiles ?? [],
          child: OrderInfo(
            order: order,
            orderButton: orderButton,
            onChangeOrderStatus: openDialog,
            onRefresh: fetchData,
            user: _userStore.user,
            routerOrder: widget.args?.routerOrder,
          ),
        ).paddingSymmetric(
          vertical: 15,
          horizontal: 20,
        ),
        buttonMaintenanceCompleted(order, _createOrderStore, context),
        refreshAfterUpdate(context),
        // ToastError.showErrorMessage(
        //   context,
        //   message: _orderStore.errorStore.errorMessage,
        // ),
        Observer(builder: (context) {
          return _orderStore.successValidateAio && inputStaff != null
              ? showDialogSign(order, inputStaff, order.isCheckBalance)
              : Container(
                  width: 0,
                  height: 0,
                );
        }),
        Observer(
          builder: (context) {
            if (_orderStore.successSignatureOrder) {
              return navigate(context);
            } else {
              return ToastError.showErrorMessage(
                context,
                message: _orderStore.errorStore.errorMessage,
              );
            }
          },
        ),
        Observer(
          builder: (context) {
            return (_orderStore.currentOrder?.totalOrderItem == 0 &&
                    !isShowWarring &&
                    orderButton.statusLabel == "Chờ khảo sát" &&
                    _userStore.profile.username == _orderStore.currentOrder?.userMarketing &&
                    _userStore.profile.username != _orderStore.currentOrder?.createdBy)
                ? showDialogWarning(order)
                : Container();
          },
        ),
      ],
    );
  }

  bool isNotAllowView(Order order) {
    if (order == null) return true;

    if (!order.isOrderParticipant(
      _userStore.user?.username,
      _userStore.profile?.isAdminAgency ?? false,
      _userStore.profile?.isManagementAgency ?? false,
    )) {
      return true;
    }

    return false;
  }

  Widget refreshAfterUpdate(BuildContext context) {
    var order = _orderStore.currentOrder;

    bool isRedirect = isNotAllowView(order);
    if (isRedirect &&
        widget.args.routerOrder != Strings.router_by_commission_order) {
      Future.delayed(Duration(milliseconds: 0), () async {
        _orderStore.resetOrder();
        var maybePop = await Navigator.of(context).maybePop();
        if (maybePop != true) {
          Navigator.of(context).pushNamed(
            Routes.task_list,
          );
        }
      });
    }

    if (_orderStore.successAddItem == true) {
      Future.delayed(Duration(milliseconds: 0), () {
        _orderStore.resetUpdateItemStatus();
        setState(() {});
      });
    }

    if (_orderStore.successUpdate == true) {
      Future.delayed(
        Duration(milliseconds: 0),
        () {
          if (_orderStore.completed == true && order.isOrderNormal) {
            showDialog(
              context: context,
              child: Dialog(
                backgroundColor: Colors.transparent,
                insetPadding: EdgeInsets.all(20),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: Style.radiusAll(spacing_standard),
                    color: Colors.white,
                  ),
                  padding: EdgeInsets.symmetric(
                    vertical: 20,
                    horizontal: 10,
                  ),
                  child: PaymentInfo(
                    order: _orderStore.currentOrder,
                    onClose: () {
                      _orderStore.setOrderComplete();
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ),
            );
            _orderStore.resetUpdateStatus();
          } else {
            print('else..................');
            _orderStore.resetUpdateStatus();
            setState(() {});
          }
        },
      );
    }

    return Container(
      width: 0,
    );
  }

  Widget navigate(BuildContext context) {
    var order = _orderStore.currentOrder;

    if (!order.isSourceTypeApp &&
        !order.isB2B &&
        !(order.isOrderPartner && order.isOrderSubStatusWaitingApprove) &&
        !(order.isOrderPartner && order.total != null && order.total <= 0) &&
        !(order.isOrderPackage && order.partner != null) && order.billType != BillType.NONE) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Navigator.of(context).pushNamed(Routes.debt);
        });
    }
    _orderStore.successSignatureOrder = false;

    return 0.width;
  }
}
