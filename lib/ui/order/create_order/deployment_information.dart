import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:home_care_partner/constants/colors.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/error.dart';
import 'package:home_care_partner/constants/hc_icon.dart';
import 'package:home_care_partner/constants/style.dart';
import 'package:home_care_partner/extensions/string.dart';
import 'package:home_care_partner/extensions/text_input_formatter.dart';
import 'package:home_care_partner/layout/layout_page.dart';
import 'package:home_care_partner/models/order/args/package.arg.dart';
import 'package:home_care_partner/models/order/combo_supply.dart';
import 'package:home_care_partner/models/order/package.dart';
import 'package:home_care_partner/models/profile/args/area.arg.dart';
import 'package:home_care_partner/models/profile/profile.dart';
import 'package:home_care_partner/models/route/route.arg.dart';
import 'package:home_care_partner/routes.dart';
import 'package:home_care_partner/stores/order/create_order_store.dart';
import 'package:home_care_partner/stores/profile/area_store.dart';
import 'package:home_care_partner/stores/user/user_store.dart';
import 'package:home_care_partner/ui/direct_app/alert_checker_widget.dart';
import 'package:home_care_partner/widgets/bottom_sheet/bar_modal_bottom_sheet.dart';
import 'package:home_care_partner/widgets/bottom_sheet/picker_work_shift_bottom_sheet.dart';
import 'package:home_care_partner/widgets/bottom_sheet/picker_work_shift_bottom_sheet_supplies.dart';
import 'package:home_care_partner/widgets/bottom_sheet/widget_bottom_sheet.dart';
import 'package:home_care_partner/widgets/button/progress_button.dart';
import 'package:home_care_partner/widgets/form/calendar_date_work.dart';
import 'package:home_care_partner/widgets/form/form_field.dart';
import 'package:home_care_partner/widgets/select/combo_supply_select.dart';
import 'package:home_care_partner/widgets/select/district_select.dart';
import 'package:home_care_partner/widgets/select/enum_select.dart';
import 'package:home_care_partner/widgets/select/package_select.dart';
import 'package:home_care_partner/widgets/select/period_time_select.dart';
import 'package:home_care_partner/widgets/select/province_select.dart';
import 'package:home_care_partner/widgets/select/ward_select.dart';
import 'package:modal_progress_hud/modal_progress_hud.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';
import 'package:smart_select/smart_select.dart';
import '../../../routes.dart';
import 'dart:core';
import 'package:home_care_partner/models/order/args/order.arg.dart';
import 'package:home_care_partner/extensions/date_time.dart';

class DeploymentInformationPage extends StatefulWidget {
  final CreateOrderArgRoute args;

  const DeploymentInformationPage({
    Key key,
    this.args,
  }) : super(key: key);

  @override
  _DeploymentInformationPageState createState() =>
      _DeploymentInformationPageState();
}

class _DeploymentInformationPageState extends State<DeploymentInformationPage> {
  AreaStore _areaStore;
  UserStore _userStore;
  final _formKey = GlobalKey<FormBuilderState>();
  PackageYear _packageYear;
  ComboSupply _comboSupply;

  PeriodTime _periodTime;
  CreateOrderStore _createOrderStore;
  int _year;
  bool isCreateOldOrder = false;
  DateTime scheduleTimeRangeTo;
  DateTime scheduleTimeRangeFrom;
  DateTime initialScheduler;
  BooleanType isPickPackageService = BooleanType.False;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _areaStore = Provider.of<AreaStore>(context);
    _createOrderStore = Provider.of<CreateOrderStore>(context);
    _areaStore.areaWhere = AreaWhere();
    _createOrderStore.isCheckSatisfyPartner = false;
    _userStore = Provider.of<UserStore>(context);
    // xoá dịch vụ nóng gấp
    _createOrderStore.resetFastWork();
    _areaStore.getListDistricts(AreaWhere(
      areaLevel: AreaLevel.District,
      provinceId: widget.args?.provinceId?.toInt()?.toString(),
    ));
    _areaStore.getListWards(AreaWhere(
      areaLevel: AreaLevel.Ward,
      parentId: widget.args?.areaId?.toInt()?.toString(),
    ));

    if (widget.args?.provinceId != null && widget.args?.areaId != null) {
      _areaStore.areaWhere.provinceId = widget.args.provinceId.toString();
      _areaStore.areaWhere.parentId = widget.args.areaId.toString();
    }
    scheduleTimeRangeTo = widget.args.scheduleTimeRangeTo;
    scheduleTimeRangeFrom = widget.args.scheduleTimeRangeFrom;
    /// tạo đơn từ đơn cũ
    if (isCreateOldOrder = widget.args.oldOrderId != null) {
      if (widget.args.scheduleTimeRangeFrom != null && widget.args.scheduleTimeRangeTo != null) {
        /// không phải đơn nóng gấp
        /// thì thowif gian ca lấy thoe khoảng thời gian của chi tiết đơn
        _createOrderStore.isPickMorning = checkMorningWorkShiftCurrentTime(widget.args.scheduleTimeRangeTo);
        // lấy ngày làm việc
        initialScheduler = widget.args.scheduleTimeRangeTo;
      }
      if (widget.args?.isHotOrder == BooleanType.True) {
        /// là đơn nóng gấp
        /// kiêm tra ca cho đơn nóng gấp
        /// check là đang trong ca
        initialScheduler = widget.args.schedulerDate;
        _createOrderStore.isPickMorning = checkHalfMorningWorkShiftCurrentTime(initialScheduler);

        /// kiêm tra thời gian nóng gấp có ngoài ca làm việc không
        DateTime schedulerStartWorkShift =
            DateTime(initialScheduler.year, initialScheduler.month, initialScheduler.day, 8, 0);
        DateTime schedulerEndWorkShift =
            DateTime(initialScheduler.year, initialScheduler.month, initialScheduler.day, 17, 30);
        if (widget.args.schedulerDate.isBefore(schedulerStartWorkShift) &&
            widget.args.schedulerDate.isAfter(schedulerEndWorkShift)) {
          outOfWorkIsFastWork();
        }
      }
    }
  }

  @override
  void dispose() async {
    _formKey?.currentState?.reset();
    super.dispose();
  }

  Future<void> _onSubmit() async {
    bool isPackageWithoutInvestigation = widget.args.orderType == OrderType.PackageWithoutInvestigation;
    bool isNormal = widget.args.orderType == OrderType.Normal;

    /// tạo đơn từ đơn cũ
    var orderItems = List<OrderItemInput>();
    if (widget.args.isHotOrder == BooleanType.True && isCreateOldOrder && widget.args.orderResultHot == BooleanType.True) {
      /// todo lấy dịch vụ từ tạo đơn mới có nóng gấp
      OrderItemCode orderItemCode;
      if (isPackageWithoutInvestigation) {
        orderItemCode = OrderItemCode.HOT_SERVICE_PACKAGE;
      }
      if (isNormal) {
        orderItemCode = OrderItemCode.HOT_SERVICE;
      }
      // lay dich vu nong gap
      orderItems = await getServiceUrgentlyHot(orderItemCode);
    }
    if (widget.args.orderType == OrderType.Package) {
      Navigator.of(context).popAndPushNamed(
        Routes.create_order,
        arguments: CreateOrderArgRoute(
          orderType: OrderType.Package,
          schedulerDate: _formKey.currentState.value['schedulerDate'],
          provinceId:
              _formKey.currentState.value['provinceId']?.toString() != null
                  ? double.parse(
                      _formKey.currentState.value['provinceId']?.toString())
                  : null,
          areaId: _formKey.currentState.value['areaId']?.toString() != null
              ? double.parse(_formKey.currentState.value['areaId']?.toString())
              : null,
          wardId: _formKey.currentState.value['wardId']?.toString() != null
              ? double.parse(_formKey.currentState.value['wardId']?.toString())
              : null,
          address: _formKey.currentState.value['address'],
          packageYear: _packageYear,
          periodTime: _periodTime,
          periodTimeYear: _year,
          contactName: widget.args?.contactName,
          contactPhoneNumber: widget.args?.contactPhoneNumber,
          customerType: widget.args?.customerType,
          companyContactName: widget.args?.companyContactName,
          companyPhoneNumber: widget.args?.companyPhoneNumber,
          taxCode: widget.args?.taxCode,
          orderPartners: widget.args?.orderPartners,
          orderCode: widget.args?.orderCode,
          createOrderBySource: widget.args?.createOrderBySource,
          partner: _areaStore.areaWhere.partner,
          areaWhere: widget.args?.areaWhere,
          isMorning: _createOrderStore.isPickMorning,
          appPartner: true,
        ),
      );
    } else if(widget.args.orderType == OrderType.PackageWithoutInvestigation){
      if(_createOrderStore.isTickHotService && _createOrderStore.fastWork?.services != null && _createOrderStore.fastWork.services.length > 0){
        _createOrderStore.fastWork.services.forEach((service) {
          OrderItemInput data = service.toOrderItem.copyWith(
            price: service.price,
            quantity: 1.0,
            displayName: service.name,
            taxId: service.taxId,
            taxValue: service.taxValue,
            defaultConfigQuantity: 1.0,
            catalogId: service.catalogId,
            status: service.status,
            unit: service.unit,
            minQuantity: 1.0,
            stepQuantity: 1.0,
            code: service.code,
            id: service.id,
            isUrgentlyHot: service.isUrgentlyHot,
            icon: service.icon,
            isDouble: false,
          );
          orderItems.add(data);
        });
      }
      Navigator.of(context).popAndPushNamed(
        Routes.create_order,
        arguments: CreateOrderArgRoute(
          orderType: OrderType.PackageWithoutInvestigation,
          schedulerDate: _formKey.currentState.value['schedulerDate'],
          provinceId:
          _formKey.currentState.value['provinceId']?.toString() != null
              ? double.parse(
              _formKey.currentState.value['provinceId']?.toString())
              : null,
          areaId: _formKey.currentState.value['areaId']?.toString() != null
              ? double.parse(_formKey.currentState.value['areaId']?.toString())
              : null,
          wardId: _formKey.currentState.value['wardId']?.toString() != null
              ? double.parse(_formKey.currentState.value['wardId']?.toString())
              : null,
          address: _formKey.currentState.value['address'],
          packageYear: _packageYear,
          periodTime: _periodTime,
          periodTimeYear: _year,
          contactName: widget.args?.contactName,
          contactPhoneNumber: widget.args?.contactPhoneNumber,
          customerType: widget.args?.customerType,
          companyContactName: widget.args?.companyContactName,
          companyPhoneNumber: widget.args?.companyPhoneNumber,
          taxCode: widget.args?.taxCode,
          orderCode: widget.args?.orderCode,
          createOrderBySource: widget.args?.createOrderBySource,
          partner: _areaStore.areaWhere.partner,
          areaWhere: widget.args?.areaWhere,
          isMorning: _createOrderStore.isPickMorning,
          appPartner: true,
          invoice: widget.args?.invoice,
          oldOrderId: widget.args?.oldOrderId,
          orderItems: orderItems,
          orderPartners: widget.args?.orderPartners,
          partnerProfile: widget.args?.partnerProfile,
          scheduleTimeRangeTo: scheduleTimeRangeTo,
          scheduleTimeRangeFrom: scheduleTimeRangeFrom,
          isHotOrder: widget.args?.isHotOrder,
          orderResultHot: widget.args?.orderResultHot,
          isQuickCreateOrder: widget.args?.isQuickCreateOrder,
        ),
      );
    }
    else if (widget.args.orderTypeCreate == OrderTypeCreate.Supply) {
      Navigator.of(context).popAndPushNamed(
        Routes.create_order,
        arguments: CreateOrderArgRoute(
          schedulerDate: _formKey.currentState.value['schedulerDate'],
          provinceId:
              _formKey.currentState.value['provinceId']?.toString() != null
                  ? double.parse(
                      _formKey.currentState.value['provinceId']?.toString())
                  : null,
          areaId: _formKey.currentState.value['areaId']?.toString() != null
              ? double.parse(_formKey.currentState.value['areaId']?.toString())
              : null,
          wardId: _formKey.currentState.value['wardId']?.toString() != null
              ? double.parse(_formKey.currentState.value['wardId']?.toString())
              : null,
          address: _formKey.currentState.value['address'],
          orderType: widget.args.orderType,
          isMorning: _createOrderStore.isPickMorning,
          orderTypeCreate: widget.args.orderTypeCreate,
          contactName: widget.args?.contactName,
          contactPhoneNumber: widget.args?.contactPhoneNumber,
          customerType: widget.args?.customerType,
          companyContactName: widget.args?.companyContactName,
          companyPhoneNumber: widget.args?.companyPhoneNumber,
          taxCode: widget.args?.taxCode,
          createOrderBySource: widget.args?.createOrderBySource,
          orderCode: widget.args?.orderCode,
          appPartner: true,
        ),
      );
    } else if (widget.args.orderType == OrderType.SalePoint) {
      Navigator.of(context).popAndPushNamed(
        Routes.create_order,
        arguments: CreateOrderArgRoute(
          schedulerDate: _formKey.currentState.value['schedulerDate'],
          provinceId:
              _formKey.currentState.value['provinceId']?.toString() != null
                  ? double.parse(
                      _formKey.currentState.value['provinceId']?.toString())
                  : null,
          areaId: _formKey.currentState.value['areaId']?.toString() != null
              ? double.parse(_formKey.currentState.value['areaId']?.toString())
              : null,
          wardId: _formKey.currentState.value['wardId']?.toString() != null
              ? double.parse(_formKey.currentState.value['wardId']?.toString())
              : null,
          address: _formKey.currentState.value['address'],
          contactName: _userStore.profile?.fullName,
          contactPhoneNumber: _userStore.profile?.phoneNumber,
          orderType: OrderType.SalePoint,
          orderTypeCreate: widget.args?.orderTypeCreate,
          comboSupplyId: _formKey.currentState.value['comboSupplyId'],
          isMorning: _createOrderStore.isPickMorning,
          appPartner: true,
        ),
      );
    } else {
      if(_createOrderStore.isTickHotService){
        orderItems = [];
        _createOrderStore.fastWork.services.forEach((service) {
          OrderItemInput data = service.toOrderItem.copyWith(
            price: service.price,
            quantity: 1.0,
            displayName: service.name,
            taxId: service.taxId,
            taxValue: service.taxValue,
            defaultConfigQuantity: 1.0,
            catalogId: service.catalogId,
            status: service.status,
            unit: service.unit,
            minQuantity: 1.0,
            stepQuantity: 1.0,
            code: service.code,
            id: service.id,
            isUrgentlyHot: service.isUrgentlyHot,
            icon: service.icon,
            isDouble: false,
          );
          orderItems.add(data);
        });
      }
      Navigator.of(context).popAndPushNamed(Routes.select_service,
          arguments: CreateOrderArgRoute(
            schedulerDate: _formKey.currentState.value['schedulerDate'],
            provinceId:
                _formKey.currentState.value['provinceId']?.toString() != null
                    ? double.parse(
                        _formKey.currentState.value['provinceId']?.toString())
                    : null,
            areaId: _formKey.currentState.value['areaId']?.toString() != null
                ? double.parse(
                    _formKey.currentState.value['areaId']?.toString())
                : null,
            wardId: _formKey.currentState.value['wardId']?.toString() != null
                ? double.parse(
                    _formKey.currentState.value['wardId']?.toString())
                : null,
            address: _formKey.currentState.value['address'],
            orderType: widget.args.orderType,
            isCombo: widget.args.isCombo,
            isMorning: _createOrderStore.isPickMorning,
            partner: _areaStore.areaWhere.partner,
            contactName: widget.args?.contactName,
            contactPhoneNumber: widget.args?.contactPhoneNumber,
            customerType: widget.args?.customerType,
            companyContactName: widget.args?.companyContactName,
            companyPhoneNumber: widget.args?.companyPhoneNumber,
            taxCode: widget.args?.taxCode,
            createOrderBySource: widget.args?.createOrderBySource,
            orderCode: widget.args?.orderCode,
            areaWhere: widget.args?.areaWhere,
            appPartner: true,
            orderItems: orderItems,
            oldOrderId: widget.args?.oldOrderId,
            invoice: widget.args?.invoice,
            orderPartners: widget.args.orderPartners,
            partnerProfile: widget.args?.partnerProfile,
            scheduleTimeRangeTo: scheduleTimeRangeTo,
            scheduleTimeRangeFrom: scheduleTimeRangeFrom,
            isHotOrder: widget.args?.isHotOrder,
            orderResultHot: widget.args?.orderResultHot,
          ));
    }
  }

  @override
  Widget build(BuildContext context) {
    var labelSchedulerDate = '';
    switch (widget.args?.orderType) {
      case OrderType.Package:
        labelSchedulerDate = 'Lịch hẹn khảo sát';
        break;
      case OrderType.SalePoint:
        labelSchedulerDate = 'Thời gian nhận hàng';
        break;
      case OrderType.Normal:
      case OrderType.Internal:
      case OrderType.Maintenance:
      case OrderType.Partner:
      case OrderType.Supply:
      case OrderType.Saler:
      case OrderType.Combo:
      case OrderType.PackageWithoutInvestigation:
        labelSchedulerDate = 'Thời gian triển khai';
        break;
      default:
        labelSchedulerDate = 'Thời gian triển khai';
    }

    return Scaffold(body: Observer(builder: (context) {
      return ModalProgressHUD(
        inAsyncCall: _createOrderStore.loading == true ||
            _areaStore.loading ||
            _areaStore.loadingDistrict ||
            _areaStore.loadingWard ||
            _createOrderStore.loadingCheckLoadPartner,
        child: LayoutPage(
          title: widget.args?.orderType == OrderType.SalePoint
              ? 'Thông tin nhận hàng'
              : 'Thông tin triển khai',
          isScrollView: true,
          child: FormBuilder(
            key: _formKey,
            initialValue: {
              'provinceId': widget.args?.provinceId?.toInt()?.toString(),
              'areaId': widget.args?.areaId?.toInt()?.toString(),
              'wardId': widget.args?.wardId?.toInt()?.toString(),
              'address': widget.args?.address?.toString(),
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // AlertChecker(
                //   features: 'CREATE_ORDER',
                // ),
                _buildAddress(),
                if(widget.args.orderType != OrderType.PackageWithoutInvestigation)
                /// Khoảng thời gian triển khai
                _buildSchedulerDateWidget(labelSchedulerDate),
                /// hiển thị chọn gói dịch vụ với đơn không khảo sát / khảo sát
                widget.args.orderType == OrderType.Package ||
                        widget.args.orderType == OrderType.PackageWithoutInvestigation
                    ? orderPackage()
                    : 0.height,

                /// Khoảng thời gian triển khai
                if(widget.args.orderType == OrderType.PackageWithoutInvestigation)
                _buildSchedulerDateWidget(labelSchedulerDate),

                if (widget.args.orderTypeCreate ==
                    OrderTypeCreate.ComboSalePoint)
                  comboSupply(),
                ProgressButton(
                  strokeWidth: 2,
                  buttonText: 'Tiếp tục',
                  onPressed: (AnimationController controller) async {
                    _formKey.currentState.save();
                    if (_formKey.currentState.validate()) {
                      _onSubmit();
                    } else {
                      print("validation failed");
                    }
                  },
                ),
                20.height,
              ],
            ),
          ),
        ),
      );
    }));
  }
  Widget _buildAddress() {
    return AbsorbPointer(
      absorbing: widget.args?.createOrderBySource != null &&
          widget.args?.provinceId != null &&
          widget.args?.areaId != null &&
          widget.args?.wardId != null &&
          widget.args?.address != null ||
          widget.args?.oldOrderId != null,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FormFieldWidget(
            labelText: 'Tỉnh / Thành phố',
            child: FormBuilderField(
              name: 'provinceId',
              validator: FormBuilderValidators.compose(
                [
                  FormBuilderValidators.required(
                    context,
                    errorText: ErrorString.required,
                  ),
                ],
              ),
              builder: (FormFieldState<dynamic> field) {
                return CustomFormField(
                  field: field,
                  child: ProvinceSelect(
                    value: field.value,
                    hasError: field.hasError,
                    isAllowResetValue: true,
                    onChange: (area) {
                      _areaStore.areaWhere.provinceId = area.id;
                      field.didChange(area?.id ?? null);
                      _formKey.currentState.fields['areaId'].reset();
                      _areaStore.getListDistricts(AreaWhere(
                          areaLevel: AreaLevel.District,
                          provinceId: (area).id));
                    },
                  ),
                );
              },
            ),
          ),
          FormFieldWidget(
            labelText: 'Quận / Huyện',
            child: FormBuilderField(
              name: 'areaId',
              validator: FormBuilderValidators.compose(
                [
                  FormBuilderValidators.required(
                    context,
                    errorText: ErrorString.required,
                  ),
                ],
              ),
              builder: (FormFieldState<dynamic> field) {
                var values = _formKey.currentState.fields ?? {};
                var provinceId = values.containsKey('provinceId') &&
                        values['provinceId'].value != null &&
                        values['provinceId'].value != ''
                    ? double.parse(values['provinceId'].value).toString()
                    : '';
                return CustomFormField(
                  field: field,
                  child: DistrictSelect(
                    level: AreaLevel.District,
                    value: field.value,
                    disabled: !provinceId.isNotEmptyString,
                    parentId: provinceId,
                    hasError: field.hasError,
                    isAllowResetValue: true,
                    onChange: (area) async{
                      field.didChange(area?.id ?? null);
                      _areaStore.areaWhere.parentId = area.id;
                      _formKey.currentState.fields['schedulerDate'].reset();
                      _formKey.currentState.fields['wardId'].reset();
                      _areaStore.getListWards(AreaWhere(
                          areaLevel: AreaLevel.Ward, parentId: (area).id));
                      // gán lại thông tin thợ là rỗng
                      _areaStore.areaWhere.partner = null;
                      _createOrderStore.getListPackageYear(SearchPackagesAppWhere(
                        packageYearType: widget.args?.orderType != null &&
                            widget.args.orderType == OrderType.PackageWithoutInvestigation
                            ? PackageYearType.WithoutInvestigation
                            : widget.args?.orderType != null &&
                            widget.args.orderType == OrderType.Package ? PackageYearType.WithInvestigation : null,
                        status: [StatusPackage.Active, StatusPackage.Hidden],
                        provinceId: values['provinceId']?.value,
                        areaId: area?.id,
                        appPartner: true,
                      ));
                      /// xoá dịch vụ hot nóng
                      _createOrderStore.isChangeAddress = true;
                    },
                  ),
                );
              },
            ),
          ),
          FormFieldWidget(
            labelText: 'Phường / Xã',
            child: FormBuilderField(
              name: 'wardId',
              validator: (val) {
                if ((val ?? '').isEmpty &&
                    (_areaStore.wards ?? []).length != 0) {
                  return ErrorString.required;
                }

                return null;
              },
              builder: (FormFieldState<dynamic> field) {
                var values = _formKey.currentState.fields ?? {};
                var areaId = values.containsKey('areaId') &&
                        values['areaId'].value != null &&
                        values['areaId'].value != ''
                    ? double.parse(values['areaId'].value).toString()
                    : '';
                return CustomFormField(
                  field: field,
                  child: WardSelect(
                    level: AreaLevel.District,
                    value: field.value,
                    disabled: !areaId.isNotEmptyString,
                    parentId: areaId,
                    hasError: field.hasError,
                    isAllowResetValue: true,
                    onChange: (area) {
                      field.didChange(area?.id ?? null);
                    },
                  ),
                );
              },
            ),
          ),
          FormFieldWidget(
            labelText: 'Địa chỉ',
            child: FormBuilderTextField(
              name: 'address',
              inputFormatters: [
                TextFieldFormatter(),
              ],
              decoration: Style.inputDecoration(
                hintText: 'Số nhà, đường phố, tổ, xóm, ...',
                suffixIcon: HCIcon.pencil,
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(
                  context,
                  errorText: ErrorString.required,
                ),
              ]),
              minLines: 1,
              maxLines: 3,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSchedulerDateWidget(String labelSchedulerDate) {
    return AbsorbPointer(
      absorbing: widget.args.oldOrderId != null || (widget.args.orderType == OrderType.PackageWithoutInvestigation && isPickPackageService == BooleanType.False),
      child: FormFieldWidget(
        labelText: labelSchedulerDate,
        child: FormBuilderField(
          name: 'schedulerDate',
          initialValue: initialScheduler,
          validator: FormBuilderValidators.compose([
            (val) {
              if (val == null && !isCreateOldOrder) return ErrorString.required;
              return null;
            }
          ]),
          builder: (FormFieldState<dynamic> field) {
            return CalendarWork(
                value: field.value,
                createOrderArgRoute: widget.args,
                areaWhere: _areaStore.areaWhere,
                hasError: field.hasError,
                isChosenDate: true,
                isNote: false,
                errorText: field.errorText,
                hint: widget.args.orderType == OrderType.Package
                    ? "Chọn khoảng thời gian hẹn lịch khảo sát"
                    : labelSchedulerDate,
                title: widget.args.orderType == OrderType.Package
                    ? "Chọn khoảng thời gian hẹn lịch khảo sát"
                    : labelSchedulerDate,
                addHours: 3,
                onChange: (date) {
                   field.didChange(date);
                },
                onUnTickCalendarAppointment: (){
                  field.didChange(null);
                },
                onChangeProfile: (profile) {
                  _areaStore.areaWhere.partnerUserName = profile.username;

                  ///TODO chi dinh tho va chon thoi gian
                  // chi khong check tai voi don vat tu
                  widget.args.orderTypeCreate == OrderTypeCreate.Supply ||
                          widget.args.orderType == OrderType.SalePoint
                      ? _buildSchedulerDateSupplyOrder(field, profile)
                      : _buildSchedulerDate(field, profile);
                });
          },
        ),
      ),
    );
  }
  /// không phải đơn vật tư và đơn điểm bán và chỉ định thợ
  /// thay đổi thợ làm việc
  Future _buildSchedulerDate(FormFieldState field, Profile profile) {
    return bottomSheet(
        context: context,
        child: WidgetBottomSheet(
          title: 'Chọn khoảng thời gian làm việc',
          padding: EdgeInsets.zero,
          child: PickerWorkShiftBottomShift(
            isNote: false,
            areaWhere: _areaStore.areaWhere,
            appointWorkers: true,
            orderType: widget.args.orderType,
            onChange: (date, isPickMorning) {
                field.didChange(date);
                _areaStore.areaWhere.partner = profile;
            },
          ),
        ));
  }

  Future _buildSchedulerDateSupplyOrder(FormFieldState field, Profile profile) {
    return bottomSheet(
        context: context,
        child: WidgetBottomSheet(
          title: 'Chọn khoảng thời gian làm việc',
          padding: EdgeInsets.zero,
          child: PickerWorkShiftBottomShiftSupplies(
            areaWhere: _areaStore.areaWhere,
            appointWorkers: true,
            onChange: (date, isPickMorning) {
              field.didChange(date);
              _areaStore.areaWhere.partner = profile;
            },
          ),
        ));
  }

  Widget orderPackage() {
    return Column(
      children: [
        FormFieldWidget(
          labelText: 'Gói dịch vụ',
          child: FormBuilderField(
            name: 'packageId',
            validator: FormBuilderValidators.compose(
              [
                FormBuilderValidators.required(
                  context,
                  errorText: ErrorString.required,
                ),
              ],
            ),
            builder: (FormFieldState<dynamic> field) {
              widget.args?.copyWith(
                appPartner: true,
              );
              return CustomFormField(
                field: field,
                child: PackageSelect(
                  value: field.value,
                  hasError: field.hasError,
                  createOrderArgRoute: widget.args,
                  onChange: (PackageYear packageYear) {
                    setState(() {
                      field.didChange(packageYear?.id ?? null);
                      _packageYear = packageYear;
                      isPickPackageService = BooleanType.True;
                    });
                    _createOrderStore.setCurrentSelectedPackageYear(packageYear);
                    if (widget.args.isQuickCreateOrder) {
                      if (_packageYear.isIgnoreCheckLoad == BooleanType.False &&
                          widget.args.orderType == OrderType.PackageWithoutInvestigation) {
                        DateTime date = _formKey.currentState.fields['schedulerDate'].value;

                        /// chua co tho thi check tren trung tam quan doi
                        checkSatisfyLoad(date: date, packageId: _packageYear.id);
                      }
                      // thay doi goi khong check tai thi xoa don nong gap
                      if (_packageYear.isIgnoreCheckLoad == BooleanType.True &&
                          widget.args.orderType == OrderType.PackageWithoutInvestigation &&
                          _createOrderStore.isTickHotService) {
                        _createOrderStore.isTickHotService = false;
                        if (_formKey.currentState.fields.containsKey('schedulerDate')) {
                          _formKey.currentState.fields['schedulerDate'].didChange(null);
                        }
                      }
                    }
                    // DeviceUtils.hideKeyboard(context);
                  },
                ),
              );
            },
          ),
        ),
        if (_packageYear != null && widget.args?.orderType != OrderType.PackageWithoutInvestigation)
          FormFieldWidget(
            labelText: 'Thời gian triển khai',
            child: Column(
              children: [
                FormBuilderField(
                  name: 'periodTimeYear',
                  validator: FormBuilderValidators.compose(
                    [
                      FormBuilderValidators.required(
                        context,
                        errorText: ErrorString.required,
                      ),
                    ],
                  ),
                  builder: (FormFieldState<dynamic> field) {
                    var now = DateTime.now().year;
                    List<S2Choice> options = [
                      S2Choice(
                        title: "Năm $now",
                        value: now,
                      ),
                      S2Choice(
                        title: "Năm ${now + 1}",
                        value: now + 1,
                      ),
                    ];
                    return CustomFormField(
                      field: field,
                      child: InputDecorator(
                        decoration: Style.inputBorderNone,
                        child: EnumSelect(
                          value: field.value,
                          options: options,
                          disabled: false,
                          placeholder: 'Chọn năm triển khai',
                          modalType: S2ModalType.fullPage,
                          title: 'Năm triển khai',
                          onChange: (val) {
                            setState(() {
                              field.didChange(val ?? null);
                              _year = val;
                            });
                            _createOrderStore.getListPeriodTimeByPackage(
                                _packageYear.id,
                                year: _year);
                            // DeviceUtils.hideKeyboard(context);
                          },
                          allowSearch: true,
                          type: DropDownType.OutLine,
                          color: field.hasError == true
                              ? HCColorPrimary
                              : HCColorGreyLightV5,
                          padding: EdgeInsets.symmetric(vertical: 0),
                          titleStyle: Style.typography,
                        ),
                      ),
                    );
                  },
                ),
                10.height,
                if (_year != null)
                  FormBuilderField(
                    name: 'periodTimeId',
                    validator: FormBuilderValidators.compose(
                      [
                        FormBuilderValidators.required(
                          context,
                          errorText: ErrorString.required,
                        ),
                      ],
                    ),
                    builder: (FormFieldState<dynamic> field) {
                      return CustomFormField(
                        field: field,
                        child: PeriodTimeSelect(
                          value: field.value,
                          hasError: field.hasError,
                          onChange: (periodTime) {
                            setState(() {
                              field.didChange(periodTime?.id ?? null);
                              _periodTime = periodTime;
                            });
                            _createOrderStore
                                .setCurrentSelectedPeriodTime(periodTime);
                            // DeviceUtils.hideKeyboard(context);
                          },
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
      ],
    );
  }

  Widget comboSupply() {
    return Column(
      children: [
        FormFieldWidget(
          labelText: 'Gói combo',
          child: FormBuilderField(
            name: 'comboSupplyId',
            validator: FormBuilderValidators.compose(
              [
                FormBuilderValidators.required(
                  context,
                  errorText: ErrorString.required,
                ),
              ],
            ),
            builder: (FormFieldState<dynamic> field) {
              return CustomFormField(
                field: field,
                child: ComboSupplySelect(
                  value: field.value,
                  hasError: field.hasError,
                  onChange: (ComboSupply packageYear) {
                    setState(() {
                      field.didChange(packageYear?.id ?? null);
                      _comboSupply = packageYear;
                    });
                    // DeviceUtils.hideKeyboard(context);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

// kiêm tra ca cho đơn nóng hot
  bool checkHalfMorningWorkShiftCurrentTime(DateTime currentTime) {
    if (currentTime.isAfter(DateTime(currentTime.year, currentTime.month, currentTime.day, 12, 0))) {
      return false;
    }
    return true;
  }

  // kiêm tra ca cho đơn nóng hot
  bool checkMorningWorkShiftCurrentTime(DateTime currentTime) {
    if (currentTime.isAfter(DateTime(currentTime.year, currentTime.month, currentTime.day, 12, 0))) {
      return false;
    }
    return true;
  }


  outOfWorkIsFastWork() {
    scheduleTimeRangeTo = null;
    scheduleTimeRangeFrom = null;
    _createOrderStore.isPickMorning = null;
  }

  Future<List<OrderItemInput>> getServiceUrgentlyHot(OrderItemCode orderItemCode) async{
    return _createOrderStore.getServiceUrgentlyHot(orderItemCode);
  }

  Future<void> checkSatisfyLoad({DateTime date, String packageId}) async {
    if(date != null) {
      if (_formKey.currentState.fields.containsKey('assignPartner') && _formKey.currentState.fields['assignPartner'].value == null) {
        await _createOrderStore.getListTime(_areaStore.areaWhere, packageId: packageId);
      }
      else{
        await _createOrderStore.getListTimeAppointWorker(_areaStore.areaWhere, packageId: packageId);
      }
      _createOrderStore.outPeriods.forEach((element) {
        if (element.date == date) {
          if (!element.morningTime && _createOrderStore.isPickMorning == true) {
            _formKey.currentState.fields['schedulerDate'].didChange(null);
          }
          if (!element.afternoonTime && _createOrderStore.isPickMorning == false) {
            _formKey.currentState.fields['schedulerDate'].didChange(null);
          }
        }
      });
    }
  }
}
