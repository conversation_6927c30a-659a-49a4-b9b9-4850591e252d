import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/data/network/constants/endpoints.dart';
import 'package:home_care_partner/models/file/file.dart';
import 'package:home_care_partner/models/invoice/invoice.arg.dart';
import 'package:home_care_partner/models/order/order.dart';
import 'package:home_care_partner/stores/invoice/invoice_store.dart';
import 'package:home_care_partner/ui/order/create_order/button_action.dart';
import 'package:home_care_partner/ui/web_view/file_view.dart';
import 'package:home_care_partner/widgets/button/progress_button.dart';
import 'package:home_care_partner/widgets/skeleton/skeleton_order.dart';
import 'package:home_care_partner/widgets/toast/error.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';

import '../../routes.dart';
import 'package:home_care_partner/ui/direct_app/alert_checker_widget.dart';

class ViewInvoiceScreen extends StatefulWidget {
  final InvoiceArg invoiceArg;

  const ViewInvoiceScreen({
    Key key,
    this.invoiceArg,
  }) : super(key: key);

  @override
  _ViewInvoiceScreenState createState() => _ViewInvoiceScreenState();
}

class _ViewInvoiceScreenState extends State<ViewInvoiceScreen> {
  AnimationController _animationController;
  InvoiceStore _invoiceStore;

  var signEmployee = '';
  var signCustomer = '';
  bool nextStepForVerifyOtp = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    _invoiceStore = Provider.of<InvoiceStore>(context);
    Future.delayed(Duration(milliseconds: 0), () async {
      if (widget.invoiceArg?.idInvoice != null) {
        _invoiceStore.getElectronicBill(widget.invoiceArg?.idInvoice);
      }

      if (widget.invoiceArg?.idOrder != null) {
        _invoiceStore.getElectronicBillByOrderId(widget.invoiceArg?.idOrder);
      }
    });
  }

  @override
  void dispose() async {
    super.dispose();
    _invoiceStore.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Observer(builder: (context) {
      return _signaturePage();
    });
  }

  Widget _signaturePage() {
    if (_invoiceStore.loading) {
      return SkeletonOrder();
    }
    return SingleChildScrollView(
      child: Column(
        children: [
          // AlertChecker(
          //   features: 'INVOICE',
          // ),
          Container(
            height: MediaQuery.of(context).size.height * 0.9,
            child: FileViewScreen(
              isFullUrl: true,
              action: [
                _invoiceStore?.currentInvoice?.statusBill == StatusElectronicBill.Published ||
                        _invoiceStore?.currentInvoice?.statusBill == StatusElectronicBill.NotReleasedYet ||
                        _invoiceStore?.currentInvoice?.statusBill == StatusElectronicBill.Cancel ||
                        _invoiceStore?.currentInvoice?.statusBill == StatusElectronicBill.OffSeasonBrowsing
                    ? 0.width
                    : ButtonAction(
                        buttonName: 'Sửa',
                        onTap: () {
                          if (widget.invoiceArg?.idOrder != null) {
                            Navigator.of(context).popAndPushNamed(Routes.create_invoice,
                                arguments: InvoiceArg(order: Order(id: widget.invoiceArg?.idOrder, isBill: true)));
                          } else {
                            Navigator.of(context)
                                .popAndPushNamed(Routes.create_invoice, arguments: InvoiceArg(idInvoice: widget.invoiceArg?.idInvoice));
                          }
                        },
                      ),
                10.width,
              ],
              files: [
                FileStorage(
                  id: '0',
                  fileType: FileType.PDF,
                  fileUrl: '${Endpoints.apiBase}/${_invoiceStore?.currentInvoice?.fileBill}',
                ),
              ],
              title: 'Thông tin hóa đơn',
            ),
          ),
          widget.invoiceArg.isNavigatorList && (_invoiceStore?.currentInvoice?.statusBill == StatusElectronicBill.UnConFimRed ||
                  _invoiceStore?.currentInvoice?.statusBill == StatusElectronicBill.RefuseToRelease)
              ? ProgressButton(
                  disabled: false,
                  strokeWidth: 2,
                  buttonText: 'Xác nhận',
                  onPressed: (AnimationController controller) async {
                    await _invoiceStore.updateStatusElectronicBill(InvoiceInput(
                      id: _invoiceStore.currentInvoice?.id,
                      orderId: _invoiceStore.currentInvoice?.orderId,
                      statusBill: _invoiceStore.currentInvoice?.statusBill,
                    ));
                  },
                ).paddingAll(20)
              : 0.height,
          Observer(
            builder: (context) {
              if (_invoiceStore.successUpdateStatusInvoice) {
                return navigate(context);
              } else {
                return ToastError.showInformationMessage(
                  context,
                  message: _invoiceStore.errorStore.errorMessage,
                  onError: (message) {
                    Future.delayed(Duration(milliseconds: 1000), () {
                      if (message != null && message.isNotEmpty) {
                        _animationController?.reset();
                      }
                    });
                  },
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget navigate(BuildContext context) {
    Navigator.of(context).pop();
    ToastError.showSuccessMessage(
      context,
      message: 'Xác nhận hóa đơn thành công!',
    );
    _invoiceStore.successUpdateStatusInvoice = false;

    return 0.width;
  }
}
