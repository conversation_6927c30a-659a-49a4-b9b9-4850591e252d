import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:home_care_partner/constants/colors.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/hc_icon.dart';
import 'package:home_care_partner/constants/index.dart';
import 'package:home_care_partner/constants/spacing.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/constants/style.dart';
import 'package:home_care_partner/models/invoice/invoice.arg.dart';
import 'package:home_care_partner/stores/invoice/invoice_store.dart';
import 'package:home_care_partner/widgets/bottom_sheet/invoice_filter_bottom_sheet.dart';
import 'package:home_care_partner/widgets/bottom_sheet/modal_bottom_sheet.dart';
import 'package:home_care_partner/widgets/empty_widget.dart';
import 'package:home_care_partner/widgets/infinity_scroll.dart';
import 'package:home_care_partner/widgets/skeleton/skeleton_list.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'invoice_card.dart';
import 'package:home_care_partner/ui/direct_app/alert_checker_widget.dart';

class InvoiceList extends StatefulWidget {
  @override
  _InvoiceListState createState() => _InvoiceListState();
}

class _InvoiceListState extends State<InvoiceList> with AutomaticKeepAliveClientMixin<InvoiceList> {
  InvoiceStore _invoiceStore;

  RefreshController _refreshController = RefreshController();

  GlobalKey _invoiceKey = GlobalKey();

  TextEditingController _keywordController = TextEditingController();

  Timer _debounce;

  String _keyword = '';

  var _pageIndex = DEFAULT_PAGE_INDEX;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() async {
    print("object");
    super.didChangeDependencies();
    _invoiceStore = Provider.of<InvoiceStore>(context);
    _keywordController.addListener(_onSearchChanged);
    _onRefresh();
  }

  @override
  void dispose() async {
    super.dispose();
    _keywordController.removeListener(_onSearchChanged);
    _keywordController.dispose();
  }

  @override
  void didUpdateWidget(InvoiceList oldWidget) {
    super.didUpdateWidget(oldWidget);
    _onRefresh();
  }

  _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce.cancel();
    _debounce = Timer(
      const Duration(milliseconds: 750),
      () {
        if (_keyword != _keywordController.text) {
          FocusScope.of(context).unfocus();
          _onRefresh();
          _keyword = _keywordController.text;
        }
      },
    );
  }

  void _onRefresh() async {
    if (!_invoiceStore.loadingList) {
      await fetchData(DEFAULT_PAGE_INDEX, true);
      _refreshController.refreshCompleted();
      _refreshController.loadNoData();
    }
  }

  void _onLoading() async {
    if ((_invoiceStore.listInvoice ?? []).length >= DEFAULT_PAGE_SIZE * (_pageIndex + 1)) {
      _pageIndex += 1;
      await fetchData(_pageIndex, false);
    } else {
      _refreshController.loadNoData();
    }
  }

  fetchData(int pageIndex, bool isReset) {
    Future.delayed(Duration(milliseconds: 0), () async {
      var where = InvoiceWhere(
        pageIndex: pageIndex,
        pageSize: DEFAULT_PAGE_SIZE,
        keySearch: _keywordController.text,
        statusBill: _invoiceStore.statusBill,
      );
      await _invoiceStore.searchElectronicBillApp(where, isReset);
    });
  }

  void handleChangeFilterStatus(List<StatusElectronicBill> listStatusElectronicBill) {
    if (_invoiceStore.currentStatus != listStatusElectronicBill) {
      _invoiceStore.currentStatus = listStatusElectronicBill;
      _invoiceStore.statusBill = listStatusElectronicBill;
      _onRefresh();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Observer(
      builder: (BuildContext context) {
        return Scaffold(
          body: InfinityScroll(
            refreshController: _refreshController,
            onLoading: _onLoading,
            onRefresh: _onRefresh,
            child: Column(
              children: [
                // AlertChecker(
                //   features: 'INVOICE',
                // ),
                _buildFilter(),
                _renderInvestigationList(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _renderInvestigationList() {
    if (_invoiceStore.loadingList) {
      return SkeletonList();
    } else {
      if (_invoiceStore.listInvoice?.length == null || _invoiceStore.listInvoice?.length == 0) {
        return EmptyDataWidget(
          state: StatePage.NotFound,
          title: Strings.emptyInvoice,
        );
      } else {
        return ListView.separated(
          key: _invoiceKey,
          scrollDirection: Axis.vertical,
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            return InvoiceCard(
              invoice: _invoiceStore.listInvoice[index],
              whenComplete: _onRefresh,
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return 0.height;
          },
          itemCount: _invoiceStore.listInvoice?.length,
        );
      }
    }
  }

  Widget _buildFilter() {
    return Container(
      margin: EdgeInsets.fromLTRB(0, 20, 0, 10),
      width: double.infinity,
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        border: Border.all(
          color: HCColorGreyLightV3,
          width: 1,
        ),
        borderRadius: Style.radiusAll(spacing_standard),
        color: HCColorGreyLightV3,
      ),
      child: Row(
        children: [
          TextField(
            autofocus: false,
            controller: _keywordController,
            textInputAction: TextInputAction.search,
            decoration: InputDecoration.collapsed(
              hintText: 'Gõ để tìm kiếm',
              hintStyle: Style.typography.copyWith(
                color: HCColorGreyLightV5,
              ),
            ),
            textAlign: TextAlign.left,
          ).paddingLeft(0).expand(),
          Icon(HCIcon.equalizer1).onTap(() {
            FocusScope.of(context).unfocus();
            bottomSheet(
              context: context,
              singleChildScroll: true,
              child: InvoiceFilterBottomSheet(
                listFilterStatus: _invoiceStore.listFilterStatus,
                onChangeFilter: (List<StatusElectronicBill> listStatusElectronicBill) {
                  this.handleChangeFilterStatus(listStatusElectronicBill);
                },
              ),
            );
          })
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
