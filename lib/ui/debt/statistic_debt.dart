import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:home_care_partner/constants/colors.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/index.dart';
import 'package:home_care_partner/models/transaction/instalment_app.arg.dart';
import 'package:home_care_partner/models/transaction/transaction.arg.dart';
import 'package:home_care_partner/models/transaction/transaction.dart';
import 'package:home_care_partner/stores/transaction/transaction_store.dart';
import 'package:home_care_partner/stores/user/user_store.dart';
import 'package:home_care_partner/ui/direct_app/alert_checker_widget.dart';
import 'package:home_care_partner/ui/statistic/statistic_card.dart';
import 'package:home_care_partner/ui/statistic/statistic_card_debt.dart';
import 'package:home_care_partner/ui/statistic/statistic_summary.dart';
import 'package:home_care_partner/widgets/empty_widget.dart';
import 'package:home_care_partner/widgets/infinity_scroll.dart';
import 'package:home_care_partner/widgets/input/input_datetime_picker.dart';
import 'package:home_care_partner/widgets/skeleton/skeleton_list.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class StatisticDebt2 extends StatefulWidget {
  final TransactionPage pageType;

  final DateTime startDate;

  final DateTime endDate;

  const StatisticDebt2({
    Key key,
    @required this.pageType,
    this.startDate,
    this.endDate,
  }) : super(key: key);

  @override
  _StatisticDebt createState() {
    return _StatisticDebt();
  }
}

class _StatisticDebt extends State<StatisticDebt2> {
  DateTime _startDate = DateTime.now().subtract(Duration(days: 7));
  DateTime _endDate = DateTime.now();

  static const _pageSize = DEFAULT_PAGE_SIZE;
  var _pageIndex = DEFAULT_PAGE_INDEX;

  //stores:---------------------------------------------------------------------
  TransactionStore _transactionStore;

  UserStore _userStore;

  RefreshController _refreshController = RefreshController(
    initialRefresh: false,
  );

  @override
  void initState() {
    super.initState();

    if (widget.startDate != null) {
      _startDate = widget.startDate;
    }

    if (widget.endDate != null) {
      _endDate = widget.endDate;
    }
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    _transactionStore = Provider.of<TransactionStore>(context);
    _userStore = Provider.of<UserStore>(context);
    _onRefresh();
  }

  @override
  void dispose() async {
    super.dispose();
    _transactionStore.dispose();
  }

  void _onRefresh() async {
    var loading = _transactionStore.loading != true &&
        _transactionStore.loadingAllDay != true;
    var isReload =
        loading || (_transactionStore.currentPage != widget.pageType);
    if (!isReload) return;
    // monitor network fetch
    fetchData(DEFAULT_PAGE_INDEX, true);
    if (widget.pageType == TransactionPage.BillViaViettelPay) {
      fetchTotalDebit();
    }

    if (widget.pageType == TransactionPage.Revenue) {
      fetchTotalRevenue();
    }

    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    if ((_transactionStore.transactions ?? []).length >=
        _pageSize * (_pageIndex + 1)) {
      _pageIndex += 1;
      // monitor network fetch
      fetchData(_pageIndex, false);
    } else {
      _refreshController.loadNoData();
    }
  }

  fetchData(int pageIndex, bool isReset) {
    Future.delayed(Duration(milliseconds: 0), () async {
      if (widget.pageType == TransactionPage.BillNotViaViettelPay) {
        await _transactionStore.userInstalments(
          InstalmentAppWhere(
            pageIndex: pageIndex,
            pageSize: _pageSize,
          ),
          isReset,
        );
      } else {
        var where = TransactionWhere(
          type: TransactionType.Debit,
          pageIndex: pageIndex,
          pageSize: _pageSize,
          username: _userStore.user.username,
        );
        switch (widget.pageType) {
          case TransactionPage.BillViaViettelPay:
            {
              where.status = [
                TransactionStatus.WaitingForPayment,
                TransactionStatus.WaitingViettelPayByCustomer,
                TransactionStatus.WaitingViettelPayByPartner,
                TransactionStatus.WaitingBankTransfer,
                TransactionStatus.WaitingBill,
                TransactionStatus.WaitingPayment
              ];
              where.excludeB2B = true;
              break;
            }
          case TransactionPage.Bill:
            {
              where.type = TransactionType.Credit;
              where.status = [
                TransactionStatus.WaitingBill,
                // TransactionStatus.WaitingPayment,
              ];
              // where.startDate = _startDate;
              // where.endDate = _endDate;
              break;
            }
          case TransactionPage.Debt:
            {
              where.status = [
                TransactionStatus.WaitingForPayment,
                TransactionStatus.WaitingViettelPayByCustomer,
                TransactionStatus.WaitingViettelPayByPartner,
                TransactionStatus.WaitingBankTransfer,
                TransactionStatus.WaitingBill,
                TransactionStatus.WaitingPayment
              ];
              where.excludeB2B = true;
              break;
            }
          case TransactionPage.Revenue:
            {
              where.type = TransactionType.Credit;
              // where.status = [
              //   TransactionStatus.Completed,
              // ];
              where.startDate = _startDate;
              where.endDate = _endDate;
              where.productType = [
                ProductType.Service,
                ProductType.Labor,
              ];
              break;
            }
          default:
            break;
        }
        await _transactionStore.userTransactions(
          where,
          isReset,
          widget.pageType,
        );
      }

      if (isReset) {
        _pageIndex = DEFAULT_PAGE_INDEX;
      }
    });
  }

  void onChangeDate(DateTime first, DateTime last) {
    setState(() {
      _startDate = first;
      _endDate = last;
    });
    fetchData(DEFAULT_PAGE_INDEX, true);
  }

  fetchTotalDebit() {
    Future.delayed(Duration(milliseconds: 0), () async {
      var whereAllTime = TotalAmountTransactionWhere(
        type: TransactionType.Debit,
        status: [
          TransactionStatus.WaitingForPayment,
          TransactionStatus.WaitingViettelPayByCustomer,
          TransactionStatus.WaitingViettelPayByPartner,
          TransactionStatus.WaitingBankTransfer,
          TransactionStatus.WaitingBill,
          TransactionStatus.WaitingPayment
        ],
      );
      await _transactionStore.transactionAllDay(whereAllTime);
      // await _transactionStore.transactionAllDay(whereAllTime);
    });
  }

  fetchTotalRevenue() {
    Future.delayed(Duration(milliseconds: 0), () async {
      var whereAllTime = TotalAmountTransactionWhere(
        type: TransactionType.Credit,
        // status: [
        //   TransactionStatus.Completed,
        // ],
        endDate: _endDate,
        startDate: _startDate,
        productType: [
          ProductType.Service,
          ProductType.Labor,
        ],
      );

      await _transactionStore.transactionAllDay(whereAllTime);
      // await _transactionStore.transactionAllDay(whereAllTime);
    });
  }

  onResetTransactionCompleted(String id) {
    Future.delayed(Duration(milliseconds: 0), () {
      _transactionStore.onResetTransactionCompleted(id);
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return InfinityScroll(
      refreshController: _refreshController,
      onLoading: _onLoading,
      onRefresh: _onRefresh,
      child: widget.pageType != TransactionPage.BillNotViaViettelPay
          ? Observer(
              builder: (BuildContext context) {
                var listTransactions = _transactionStore.listTransactions ?? [];

                if (listTransactions.length < _pageSize * (_pageIndex + 1)) {
                  _refreshController.loadNoData();
                } else {
                  _refreshController.loadFailed();
                }
                return Column(
                  children: [
                    // AlertChecker(
                    //   features: 'DEBT',
                    // ),
                    widget.pageType == TransactionPage.Bill ? 20.height : 0.height,
                    ..._topCard(),
                    10.height,
                    ...listTransactions.map(
                      (transaction) {
                        var transactionColor = getColorTransaction(transaction);
                        return StatisticCardDebt(
                          orderId: transaction.id,
                          pageType: widget.pageType,
                          dateTime: transaction.createdAt,
                          orderCode: transaction.order?.code ?? '',
                          total: transaction.amount,
                          iconColor: transactionColor['iconColor'],
                          subTitle: transactionColor['subTitle'],
                          subTitleColor: transactionColor['subTitleColor'],
                          allowRedirect: transactionColor['allowRedirect'],
                          allowPayment: transactionColor['allowPayment'],
                          onPaymentCompleted: () {
                            onResetTransactionCompleted(transaction.id);
                          },
                          onRefresh: () {
                            _onRefresh();
                          },
                        );
                      },
                    ),
                    if (_transactionStore.loading == true) SkeletonList(),
                    if (listTransactions.length == 0 && _transactionStore.loading == false)
                      EmptyDataWidget(
                        state: StatePage.NotFound,
                        title: 'Không có ${getTitleEmpty()}',
                      ),
                  ],
                );
              },
            )
          : Observer(
              builder: (BuildContext context) {
                var listInstalments = _transactionStore.listInstalments ?? [];

                if (listInstalments.length < _pageSize * (_pageIndex + 1)) {
                  _refreshController.loadNoData();
                } else {
                  _refreshController.loadFailed();
                }
                return Column(
                  children: [
                    widget.pageType == TransactionPage.Bill ? 20.height : 0.height,
                    ..._topCard(),
                    10.height,
                    ...listInstalments.map(
                      (instalment) {
                        return StatisticCardDebt(
                          orderId: instalment.id,
                          pageType: widget.pageType,
                          orderCode: instalment.orderCode ?? '',
                          total: instalment?.remainAmount?.toDouble() ?? 0.0,
                          iconColor: getColorInstalment(instalment?.status),
                          subTitle:  getTitleInstalment(instalment?.status),
                          dateTime: instalment?.expiredAt,
                          onPaymentCompleted: () {
                            onResetTransactionCompleted(instalment.id);
                          },
                          onRefresh: () {
                            _onRefresh();
                          },
                        );
                      },
                    ).toList(),
                    if (_transactionStore.loading == true) SkeletonList(),
                    if (listInstalments.length == 0 && _transactionStore.loading == false)
                      EmptyDataWidget(
                        state: StatePage.NotFound,
                        title: 'Không có ${getTitleEmpty()}',
                      ),
                  ],
                );
              },
            ),
    );
  }

  String getTitleEmpty() {
    switch (widget.pageType) {
      case TransactionPage.CashOut:
        {
          return 'giao dịch nộp tiền';
        }
      case TransactionPage.CashIn:
        {
          return 'giao dịch nhận tiền';
        }
      case TransactionPage.Revenue:
        {
          return 'doanh thu';
        }
      case TransactionPage.Debt:
      case TransactionPage.BillViaViettelPay:
      case TransactionPage.BillNotViaViettelPay:
        {
          return 'công nợ';
        }
      case TransactionPage.Bill:
        {
          return 'hóa đơn';
        }
      default:
        return 'dữ liệu hiển thị';
    }
  }

  List<Widget> _topCard() {
    List<Widget> widgets = [
      0.height,
    ];
    switch (widget.pageType) {
      case TransactionPage.BillViaViettelPay:
        {
          var totalDebt = _transactionStore.loadingAllDay
              ? 0.0
              : _transactionStore.totalTransactionAmount ?? 0.0;
          widgets.add(
            StatisticSummary(
              title: 'Tổng công nợ',
              total: totalDebt,
            ),
          );
          break;
        }
      case TransactionPage.BillNotViaViettelPay:
        {
          var totalDebt = _transactionStore.loadingAllDay
              ? 0.0
              : _transactionStore.totalAmountInstalmentApp ?? 0.0;
          widgets.add(
            StatisticSummary(
              title: 'Tổng công nợ',
              total: totalDebt,
            ),
          );
          widgets.add(
            Text(
              "*Đơn hàng thanh toán qua CNCT: Yêu cầu thanh toán về số tài khoản chuyên thu của CNCT",
              style: TextStyle(
                fontSize: 13,
              ),
              textAlign: TextAlign.center,
            ),
          );
          break;
        }
      case TransactionPage.Debt:
        {
          var totalDebt = _transactionStore.loadingAllDay
              ? 0.0
              : _transactionStore.totalTransactionAmount ?? 0.0;
          widgets.add(
            StatisticSummary(
              title: 'Tổng công nợ',
              total: totalDebt,
            ),
          );
          widgets.add(
            Text(
              "(*) Để kiểm tra công nợ không thanh toán qua Viettel Pay, vui lòng liên hệ 1900 98 98 68 để được hỗ trợ!",
              style: TextStyle(
                fontSize: 13,
              ),
              textAlign: TextAlign.center,
            ),
          );
          break;
        }
      case TransactionPage.Bill:
        {
          // widgets.add(
          //   InputDatetimePicker(
          //     startDate: _startDate,
          //     endDate: _endDate,
          //     onChange: this.onChangeDate,
          //   ),
          // );
          break;
        }
      case TransactionPage.Revenue:
        {
          var totalRevenue = _transactionStore.loadingAllDay
              ? 0.0
              : _transactionStore.totalTransactionAmount ?? 0.0;

          widgets.addAll(
            [
              StatisticSummary(
                title: 'Tổng doanh thu',
                total: totalRevenue,
              ),
              5.height,
              InputDatetimePicker(
                startDate: _startDate,
                endDate: _endDate,
                onChange: this.onChangeDate,
              ),
              5.height,
            ],
          );
          break;
        }
      default:
        break;
    }
    return widgets;
  }

  getColorTransaction(Transaction transaction) {
    var iconColor;
    var subTitle = transaction.order?.name ?? '';
    var subTitleColor;

    bool allowPayment =
        transaction.status == TransactionStatus.WaitingForPayment ||
            transaction.status == TransactionStatus.WaitingViettelPayByPartner;

    bool allowRedirect = false;

    switch (transaction.status) {
      case TransactionStatus.WaitingForPayment:
      case TransactionStatus.WaitingViettelPayByCustomer:
      case TransactionStatus.WaitingViettelPayByPartner:
        {
          if (transaction.transactionExpired) {
            iconColor = HCColorPrimary;
            subTitle = 'Đã quá hạn';
            subTitleColor = HCColorPrimary;
          } else {
            iconColor = HCColorPending;
            subTitle = 'Chờ thanh toán';
            if (transaction.status ==
                TransactionStatus.WaitingViettelPayByCustomer) {
              subTitle = 'KH thanh toán ViettelPay';
            }
            if (transaction.status ==
                TransactionStatus.WaitingViettelPayByPartner) {
              subTitle = 'Thanh toán ViettelPay';
            }
            subTitleColor = HCColorBlack;
          }
          break;
        }
      case TransactionStatus.WaitingBill:
        {
          iconColor = HCColorPrimary;
          break;
        }
      case TransactionStatus.WaitingPayment:
        {
          iconColor = HCColorPending;
          break;
        }
      case TransactionStatus.WaitingBankTransfer:
        {
          subTitle = 'Kiểm tra chuyển khoản';
          subTitleColor = HCColorGrey;
          iconColor = HCColorSuccess;
          break;
        }
      case TransactionStatus.Completed:
        {
          subTitle = transaction.order?.name ?? '';
          iconColor = HCColorSuccess;
          break;
        }
      default:
        {
          break;
        }
    }

    return {
      'iconColor': iconColor,
      'subTitleColor': subTitleColor,
      'subTitle': subTitle,
      'allowPayment': allowPayment,
      'allowRedirect': allowRedirect,
    };
  }

  getColorInstalment(InstalmentStatus status){
    switch (status) {
      case InstalmentStatus.Pending:
        return HCColorPending;
      case InstalmentStatus.Processing:
        return HCColorPending;
      case InstalmentStatus.Completed:
        return HCColorSuccess;
      case InstalmentStatus.Declined:
        return HCColorPrimary;
      case InstalmentStatus.WaitingForPayment:
        return HCColorPending;
      case InstalmentStatus.Paid:
        return HCColorSuccess;
      default:
        {
          break;
        }
    }
  }
  getTitleInstalment(InstalmentStatus status){
    switch (status) {
      case InstalmentStatus.Pending:
        return "Chờ kiểm duyệt";
      case InstalmentStatus.Declined:
        return "Từ chối kiểm duyệt";
      case InstalmentStatus.WaitingForPayment:
        return "Chờ thanh toán";
      case InstalmentStatus.Paid:
        return "Đã thanh toán";
      default:
        {
          break;
        }
    }
  }
}
