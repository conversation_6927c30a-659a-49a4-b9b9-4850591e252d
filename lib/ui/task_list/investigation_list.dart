import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:home_care_partner/constants/colors.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/hc_icon.dart';
import 'package:home_care_partner/constants/spacing.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/constants/style.dart';
import 'package:home_care_partner/models/investigation/investigation.dart';
import 'package:home_care_partner/stores/investigation/investigation_store.dart';
import 'package:home_care_partner/stores/user/user_store.dart';
import 'package:home_care_partner/ui/direct_app/alert_checker_widget.dart';
import 'package:home_care_partner/widgets/bottom_sheet/modal_bottom_sheet.dart';
import 'package:home_care_partner/widgets/bottom_sheet/survey_filter_bottom_sheet.dart';
import 'package:home_care_partner/widgets/card/investigation_list_card.dart';
import 'package:home_care_partner/widgets/empty_widget.dart';
import 'package:home_care_partner/widgets/infinity_scroll.dart';
import 'package:home_care_partner/widgets/skeleton/skeleton_list.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:smart_select/smart_select.dart';

import '../../routes.dart';

class InvestigationList extends StatefulWidget {
  @override
  _InvestigationListState createState() => _InvestigationListState();
}

class _InvestigationListState extends State<InvestigationList> with AutomaticKeepAliveClientMixin<InvestigationList> {
  List<InvestigationStatusEnum> _currentStatus = [];
  List<CustomerType2> _currentCustomerType = [];
  Campaign _campaign;
  double _areaId;

  InvestigationStore _investigationStore;

  RefreshController _refreshController = RefreshController(initialRefresh: true);

  GlobalKey _investigationKey = GlobalKey();

  TextEditingController _keywordController = TextEditingController();

  Timer _debounce;

  String _keyword = '';

  List<FilterInvestigationStatus> _listFilterStatus = [
    FilterInvestigationStatus(investigationStatusEnum: InvestigationStatusEnum.Requesting, name: "Đang yêu cầu", isCheck: false),
    FilterInvestigationStatus(investigationStatusEnum: InvestigationStatusEnum.Close, name: "Đã đóng", isCheck: false),
    FilterInvestigationStatus(investigationStatusEnum: InvestigationStatusEnum.Cancelled, name: "Đã hủy", isCheck: false),
  ];

  List<FilterInvestigationStatus> _listFilterTypeCustomer = [
    FilterInvestigationStatus(investigationTypeCustomer: CustomerType2.B2B, name: "B2B", isCheck: false),
    FilterInvestigationStatus(investigationTypeCustomer: CustomerType2.B2C, name: "B2C", isCheck: false),
  ];

  UserStore _userStore;

  @override
  void initState() {
    _currentStatus = statusFilter[0].value;
    super.initState();
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    _investigationStore = Provider.of<InvestigationStore>(context);
    _keywordController.addListener(_onSearchChanged);
    // _onRefresh();
    _userStore = Provider.of<UserStore>(context);
  }

  @override
  void dispose() async {
    super.dispose();
    _keywordController.removeListener(_onSearchChanged);
    _keywordController.dispose();
    _investigationStore.successCreateInvestigation = false;
  }

  _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce.cancel();
    _debounce = Timer(
      const Duration(milliseconds: 750),
          () {
        if (_keyword != _keywordController.text) {
          FocusScope.of(context).unfocus();
          _onRefresh();
          _keyword = _keywordController.text;
        }
      },
    );
  }

  List<S2Choice> statusFilter = [
    S2Choice(
      title: 'Tất cả trạng thái',
      value: [
        InvestigationStatusEnum.Requesting,
        InvestigationStatusEnum.Close,
        InvestigationStatusEnum.Cancelled,
      ],
    ),
    S2Choice(
      title: Strings.orderRequesting,
      value: [InvestigationStatusEnum.Requesting],
    ),
    S2Choice(
      title: Strings.orderClose,
      value: [InvestigationStatusEnum.Close],
    ),
    S2Choice(
      title: Strings.orderCancel,
      value: [InvestigationStatusEnum.Cancelled],
    ),
  ];

  void _onRefresh() async {
    await _investigationStore.getInitialListInvestigation(
      clear: true,
      status: _currentStatus,
      customerType: _currentCustomerType,
      keyword: _keywordController.text,
      idCampaign: _campaign?.id,
      provinceId: _userStore.profile.provinceId != null ? double.parse(_userStore.profile.provinceId.toString()) : null,
      areaId: _userStore.profile.provinceId != null ? _areaId : null,
    );

    _refreshController.refreshCompleted(resetFooterState: true);
  }

  void _onLoading() async {
    _refreshController.requestLoading();
    if (_investigationStore.maxToLoadMoreInvestigation == false) {
      await _investigationStore.getMoreListInvestigation(
        status: _currentStatus,
        customerType: _currentCustomerType,
        provinceId: _userStore.profile.provinceId != null ? double.parse(_userStore.profile.provinceId.toString()) : null,
        areaId: _userStore.profile.provinceId != null ? _areaId : null,
      );
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  void handleChangeFilterStatus(List<InvestigationStatusEnum> value, List<CustomerType2> customerType, Campaign campaign,double areaId) {
    if (_currentStatus != value || _currentCustomerType != customerType) {
      setState(() {
        _currentStatus = value;
        _currentCustomerType = customerType;
        _campaign = campaign;
        _areaId = areaId;
      });

      _investigationStore.setCurrentFilterStatus(value, _currentCustomerType);

      _onRefresh();
    } else {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Observer(
      builder: (BuildContext context) {
        return Scaffold(
          body: InfinityScroll(
            refreshController: _refreshController,
            onLoading: _onLoading,
            onRefresh: _onRefresh,
            child: Column(
              children: [
                // AlertChecker(
                //   features: 'SURVEY',
                // ),
                _buildFilter(),
                _renderInvestigationList(),
              ],
            ),
          ),
          floatingActionButton: FloatingActionButton(
            child: Icon(
              Icons.add,
              color: Colors.white,
            ),
            onPressed: () {
              Navigator.of(context).pushNamed(Routes.create_investigation).whenComplete(() => _onRefresh());
            },
          ),
        );
      },
    );
  }

  Widget _renderInvestigationList() {
    if (_investigationStore.loadingListInvestigation) {
      return SkeletonList();
    } else {
      if (_investigationStore.listInvestigation.length == 0) {
        return EmptyDataWidget(
          state: StatePage.NotFound,
          title: Strings.emptyInvestigation,
        );
      } else {
        return ListView.separated(
          key: _investigationKey,
          scrollDirection: Axis.vertical,
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            return InvestigationCard(
              investigation: _investigationStore.listInvestigation[index],
              onCallback: (){
                _onRefresh();
              },
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return 0.height;
          },
          itemCount: _investigationStore.listInvestigation.length,
        );
      }
    }
  }

  Widget _buildFilter() {
    return Row(
      children: [
        Expanded(
          child: Container(
            margin: EdgeInsets.fromLTRB(0, 20, 0, 10),
            width: double.infinity,
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              border: Border.all(
                color: HCColorGreyLightV3,
                width: 1,
              ),
              borderRadius: Style.radiusAll(spacing_standard),
              color: HCColorGreyLightV3,
            ),
            child: Row(
              children: [
                TextField(
                  autofocus: false,
                  controller: _keywordController,
                  textInputAction: TextInputAction.search,
                  decoration: InputDecoration.collapsed(
                    hintText: 'Gõ để tìm kiếm',
                    hintStyle: Style.typography.copyWith(
                      color: HCColorGreyLightV5,
                    ),
                  ),
                  textAlign: TextAlign.left,
                ).paddingLeft(0).expand(),
                Icon(HCIcon.equalizer1).onTap((){
                  FocusScope.of(context).unfocus();
                  bottomSheet(
                    context: context,
                    singleChildScroll: true,
                    child: SurveyFilterBottomSheet(
                      listFilterStatus: _listFilterStatus,
                      listFilterTypeCustomer: _listFilterTypeCustomer,
                      campaign: _campaign,
                      areaId: _areaId,
                      isSearchTypeSurveyCustomer: BooleanType.True,
                      onChangeFilter: (List<InvestigationStatusEnum> listInvestigationStatusEnum, List<CustomerType2> customerType, Campaign campaign,double areaId) {
                        this.handleChangeFilterStatus(listInvestigationStatusEnum, customerType, campaign,areaId);
                      },
                    ),
                  );
                })
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
