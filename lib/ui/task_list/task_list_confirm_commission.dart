import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:home_care_partner/constants/assets.dart';
import 'package:home_care_partner/constants/colors.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/font_size.dart';
import 'package:home_care_partner/constants/index.dart';
import 'package:home_care_partner/constants/spacing.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/constants/style.dart';
import 'package:home_care_partner/models/order/args/order.arg.dart';
import 'package:home_care_partner/models/order/order_status.dart';
import 'package:home_care_partner/stores/order/order_store.dart';
import 'package:home_care_partner/stores/user/user_store.dart';
import 'package:home_care_partner/ui/direct_app/alert_checker_widget.dart';
import 'package:home_care_partner/widgets/bottom_sheet/bar_modal_bottom_sheet.dart';
import 'package:home_care_partner/widgets/bottom_sheet/order_filter_bottom_sheet.dart';
import 'package:home_care_partner/widgets/bottom_sheet/widget_bottom_sheet.dart';
import 'package:home_care_partner/widgets/card/commission_order_card.dart';
import 'package:home_care_partner/widgets/card/task_list_card.dart';
import 'package:home_care_partner/widgets/empty_widget.dart';
import 'package:home_care_partner/widgets/infinity_scroll.dart';
import 'package:home_care_partner/widgets/skeleton/skeleton_list.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class TaskListConfirmCommission extends StatefulWidget {
  final TaskListFilterArgs taskListFilterArgs;

  final bool isAssignmentTab;

  const TaskListConfirmCommission({
    Key key,
    this.isAssignmentTab = false,
    this.taskListFilterArgs,
  }) : super(key: key);

  @override
  _TaskListConfirmCommission createState() {
    return _TaskListConfirmCommission();
  }
}

class _TaskListConfirmCommission extends State<TaskListConfirmCommission>
    with AutomaticKeepAliveClientMixin<TaskListConfirmCommission> {
  List<OrderType> _orderTypes;

  List<OrderSubStatusModel> listStatusSearch = [];

  DateTime _startDate;

  DateTime _endDate;

  TextEditingController _keywordController = TextEditingController();

  OrderSellerStatus _orderSellerStatus;

  static const _pageSize = DEFAULT_PAGE_SIZE;
  var _pageIndex = DEFAULT_PAGE_INDEX;

  RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  //stores:---------------------------------------------------------------------
  OrderStore _orderStore;

  CancelToken _cancelToken = CancelToken();

  String currentSearch = '';
  String previousSearch = '';

  FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    _orderStore = Provider.of<OrderStore>(context);

    listStatusSearch = [
      OrderSubStatusModel(
        statusLabel: 'Tất cả',
        icon: Assets.icSearchAll,
        orderSellerStatus: null,
      ),
      OrderSubStatusModel(
        statusLabel: 'Chờ xác nhận',
        icon: Assets.icWarning,
        orderSellerStatus: OrderSellerStatus.Waiting,
      ),
      OrderSubStatusModel(
        statusLabel: 'Đã xác nhận',
        icon: Assets.icCheckCircle,
        orderSellerStatus: OrderSellerStatus.Approved,
      ),
      OrderSubStatusModel(
        statusLabel: 'Từ chối',
        icon: Assets.icCancel,
        orderSellerStatus: OrderSellerStatus.Deny,
      ),
    ];

    Future.delayed(Duration(milliseconds: 0), () {
      var initFilter = widget.taskListFilterArgs;

      if (initFilter != null) {
        setState(() {
          _orderTypes = initFilter.orderTypes;
          _startDate = initFilter.startDate;
          _endDate = initFilter.endDate;
        });
      }
    });
    _onRefresh();
  }

  @override
  void dispose() async {
    super.dispose();
    _orderStore.dispose();
    _keywordController.dispose();
  }

  void _onRefresh() async {
    var canLoadOrder =
        _orderStore.isLoading != true || _orderStore.searchByStatus == false;
    if (canLoadOrder) {
      // monitor network fetch
      fetchData(DEFAULT_PAGE_INDEX, true);
      // if failed,use refreshFailed()
      _refreshController.refreshCompleted();
    }
  }

  void _onLoading() async {
    if ((_orderStore.orders ?? []).length >= _pageSize * (_pageIndex + 1)) {
      _pageIndex += 1;
      // monitor network fetch
      fetchData(_pageIndex, false);
    } else {
      _refreshController.loadNoData();
    }
  }

  fetchData(int pageIndex, bool isReset) {
    Future.delayed(Duration(milliseconds: 0), () async {
      var where = CommissionOrderWhere(
        pageSize: _pageSize,
        pageIndex: pageIndex,
        sortDirection: SortDirection.DESC,
      );

      if (_orderSellerStatus != null) {
        where.status = _orderSellerStatus;
      }

      if ((_orderTypes ?? []).length != 0) {
        where.orderTypes = _orderTypes;
      }

      if (_startDate != null) {
        where.startDate = _startDate;
      }

      if (_endDate != null) {
        where.endDate = _endDate;
      }

      where.keyword = _keywordController.text;

      _cancelToken.cancel('next_status');

      _cancelToken = CancelToken();

      where.cancelToken = _cancelToken;

      await _orderStore.getCommissionOrders(where, isReset);
      if (isReset) {
        _pageIndex = DEFAULT_PAGE_INDEX;
      }
    });
  }

  void _handleChangeFilter(
    List<OrderType> orderTypes,
    DateTime startDate,
    DateTime endDate,
  ) {
    setState(() {
      _orderTypes = orderTypes;
      _startDate = startDate;
      _endDate = endDate;
    });

    _onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return InfinityScroll(
      refreshController: _refreshController,
      onLoading: _onLoading,
      onRefresh: _onRefresh,
      padding: EdgeInsets.zero,
      child: Observer(
        builder: (BuildContext context) {
          var listOrders = _orderStore.listCommissionOrders ?? [];

          if (listOrders.length < _pageSize * (_pageIndex + 1)) {
            _refreshController.loadNoData();
          } else {
            _refreshController.loadComplete();
          }

          return Column(
            children: [
              // AlertChecker(
              //   features: 'COMMISSION',
              // ),
              _buildFilter(),
              ...listOrders
                  .map(
                    (order) => CommissionOrderCard(
                      order: order,
                      orderType: order.orderType,
                      orderId: order.orderId,
                      orderCode: order.code,
                      partnerName: order.orderCustomerName,
                      workType: order.name,
                      address:
                          "${order.address}${order?.aioWard?.name != null ? ' - ${order?.aioWard?.name}' : ''} - ${order?.aioArea?.name} - ${order?.catProvince?.name}",
                      schedulerDate: order.schedulerDate,
                      periodTime: order.periodTime,
                      whenComplete: (){
                        _onRefresh();
                      },
                    ).paddingSymmetric(
                      horizontal: spacing_standard_new,
                    ),
                  )
                  .toList(),
              _orderStore.loadingListCommissionOrders == true ? SkeletonList() : 0.width,
              listOrders.length == 0 && _orderStore.loadingListCommissionOrders != true
                  ? Container(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height * 0.5,
                      alignment: Alignment.center,
                      child: EmptyDataWidget(
                        state: StatePage.NotFound,
                        title: Strings.emptyOrder,
                      ),
                    )
                  : 0.width,
              refreshAfterUpdate(context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            FormBuilderTextField(
              name: 'keySearch',
              focusNode: _focusNode,
              style: Style.sfProTextNormal,
              controller: _keywordController,
              textInputAction: TextInputAction.search,
              onSubmitted: (value) {
                if (currentSearch != previousSearch) {
                  previousSearch = currentSearch;
                  _onRefresh();
                }
                _focusNode.unfocus();
              },
              onChanged: (value) {
                setState(() {
                  currentSearch = value;
                });
              },
              onTap: () {
                setState(() {});
              },
              decoration: InputDecoration(
                filled: true,
                fillColor: HCBackground,
                contentPadding: EdgeInsets.only(top: 16),
                hintText: 'Mã đơn hàng/tên/số điện thoại',
                hintStyle: Style.sfProTextNormal.copyWith(
                  color: HCColorIcon,
                ),
                suffixIcon: !currentSearch.isEmptyOrNull && _focusNode.hasFocus
                    ? Icon(
                        Icons.clear,
                        color: HCColorIcon,
                      ).onTap(() {
                        setState(() {
                          currentSearch = '';
                        });
                        _keywordController.text = '';
                        _focusNode.unfocus();
                        if (currentSearch != previousSearch) {
                          previousSearch = currentSearch;
                          _onRefresh();
                        }
                      })
                    : 0.height,
                prefixIcon: Icon(
                  Icons.search,
                  color: HCColorIcon,
                ),
                focusColor: HCColorPrimary,
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white),
                  borderRadius: BorderRadius.circular(8),
                ),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white),
                  borderRadius: BorderRadius.circular(8),
                ),
                disabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white),
                  borderRadius: BorderRadius.circular(8),
                ),
                border: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            )
                .paddingOnly(
                    right: spacing_standard_new, left: spacing_standard_new)
                .expand(),
            SvgPicture.asset(
              Assets.icFilter,
              color: (_orderTypes ?? []).length != 0 ||
                      _startDate != null ||
                      _endDate != null
                  ? HCColorPrimary
                  : Colors.black,
            ).paddingOnly(right: 16).onTap(() {
              bottomSheet(
                  context: context,
                  child: WidgetBottomSheet(
                    title: 'Bộ lọc tìm kiếm',
                    child: OrderFilterBottomSheet(
                      isAssignmentTab: widget.isAssignmentTab,
                      orderTypes: _orderTypes,
                      startDate: _startDate,
                      endDate: _endDate,
                      onChangeFilter: (
                        List<OrderType> orderTypes,
                        DateTime startDate,
                        DateTime endDate,
                      ) {
                        _handleChangeFilter(
                          orderTypes,
                          startDate,
                          endDate,
                        );
                      },
                    ),
                  ));
            }),
          ],
        ).paddingOnly(
          top: spacing_standard_new,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: listStatusSearch.map((status) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  status.icon,
                  width: 24,
                  height: 24,
                  color: _orderSellerStatus == status.orderSellerStatus
                      ? HCColorPrimary
                      : HCColorIcon,
                ),
                8.height,
                Text(
                  '${status.statusLabel}',
                  style: Style.sfProTextNormal.copyWith(
                    fontSize: FontSize.s10,
                    color: _orderSellerStatus == status.orderSellerStatus
                        ? HCColorPrimary
                        : HCColorIcon,
                    fontWeight: _orderSellerStatus == status.orderSellerStatus
                        ? FontWeight.w600
                        : FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ).center(),
              ],
            ).onTap(() {
              _orderSellerStatus = status.orderSellerStatus;
              setState(() {});
              _handleChangeFilter(
                _orderTypes,
                _startDate,
                _endDate,
              );
            });
          }).toList(),
        ).paddingSymmetric(
            vertical: padding_12, horizontal: spacing_standard_new),
      ],
    );
  }

  Widget refreshAfterUpdate(BuildContext context) {
    if (_orderStore.successUpdate == true) {
      Future.delayed(
        Duration(milliseconds: 0),
        () {
          _orderStore.resetUpdateStatus();
          setState(() {});
        },
      );
    }

    return 0.height;
  }

  @override
  bool get wantKeepAlive => true;
}
