import 'dart:io';
import 'package:flutter/material.dart';
import 'package:home_care_partner/constants/environments.dart';
import 'package:home_care_partner/extensions/string.dart';
import 'package:home_care_partner/layout/layout_bottom_navigation.dart';
import 'package:home_care_partner/ui/home_page/home_page.dart';
import 'package:home_care_partner/widgets/dialog/notify_dialog/notify_dialog.dart';
import 'package:home_care_partner/widgets/upgrader/src/upgrade_alert.dart';
import 'package:home_care_partner/widgets/upgrader/src/upgrade_messages.dart';
import 'package:home_care_partner/widgets/upgrader/src/upgrader.dart';
import 'package:launch_review/launch_review.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:home_care_partner/services/alert_manager_service.dart';
import 'package:home_care_partner/utils/alert_test_helper.dart';


class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with AlertManagerMixin, AlertTestMixin {
  _launchURL() async {
    var url = 'https://app.congtrinhviettel.com.vn/';
    await launch(url);
  }

  @override
  Widget build(BuildContext context) {
    return UpgradeAlert(
      countryCode: 'VN',
      durationToAlertAgain: Duration(hours: 6),
      canDismissDialog: Environments.SKIP_CHECK_VERSION.parseBool(),
      disableBackButton: !Environments.SKIP_CHECK_VERSION.parseBool(),
      debugLogging: true,
      showLater: Environments.SKIP_CHECK_VERSION.parseBool(),
      showIgnore: Environments.SKIP_CHECK_VERSION.parseBool(),
      appcastConfig: AppcastConfiguration(
        supportedOS: ['android', 'ios'],
        url: Platform.isIOS
            ? Environments.APPCAST_URL
            : Environments.APPCAST_URL.replaceFirst('ios', 'android'),
      ),
      onUpdate: () {
        if (Platform.isIOS) {
          LaunchReview.launch(
            androidAppId: Environments.APP_ID_ANDROID,
            iOSAppId: Environments.APP_ID_IOS,
            writeReview: false,
          );
        } else {
          _launchURL();
        }

        return true;
      },
      dialogStyle: Platform.isIOS
          ? UpgradeDialogStyle.cupertino
          : UpgradeDialogStyle.material,
      messages: UpgraderMessages(
        code: 'vi',
      ),
      minAppVersion: Platform.isIOS
          ? Environments.APP_VERSION_IOS
          : Environments.APP_VERSION_ANDROID,
      child: NotifyDialog(
        child: LayoutBottomNav(
          child: HomePageScreen(),
        ),
      ),
    );
  }
}
