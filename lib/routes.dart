import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/extensions/string.dart';
import 'package:home_care_partner/models/explanation/user_list_punishment_profile.dart';
import 'package:home_care_partner/models/feedback/feedback_model.dart';
import 'package:home_care_partner/models/invoice/invoice.arg.dart';
import 'package:home_care_partner/models/order/args/order.arg.dart';
import 'package:home_care_partner/models/order/order.dart';
import 'package:home_care_partner/models/route/route.arg.dart';
import 'package:home_care_partner/services/auth_services.dart';
import 'package:home_care_partner/services/navigation_service.dart';
import 'package:home_care_partner/stores/user/user_store.dart';
import 'package:home_care_partner/ui/auth/forgot_password/fill_phone_number.dart';
import 'package:home_care_partner/ui/auth/forgot_password/reset_password.dart';
import 'package:home_care_partner/ui/auth/register/register.dart';
import 'package:home_care_partner/ui/auth/verification/verification.dart';
import 'package:home_care_partner/ui/change_password/change_password.dart';
import 'package:home_care_partner/ui/chat/chat.dart';
import 'package:home_care_partner/ui/chat/chat_detail.dart';
import 'package:home_care_partner/ui/communication/communication.dart';
import 'package:home_care_partner/ui/communication/communication_page.dart';
import 'package:home_care_partner/ui/complain/create_ticket.dart';
import 'package:home_care_partner/ui/complain/widget/cim_explain_history_screen.dart';
import 'package:home_care_partner/ui/complain/widget/complain_history_screen.dart';
import 'package:home_care_partner/ui/complain/widget/contact_history_screen.dart';
import 'package:home_care_partner/ui/complain/widget/coordinat_screen.dart';
import 'package:home_care_partner/ui/contribute_idea/contribute_idea.dart';
import 'package:home_care_partner/ui/contribute_idea/contribute_idea_detail.dart';
import 'package:home_care_partner/ui/contribute_idea/create_contribute_idea.dart';
import 'package:home_care_partner/ui/customer_360/detail_contract.dart';
import 'package:home_care_partner/ui/customer_360/detail_customer_page.dart';
import 'package:home_care_partner/ui/customer_360/detail_survey.dart';
import 'package:home_care_partner/ui/customer_360/detail_ticket.dart';
import 'package:home_care_partner/ui/customer_360/detail_warrant.dart';
import 'package:home_care_partner/ui/customer_360/detail_warrant_history.dart';
import 'package:home_care_partner/ui/customer_360/list_contract.dart';
import 'package:home_care_partner/ui/customer_360/list_survey.dart';
import 'package:home_care_partner/ui/customer_360/list_ticket.dart';
import 'package:home_care_partner/ui/customer_360/list_warranty.dart';
import 'package:home_care_partner/ui/debt/debt.dart';
import 'package:home_care_partner/ui/delivery_bill/create_trans_warehouse/create_goods_delivery_note_page.dart';
import 'package:home_care_partner/ui/delivery_bill/delivery_bill_menu.dart';
import 'package:home_care_partner/ui/delivery_bill/detail_bill/detail_supply_warehouse.dart';
import 'package:home_care_partner/ui/detail_notification/detail_notification.dart';
import 'package:home_care_partner/ui/explanation/explanation.dart';
import 'package:home_care_partner/ui/explanation/explanation_detail.dart';
import 'package:home_care_partner/ui/explanation/explanation_history/explanation_history.dart';
import 'package:home_care_partner/ui/investigation/employee_investigation_detail.dart';
import 'package:home_care_partner/ui/investigation/investigation_detail.dart';
import 'package:home_care_partner/ui/investigation/investigation_detail_waiting.dart';
import 'package:home_care_partner/ui/investigation/question_examined.dart';
import 'package:home_care_partner/ui/invoice/invoice.dart';
import 'package:home_care_partner/ui/invoice/navigation_invoice.dart';
import 'package:home_care_partner/ui/invoice/view_invoice_page.dart';
import 'package:home_care_partner/ui/notification/notification.dart';
import 'package:home_care_partner/ui/one_pay/one_pay_view.dart';
import 'package:home_care_partner/ui/order/create_order/create_order.dart';
import 'package:home_care_partner/ui/order/create_order/deployment_information.dart';
import 'package:home_care_partner/ui/order/create_order/select_service.dart';
import 'package:home_care_partner/ui/order/create_order/select_service_appointment.dart';
import 'package:home_care_partner/ui/order/edited_order/edited_order_screen.dart';
import 'package:home_care_partner/ui/order/log_inventory_order_widget.dart';
import 'package:home_care_partner/ui/order/order_detail/order_change_package.dart';
import 'package:home_care_partner/ui/order/order_detail/order_detail.dart';
import 'package:home_care_partner/ui/order/order_detail/sale_point_order_detail.dart';
import 'package:home_care_partner/ui/order/order_shipping_detail/order_shipping_detail.dart';
import 'package:home_care_partner/ui/order/product_used/list_product_investigation_use.dart';
import 'package:home_care_partner/ui/order/product_used/list_product_use.dart';
import 'package:home_care_partner/ui/order/rating/rating.dart';
import 'package:home_care_partner/ui/order/shipping_order/shipping_order.dart';
import 'package:home_care_partner/ui/order/sold_order/sold_order_screen.dart';
import 'package:home_care_partner/ui/order/success_create_order.dart';
import 'package:home_care_partner/ui/play_video/video_app.dart';
import 'package:home_care_partner/ui/profile/auto_distribution/auto_distribution.dart';
import 'package:home_care_partner/ui/profile/notification_setting/notification_setting.dart';
import 'package:home_care_partner/ui/profile/privacy/privacy.dart';
import 'package:home_care_partner/ui/profile/profile/profile_page.dart';
import 'package:home_care_partner/ui/profile/profile_ability/profile_ability.dart';
import 'package:home_care_partner/ui/profile/profile_catalog/profile_catalog.dart';
import 'package:home_care_partner/ui/profile/profile_document/profile_document.dart';
import 'package:home_care_partner/ui/profile/profile_management/profile_management.dart';
import 'package:home_care_partner/ui/profile/profile_tool/add_profile_tool.dart';
import 'package:home_care_partner/ui/profile/profile_tool/list_transfer_tool.dart';
import 'package:home_care_partner/ui/profile/profile_tool/profile_tool.dart';
import 'package:home_care_partner/ui/profile/profile_update/profile_update.dart';
import 'package:home_care_partner/ui/profile/request_budget/request_budget.dart';
import 'package:home_care_partner/ui/proposed_purchase/proposed_purchase.dart';
import 'package:home_care_partner/ui/proposed_purchase/proposed_purchase_create.dart';
import 'package:home_care_partner/ui/report_system_error/create_report_system_error.dart';
import 'package:home_care_partner/ui/report_system_error/list_reprot_error_created.dart';
import 'package:home_care_partner/ui/report_system_error/report_system_error.dart';
import 'package:home_care_partner/ui/report_system_error/report_system_error_detail.dart';
import 'package:home_care_partner/ui/sale_group/create_sale_group.dart';
import 'package:home_care_partner/ui/sale_group/detail_sale_group.dart';
import 'package:home_care_partner/ui/sale_point/create_sales_slip.dart';
import 'package:home_care_partner/ui/splash/not_found.dart';
import 'package:home_care_partner/ui/task_list/create_investigation.dart';
import 'package:home_care_partner/ui/task_list/mission_list.dart';
import 'package:home_care_partner/ui/task_list/page_task.dart';
import 'package:home_care_partner/ui/task_list/task_list_page.dart';
import 'package:home_care_partner/ui/task_list/task_list_screen.dart';
import 'package:home_care_partner/ui/task_list/widget/task_detail.dart';
import 'package:home_care_partner/ui/task_list/widget/task_plan.dart';
import 'package:home_care_partner/ui/time_sheet/time_sheet_management.dart';
import 'package:home_care_partner/ui/transaction/transaction.dart';
import 'package:home_care_partner/ui/viettel_pay/viettel_pay.dart';
import 'package:home_care_partner/ui/view_image/view_image.dart';
import 'package:home_care_partner/ui/web_view/web_view.dart';
import 'package:home_care_partner/models/notification/notification.dart' as m;
import 'package:home_care_partner/widgets/card/ticket_knpa_card.dart';
import 'models/complain/ticket_report.dart';
import 'models/customer_360/customer_360.arg.dart';
import 'models/customer_360/customer_360_dto.dart';
import 'models/delivery_bill/supply_stock_trans.dart';
import 'models/error_management/error_management_model.dart';
import 'models/error_management/error_system.arg.dart';
import 'models/image/image_response_model.dart';
import 'models/order/order.dart';
import 'models/profile/profile.dart';
import 'models/tasks/task.dart';
import 'ui/auth/login/login.dart';
import 'ui/complain/complain_detail.dart';
import 'ui/complain/widget/explain_history_screen.dart';
import 'middleware/route_guard_middleware.dart';
import 'ui/delivery_bill/bill_trans_myself/bill_trans_myself.dart';
import 'ui/delivery_bill/confirmation_bill/confirmation_bill_list.dart';
import 'ui/delivery_bill/create_trans_warehouse/create_trans_warehouse_page.dart';
import 'ui/delivery_bill/detail_bill/detail_bill.dart';
import 'ui/delivery_bill/personal_inventory/personal_inventory_page.dart';
import 'ui/home/<USER>';
import 'ui/order/create_order/create_order_by_saler.dart';
import 'ui/order/product_used/edit_product_use.dart';
import 'ui/question/detail_question.dart';
import 'ui/question/list_question.dart';
import 'ui/report_system_error/create_report_error_login.dart';
import 'ui/report_system_error/edit_report_error.dart';
import 'ui/sale_group/censorship.dart';
import 'ui/splash/splash.dart';
import 'ui/time_sheet/list_employee.dart';
import 'ui/view_image/view_list_image.dart';
import 'widgets/view_document_cim.dart';

class Routes {
  Routes._();

  //static variables
  static const String splash = '/';

  /// Helper function để tạo guarded route
  static WidgetBuilder _guardedRoute(String route, WidgetBuilder builder, {String? blockedMessage}) {
    return (BuildContext context) {
      return RouteGuardMiddleware.instance.guardedRouteBuilder(
        context,
        route,
        builder,
        blockedMessage: blockedMessage,
      );
    };
  }

  // auth
  static const String login = '/login';
  static const String register = '/register';
  static const String phone_number_forgot =
      '/forgot_password/fill_phone_number';
  static const String change_password_forgot =
      '/forgot_password/change_password';
  static const String change_password = '/change_password';
  static const String verification = '/verification';

  // end auth

  static const String home = '/home';
  static const String task_list = '/task_list';
  static const String page_task = '/page_task';
  static const String task_list_screen = '/task_list_screen';
  static const String communication = '/communication';

  // chat
  static const String chat = '/chat';
  static const String chat_detail = '/chat_detail';

  // profile
  static const String profile = '/profile';
  static const String profile_update = '/profile_update';
  static const String add_profile_tool = '/add_profile_tool';
  static const String list_transfer_tool = '/list_transfer_tool';
  static const String profile_management = '/profile_management';
  static const String profile_privacy = '/profile_privacy';
  static const String profile_notification_setting =
      '/profile_notification_setting';
  static const String profile_catalog = '/profile_catalog';
  static const String profile_ability = '/profile_ability';
  static const String profile_tool = '/profile_tool';
  static const String profile_document = '/profile_document';
  static const String transaction = '/transaction';
  static const String statistic = '/statistic';
  static const String debt = '/debt';
  static const String edited_order = '/edited_order';
  static const String sold_order = '/sold_order';
  static const String auto_distribution = '/auto_distribution';

  // order
  static const String order_detail = '/order_detail';
  static const String order_shipping_detail = '/order_shipping_detail';
  static const String order_detail_confirm = '/order_detail_confirm';
  static const String order_rating = '/order_rating';
  static const String create_order = '/create_order';
  static const String deployment_information = '/deployment_information';
  static const String select_service = '/select_service';
  static const String select_service_appointment = '/select_service_appointment';
  static const String create_invoice = '/create_invoice';
  static const String view_invoice_pdf = '/view_invoice_pdf';
  static const String list_invoice = '/list_invoice';
  static const String viettel_pay = '/viettel_pay';
  static const String create_order_by_saler = '/create_order_by_saler';

  // delivery bill
  static const String delivery_bill_menu = '/delivery_bill_menu';
  static const String personal_inventory = '/personal_inventory';
  static const String bill_trans_myself = '/bill_trans_myself';
  static const String detail_supply_warehouse = '/detail_supply_warehouse';
  static const String confirmation_bill = '/confirmation_bill';
  static const String detail_bill = '/detail_bill';
  static const String create_trans_warehouse = '/create_trans_warehouse';
  static const String create_goods_delivery_note_page = '/create_goods_delivery_note_page';

  // investigate
  static const String investigation_detail = '/investigation_detail';
  static const String question_examined = '/question_examined';
  static const String investigation_detail_waiting = '/investigation_detail_waiting';
  static const String employee_investigation_detail = '/employee_investigation_detail';
  static const String create_investigation = '/create_investigation';

  //sale_group
  static const String create_sale_group = '/create_sale_group';
  static const String detail_sale_group = '/detail_sale_group';
  static const String censorship = '/censorship';

  //proposed_purchase
  static const String proposed_purchase = '/proposed_purchase';
  static const String proposed_purchase_create = '/proposed_purchase_create';

  // other
  static const String notification = '/notification';
  static const String my_notification = '/my_notification';
  static const String web_view = '/web_view';
  static const String not_found = '/not_found';
  static const String my_order = '/my_order';
  static const String request_budget = '/request_budget';
  static const String report_system_error = 'report_system_error';
  static const String report_system_error_detail = 'report_system_error_detail';
  static const String create_report_system_error = 'create_report_system_error';
  static const String contribute_idea = 'contribute_idea';
  static const String contribute_idea_detail = 'contribute_idea_detail';
  static const String create_contribute_idea = 'create_contribute_idea';
  static const String play_video = 'play_video';
  static const String view_image = 'view_image';
  static const String view_list_image = 'view_list_image';
  static const String time_attendance_management = 'time_attendance_management';
  static const String list_employees = 'list_employees';
  static const String list_question = 'list_question';
  static const String detail_question = 'detail_question';
  static const String create_report_system_error_before_login = 'create_report_system_error_before_login';
  static const String report_error_created = 'report_error_created';
  static const viewDocumentCim = '/viewDocumentCim';
  static const edit_report_error = '/edit_report_error';
  static const String mission_detail = '/task_detail';
  static const String task_plan = '/task_plan';
  //complain
  static const String complain_detail = 'complain_detail';
  static const String coordinat_screen = 'coordinat_screen';
  static const String explain_history_screen = 'explain_history_screen';
  static const String cim_explain_history_screen = 'cim_explain_history_screen';
  static const String complain_history_screen = 'complain_history_screen';
  static const String detail_notification = 'detail_notification';
  static const String contact_history_screen = 'contact_history_screen';

  static const String success_create_order = 'success_create_order';
  static const String order_change_package = 'order_change_package';
  static const String messages = '/messages';

  static const String task_detail = '/task_detail';
  static const String list_product_use = '/list_product_use';
  static const String list_product_investigation_use = '/list_product_investigation_use';
  static const String edit_product_use = '/edit_product_use';
  static const String list_survey_cim = '/list_survey_cim';
  static const String detail_survey_cim = '/detail_survey_cim';
  static const String list_contract_cim = '/list_contract_cim';
  static const String detail_contract_cim = '/detail_contract_cim';
  static const String list_ticket_cim = '/list_ticket_cim';
  static const String detail_ticket_cim = '/detail_ticket_cim';
  static const String list_warranty_cim = '/list_warranty_cim';
  static const String detail_warranty_cim = '/detail_warranty_cim';
  static const String list_warranty_history_cim = '/list_warranty_history_cim';
  static const String detail_warranty_history_cim = '/detail_warranty_history_cim';

  // sale point sell order 
  static const String sale_point_order_detail = '/sale_point_order_detail';
  static const String create_sale_point_order_sell = '/create_sale_point_order_sell';

  ///TODO giải trình
  static const String explanation = 'explanation';
  static const String explanation_detail = 'explanation_detail';
  static const String create_ticket = '/create_ticket';
  static const String explanation_explain_history = 'explanation_explain_history';
  static const String customer_360 = 'customer_360';

  static const String mission = '/mission';

  // đơn vận chuyển
  static const String shipping_order = '/shipping_order';

  static const String one_pay = '/one_pay';

  static const String product_list_screen = '/product_list_screen';

  static final routes = <String, WidgetBuilder>{
    splash: (BuildContext context) => SplashScreen(),
    // auth
    login: (BuildContext context) => LoginScreen(),
    register: (BuildContext context) => RegisterScreen(),
    change_password: (BuildContext context) => ChangePasswordScreen(),
    change_password_forgot: (BuildContext context) =>
        ChangePasswordForgotScreen(),
    phone_number_forgot: (BuildContext context) => PhoneNumberForgotScreen(),
    verification: (BuildContext context) => VerificationScreen(),
    // end auth
    home: _guardedRoute('/home', (context) => HomeScreen()),
    task_list: _guardedRoute('/task_list', (context) {
      final args = ModalRoute.of(context).settings.arguments;
      return TaskListPage(
        taskListFilterArgs: args,
      );
    }),
    page_task: _guardedRoute('/page_task', (context) {
      final args = ModalRoute.of(context).settings.arguments;
      return PageViewTask(
        taskListFilterArgs: args,
      );
    }),
    task_list_screen: _guardedRoute('/task_list_screen', (context) {
      final args = ModalRoute.of(context).settings.arguments;
      TaskListFilterArgs taskListFilterArgs = args;
      return TaskListScreen(
        initialTab: 1,
        taskListFilterArgs: args,
        isNavigation: true,
        taskGroup: taskListFilterArgs.taskGroup,
      );
    }),
    communication: _guardedRoute('/communication', (context) => CommunicationPage()),
    //chat
    chat: (BuildContext context) => ChatScreen(),
    chat_detail: (BuildContext context) {
      final ChatDetailArgRoute args = ModalRoute.of(context).settings.arguments;
      return ChatDetailScreen(
        args: args,
      );
    },
    // profile
    profile: _guardedRoute('/profile', (context) => ProfilePage()),
    profile_management: _guardedRoute('/profile_management', (context) => ProfileManagement()),
    profile_privacy: _guardedRoute('/profile_privacy', (context) => PrivacyScreen()),
    profile_update: _guardedRoute('/profile_update', (context) => ProfileUpdateScreen()),
    profile_notification_setting: _guardedRoute('/profile_notification_setting', (context) =>
        NotificationSettingScreen()),
    profile_catalog: _guardedRoute('/profile_catalog', (context) => ProfileCatalogScreen()),
    profile_ability: (BuildContext context) {
      final ProfileAbilityArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return ProfileAbilityScreen(
        args: args,
      );
    },
    profile_tool: (BuildContext context) => ProfileToolScreen(),
    add_profile_tool: (BuildContext context) => AddProfileToolScreen(),
    list_transfer_tool: (BuildContext context) => ListTransferTool(),
    profile_document: (BuildContext context) => ProfileDocumentScreen(),
    // end profile
    transaction: (BuildContext context) => TransactionScreen(),
    auto_distribution: (BuildContext context) => AutoDistributionScreen(),
    // statistic: (BuildContext context) {
    //   final StatisticArgRoute args = ModalRoute.of(context).settings.arguments;
    //   return StatisticScreen(
    //     params: args,
    //   );
    // },
    // order
    order_detail: (BuildContext context) {
      final OrderDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return OrderDetailScreen(
        args: args,
      );
    },
    order_shipping_detail: (BuildContext context) {
      final OrderDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return OrderShippingDetailScreen(
        args: args,
      );
    },
    order_rating: (BuildContext context) {
      final OrderRatingArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return RatingCustomer(
        args: args,
      );
    },
    create_order: (BuildContext context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return CreateOrderScreen(args: args);
    },
    deployment_information: (BuildContext context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return DeploymentInformationPage(args: args);
    },
    select_service: (BuildContext context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return SelectService(args: args,);
    },
    select_service_appointment: (BuildContext context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return SelectServiceAppointment(args: args,);
    },
    create_order_by_saler: (BuildContext context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return CreateOrderBySaler(args: args);
    },
    create_invoice: (BuildContext context) {
      final InvoiceArg args =
          ModalRoute.of(context).settings.arguments;
      return Invoice(invoiceArg: args,);
    },
    view_invoice_pdf: (BuildContext context) {
      final InvoiceArg args =
          ModalRoute.of(context).settings.arguments;
      return ViewInvoiceScreen(invoiceArg: args,);
    },
    list_invoice: (BuildContext context) {
      return NavigationInvoiceScreen();
    },
    // other
    notification: (BuildContext context) => NotificationScreen(),
    my_notification: (BuildContext context) => CommunicationPage(
          args: NavBottomArgRoute(
            initialTab: 1,
          ),
        ),
    viettel_pay: (BuildContext context) {
      final ViettelPayArgRoute args = ModalRoute.of(context).settings.arguments;
      return ViettelPayScreen(
        args: args,
      );
    },
    web_view: (BuildContext context) {
      final WebViewArgRoute args = ModalRoute.of(context).settings.arguments;
      return WebViewScreen(
        args: args,
      );
    },
    debt: (BuildContext context) {
      final DebtArgRoute args = ModalRoute.of(context).settings.arguments;
      return DebtScreen(
        args: args,
      );
    },
    edited_order: (BuildContext context) {
      final StatisticArgRoute args = ModalRoute.of(context).settings.arguments;

      return EditedOrderScreen(params: args);
    },
    sold_order: (BuildContext context) {
      final StatisticArgRoute args = ModalRoute.of(context).settings.arguments;
      return SoldOrderScreen(params: args);
    },
    not_found: (BuildContext context) => NotFoundScreen(),
    my_order: _guardedRoute('/my_order', (context) => TaskListPage(
          args: NavBottomArgRoute(
            initialTab: 1,
          ),
        )),
    request_budget: (BuildContext context) => RequestBudgetScreen(),
    report_system_error: (BuildContext context) => ReportSystemError(),
    report_system_error_detail: (BuildContext context) {
      final ErrorManagementDTO args = ModalRoute.of(context).settings.arguments;
      return ReportSystemErrorDetail(args: args,);
    },
    create_report_system_error: (BuildContext context) {
      final ErrorSystemArg args = ModalRoute.of(context).settings.arguments;
      return CreateReportSystemError(
        args: args,
      );
    },
    contribute_idea: (BuildContext context) => ContributeIdea(),
    create_contribute_idea: (BuildContext context) => CreateContributeIdea(),
    play_video: (BuildContext context) {
      final String linkVideo = ModalRoute.of(context).settings.arguments;
      return VideoScreen(linkVideo: linkVideo,);
    },
    view_image: (BuildContext context) {
      final String src = ModalRoute.of(context).settings.arguments;
      return ViewImage(src: src,);
    },
    view_list_image: (BuildContext context) {
      final List<ImageResponseModel> src = ModalRoute.of(context).settings.arguments;
      return ViewListImage(listFiles: src,);
    },
    contribute_idea_detail: (BuildContext context) {
      final FeedbackModel args = ModalRoute.of(context).settings.arguments;
      return ContributeIdeaDetail(args: args);
    },
    investigation_detail: (BuildContext context) {
      final InvestigationDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return InvestigationDetailScreen(
        args: args,
      );
    },
    explanation_detail: (BuildContext context) {
      ExplanationDetailArgRoute args = ModalRoute.of(context).settings.arguments;
      return ExplanationDetailScreen(
        args: args,
      );
    },
    question_examined: (BuildContext context) {
      final QuestionExaminedScreenArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return QuestionExaminedScreen(
        args: args,
      );
    },
    employee_investigation_detail: (BuildContext context) {
      final EmployeeInvestigationScreenArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return EmployeeInvestigationDetailScreen(
        args: args,
      );
    },
    investigation_detail_waiting: (BuildContext context) {
      final InvestigationDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return InvestigationWaitingDetailScreen(
        args: args,
      );
    },
    create_investigation: (BuildContext context) {
      Order arg = ModalRoute.of(context).settings.arguments;
      return CreateInvestigation(order: arg,);
    },
    delivery_bill_menu: (BuildContext context) => DeliveryBillMenuScreen(),
    personal_inventory: (BuildContext context) => PersonalInventoryPage(),
    detail_supply_warehouse: (BuildContext context) {
      final SupplyStockTrans args = ModalRoute.of(context).settings.arguments;
      return DetailSupplyWareHouse(
        supplyStockTrans: args,
      );
    },
    confirmation_bill: (BuildContext context) => ListConfirmationBillPage(),
    bill_trans_myself: (BuildContext context) => BillTransMySelfPage(),
    detail_bill: (BuildContext context) {
      final DeliveryBillArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return DetailBillPage(
        args: args,
      );
    },
    // => DetailBillPage(),
    create_trans_warehouse: (BuildContext context) {
      final DeliveryBillArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return CreateTransWareHousePage(
        args: args,
      );
    },
    create_goods_delivery_note_page: (BuildContext context) {
      return CreateGoodsDeliveryNotePage();
    },

    create_sale_group: (BuildContext context) {
      return CreateSaleGroup();
    },
    detail_sale_group: (BuildContext context) {
            final String args =
          ModalRoute.of(context).settings.arguments;
      return DetailSaleGroup(id : args);
    },
    censorship: (BuildContext context) {
      final String args =
          ModalRoute.of(context).settings.arguments;
      return Censorship(id : args);
    },

    time_attendance_management: (BuildContext context) {
      final Profile arg = ModalRoute.of(context).settings.arguments;
      return TimeSheetManagement(
        user: arg,
      );
    },
    list_employees: (BuildContext context) {
      return ListEmployeeScreen(
      );
    },
    list_question: (BuildContext context) {
      return ListQuestion(
      );
    },
    detail_question: (BuildContext context) {
      final String arg = ModalRoute.of(context).settings.arguments;
      return DetailQuestion(
        id: arg,
      );
    },
    create_report_system_error_before_login: (BuildContext context) {
      return CreateReportSystemErrorLogin();
    },
    report_error_created: (BuildContext context) {
      return ReportSystemErrorCreated();
    },
    viewDocumentCim: (BuildContext context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return ViewDocumentFileCim(
        filePath: arg
      );
    },
    edit_report_error: (BuildContext context) {
      return EditReportSystemError();
    },
    proposed_purchase: (BuildContext context) {
      return ProposedPurchaseScreen();
    },
    proposed_purchase_create: (BuildContext context) {
      return ProposedPurchaseCreate();
    },

    complain_detail: (BuildContext context) {
      TicketArg arg = ModalRoute.of(context).settings.arguments ;
      return ComplainTicketDetail(id: arg.id, ticketCode: arg.ticketCode,);
    },
    coordinat_screen: (BuildContext context) {
      TicketReport arg = ModalRoute.of(context).settings.arguments;
      return CoordinatScreen(ticketReport: arg,);
    },
    explain_history_screen: (BuildContext context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return ExplainHistoryScreen(id: arg,);
    },
    cim_explain_history_screen: (BuildContext context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return CimExplainHistoryScreen(id: arg,);
    },
    complain_history_screen: (BuildContext context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return ConplainHistoryScreen(id: arg,);
    },
    contact_history_screen: (BuildContext context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return ContactHistoryScreen(id: arg,);
    },
    mission_detail: (BuildContext context) {
      var taskId = ModalRoute.of(context).settings.arguments;
      bool isFromNotificationDetail;
      TaskDetailArgRoute task;
      if (ModalRoute.of(context).settings.arguments is TaskDetailArgRoute) {
        task  = ModalRoute.of(context).settings.arguments;
        taskId = task.taskId;
        isFromNotificationDetail = true;
      }
      return MissionDetail(taskDetailArgRoute: TaskDetailArgRoute(taskId: taskId,isFromNotification: isFromNotificationDetail ),
      );
    },
    task_plan: (BuildContext context) {
      final Task task = ModalRoute.of(context).settings.arguments;
      return TaskPlan(
        task: task,
      );
    },
    success_create_order: (BuildContext context) {
      OrderDetailArgRoute arg = ModalRoute.of(context).settings.arguments;
      return SuccessCreateOrder(arg: arg,);
    },
    detail_notification: (BuildContext context) {
      m.Notification arg = ModalRoute.of(context).settings.arguments;
      return DetailNotificationScreen(noti: arg,);
    },
    order_change_package: (BuildContext context) {
      final ChangePackageArgRoute arg = ModalRoute.of(context).settings.arguments;
      return OrderChangePackagePage(args: arg,);
    },
    list_product_use: (BuildContext context) {
      final Order arg = ModalRoute.of(context).settings.arguments;
      return ListProductUse(order: arg,);
    },
    list_product_investigation_use: (BuildContext context) {
      final Order arg = ModalRoute.of(context).settings.arguments;
      return ListProductUseInvestigation(order: arg,);
    },
    edit_product_use: (BuildContext context) {
      final Order arg = ModalRoute.of(context).settings.arguments;
      return EditProductUse(order: arg,);
    },
    messages: _guardedRoute('/messages', (context) {
      return ChatScreenBottomBar();
    }),
    //TODO tao man hinh gia trinh
    explanation: (BuildContext context) {
      return Explanation();
    },
    customer_360: (BuildContext context) {
      final Customer360Input arg = ModalRoute.of(context).settings.arguments;
      return DetailCustomerPage(arg: arg,);
    },
    list_survey_cim: (BuildContext context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListSurveyPage(arg: arg,);
    },
    detail_survey_cim: (BuildContext context) {
      final SurveyCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailSurveyScreen(survey: arg,);
    },
    list_contract_cim: (BuildContext context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListContractPage(arg: arg,);
    },
    detail_contract_cim: (BuildContext context) {
      final ContractCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailContractScreen(contract: arg,);
    },
    list_ticket_cim: (BuildContext context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListTicketPage(arg: arg,);
    },
    detail_ticket_cim: (BuildContext context) {
      final TicketCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailTicketScreen(ticket: arg,);
    },
    list_warranty_cim: (BuildContext context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListWarrantPage(arg: arg,);
    },
    detail_warranty_cim: (BuildContext context) {
      final WarrantyCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailWarrantScreen(warranty: arg,);
    },
    list_warranty_history_cim: (BuildContext context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListWarrantPage(arg: arg,);
    },
    detail_warranty_history_cim: (BuildContext context) {
      final WarrantyHistoryCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailWarrantHistoryScreen(warranty: arg,);
    },
    sale_point_order_detail: (BuildContext context) {
      final SalePointOrderDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return SalePointOrderDetailScreen(
        args: args,
      );
    },
    create_sale_point_order_sell: (BuildContext context) {
      return CreateSalesSlipScreen();
    },
    mission: (BuildContext context) {
      return MissionList();
    },
    explanation_explain_history: (BuildContext context) {
      ExplanationHistoryArgRoute arg = ModalRoute.of(context).settings.arguments;
      return ExplanationHistory(arg: arg);
    },
    create_ticket: (BuildContext context) {
      return CreateTicketScreen();
    },
    //shippinmg order
    shipping_order: (BuildContext context) {
      return ShippingOrderScreen();
    },
    one_pay: (BuildContext context) {
      final args =
          ModalRoute.of(context).settings.arguments;
      return OnePayView(
          arguments: args,
      );
    },
    product_list_screen: (BuildContext context) {
      final args =
          ModalRoute.of(context).settings.arguments;
      return ProductListScreen(
          orderId: args,
      );
    },
  };
}

class DeeplinkRoute {
  final String link;

  DeeplinkRoute(this.link);

  void redirect() async {
    var getIt = GetIt.instance;

    print('link..... ${this.link}');
    var latestLink = Uri.parse(this.link);
    var redirectPath = latestLink.path;
    dynamic arg;

    var isLoggedIn = await AuthServices().checkLoggedIn(false);

    if (isLoggedIn == false) {
      redirectPath = Routes.login;
    } else {
      var params = latestLink.queryParameters;
      print('params------------$params');

      if (!redirectPath.isNotEmptyString) {
        redirectPath = Routes.home;
      }

      switch (redirectPath) {
        case Routes.order_detail:
          {
            arg = OrderDetailArgRoute(
              id: params['id'],
            );
            break;
          }
        case Routes.list_transfer_tool:
          break;
        case Routes.investigation_detail:
          {
            arg = InvestigationDetailArgRoute(
              id: params['id'],
            );
            break;
          }
        case Routes.chat_detail:
          {
            var currentUser = getIt<UserStore>().user;
            if (currentUser != null) {
              arg = ChatDetailArgRoute(
                chatGroupKey: params['chatGroupKey'],
                receiveName: params['receiveName'],
                receiverId: params['receiverId'],
                senderId: currentUser.id,
                senderName: currentUser.name,
              );
            } else {
              redirectPath = Routes.login;
            }

            break;
          }
        case Routes.profile_ability:
          {
            arg = ProfileAbilityArgRoute(
              isRoot: false,
              parentId: params['parentId'],
              level: params['level'].parseToInt(),
              title: params['title'],
            );
            break;
          }
        case Routes.mission_detail:
          arg = TaskDetailArgRoute(
            taskId: params['id'],
            isFromNotification: true,
          );
          break;
        case Routes.order_detail_confirm:
          arg = OrderDetailArgRoute(
            id: params['id'],
            routerOrder: Strings.router_by_commission_order,
          );
          break;
        case Routes.order_shipping_detail:
          arg = OrderDetailArgRoute(
            id: params['id'],
            orderType: OrderType.Shipping
          );
          break;
        case Routes.explanation_detail: {
          arg = ExplanationDetailArgRoute(
            punishmentProfiles: PunishmentProfiles(
              id: params['punishmentProfile'],
              fromNotification: true,
            ),
          );
          break;
        }
        default:
          break;
      }
    }

    if (redirectPath == Routes.order_detail_confirm) {
      redirectPath = Routes.order_detail;
    }

    if (redirectPath == Routes.order_shipping_detail) {
      redirectPath = Routes.order_shipping_detail;
    }

    print('redirectPath----------------- $redirectPath');
    getIt<NavigationService>().pushNamed(
      redirectPath,
      arguments: arg,
    );
  }
}
