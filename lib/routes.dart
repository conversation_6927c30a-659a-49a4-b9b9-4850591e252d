import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:home_care_partner/constants/enum.dart';
import 'package:home_care_partner/constants/strings.dart';
import 'package:home_care_partner/extensions/string.dart';
import 'package:home_care_partner/models/explanation/user_list_punishment_profile.dart';
import 'package:home_care_partner/models/feedback/feedback_model.dart';
import 'package:home_care_partner/models/invoice/invoice.arg.dart';
import 'package:home_care_partner/models/order/args/order.arg.dart';
import 'package:home_care_partner/models/order/order.dart';
import 'package:home_care_partner/models/route/route.arg.dart';
import 'package:home_care_partner/services/auth_services.dart';
import 'package:home_care_partner/services/navigation_service.dart';
import 'package:home_care_partner/stores/user/user_store.dart';
import 'package:home_care_partner/ui/auth/forgot_password/fill_phone_number.dart';
import 'package:home_care_partner/ui/auth/forgot_password/reset_password.dart';
import 'package:home_care_partner/ui/auth/register/register.dart';
import 'package:home_care_partner/ui/auth/verification/verification.dart';
import 'package:home_care_partner/ui/change_password/change_password.dart';
import 'package:home_care_partner/ui/chat/chat.dart';
import 'package:home_care_partner/ui/chat/chat_detail.dart';
import 'package:home_care_partner/ui/communication/communication.dart';
import 'package:home_care_partner/ui/communication/communication_page.dart';
import 'package:home_care_partner/ui/complain/create_ticket.dart';
import 'package:home_care_partner/ui/complain/widget/cim_explain_history_screen.dart';
import 'package:home_care_partner/ui/complain/widget/complain_history_screen.dart';
import 'package:home_care_partner/ui/complain/widget/contact_history_screen.dart';
import 'package:home_care_partner/ui/complain/widget/coordinat_screen.dart';
import 'package:home_care_partner/ui/contribute_idea/contribute_idea.dart';
import 'package:home_care_partner/ui/contribute_idea/contribute_idea_detail.dart';
import 'package:home_care_partner/ui/contribute_idea/create_contribute_idea.dart';
import 'package:home_care_partner/ui/customer_360/detail_contract.dart';
import 'package:home_care_partner/ui/customer_360/detail_customer_page.dart';
import 'package:home_care_partner/ui/customer_360/detail_survey.dart';
import 'package:home_care_partner/ui/customer_360/detail_ticket.dart';
import 'package:home_care_partner/ui/customer_360/detail_warrant.dart';
import 'package:home_care_partner/ui/customer_360/detail_warrant_history.dart';
import 'package:home_care_partner/ui/customer_360/list_contract.dart';
import 'package:home_care_partner/ui/customer_360/list_survey.dart';
import 'package:home_care_partner/ui/customer_360/list_ticket.dart';
import 'package:home_care_partner/ui/customer_360/list_warranty.dart';
import 'package:home_care_partner/ui/debt/debt.dart';
import 'package:home_care_partner/ui/delivery_bill/create_trans_warehouse/create_goods_delivery_note_page.dart';
import 'package:home_care_partner/ui/delivery_bill/delivery_bill_menu.dart';
import 'package:home_care_partner/ui/delivery_bill/detail_bill/detail_supply_warehouse.dart';
import 'package:home_care_partner/ui/detail_notification/detail_notification.dart';
import 'package:home_care_partner/ui/explanation/explanation.dart';
import 'package:home_care_partner/ui/explanation/explanation_detail.dart';
import 'package:home_care_partner/ui/explanation/explanation_history/explanation_history.dart';
import 'package:home_care_partner/ui/investigation/employee_investigation_detail.dart';
import 'package:home_care_partner/ui/investigation/investigation_detail.dart';
import 'package:home_care_partner/ui/investigation/investigation_detail_waiting.dart';
import 'package:home_care_partner/ui/investigation/question_examined.dart';
import 'package:home_care_partner/ui/invoice/invoice.dart';
import 'package:home_care_partner/ui/invoice/navigation_invoice.dart';
import 'package:home_care_partner/ui/invoice/view_invoice_page.dart';
import 'package:home_care_partner/ui/notification/notification.dart';
import 'package:home_care_partner/ui/one_pay/one_pay_view.dart';
import 'package:home_care_partner/ui/order/create_order/create_order.dart';
import 'package:home_care_partner/ui/order/create_order/deployment_information.dart';
import 'package:home_care_partner/ui/order/create_order/select_service.dart';
import 'package:home_care_partner/ui/order/create_order/select_service_appointment.dart';
import 'package:home_care_partner/ui/order/edited_order/edited_order_screen.dart';
import 'package:home_care_partner/ui/order/log_inventory_order_widget.dart';
import 'package:home_care_partner/ui/order/order_detail/order_change_package.dart';
import 'package:home_care_partner/ui/order/order_detail/order_detail.dart';
import 'package:home_care_partner/ui/order/order_detail/sale_point_order_detail.dart';
import 'package:home_care_partner/ui/order/order_shipping_detail/order_shipping_detail.dart';
import 'package:home_care_partner/ui/order/product_used/list_product_investigation_use.dart';
import 'package:home_care_partner/ui/order/product_used/list_product_use.dart';
import 'package:home_care_partner/ui/order/rating/rating.dart';
import 'package:home_care_partner/ui/order/shipping_order/shipping_order.dart';
import 'package:home_care_partner/ui/order/sold_order/sold_order_screen.dart';
import 'package:home_care_partner/ui/order/success_create_order.dart';
import 'package:home_care_partner/ui/play_video/video_app.dart';
import 'package:home_care_partner/ui/profile/auto_distribution/auto_distribution.dart';
import 'package:home_care_partner/ui/profile/notification_setting/notification_setting.dart';
import 'package:home_care_partner/ui/profile/privacy/privacy.dart';
import 'package:home_care_partner/ui/profile/profile/profile_page.dart';
import 'package:home_care_partner/ui/profile/profile_ability/profile_ability.dart';
import 'package:home_care_partner/ui/profile/profile_catalog/profile_catalog.dart';
import 'package:home_care_partner/ui/profile/profile_document/profile_document.dart';
import 'package:home_care_partner/ui/profile/profile_management/profile_management.dart';
import 'package:home_care_partner/ui/profile/profile_tool/add_profile_tool.dart';
import 'package:home_care_partner/ui/profile/profile_tool/list_transfer_tool.dart';
import 'package:home_care_partner/ui/profile/profile_tool/profile_tool.dart';
import 'package:home_care_partner/ui/profile/profile_update/profile_update.dart';
import 'package:home_care_partner/ui/profile/request_budget/request_budget.dart';
import 'package:home_care_partner/ui/proposed_purchase/proposed_purchase.dart';
import 'package:home_care_partner/ui/proposed_purchase/proposed_purchase_create.dart';
import 'package:home_care_partner/ui/report_system_error/create_report_system_error.dart';
import 'package:home_care_partner/ui/report_system_error/list_reprot_error_created.dart';
import 'package:home_care_partner/ui/report_system_error/report_system_error.dart';
import 'package:home_care_partner/ui/report_system_error/report_system_error_detail.dart';
import 'package:home_care_partner/ui/sale_group/create_sale_group.dart';
import 'package:home_care_partner/ui/sale_group/detail_sale_group.dart';
import 'package:home_care_partner/ui/sale_point/create_sales_slip.dart';
import 'package:home_care_partner/ui/splash/not_found.dart';
import 'package:home_care_partner/ui/task_list/create_investigation.dart';
import 'package:home_care_partner/ui/task_list/mission_list.dart';
import 'package:home_care_partner/ui/task_list/page_task.dart';
import 'package:home_care_partner/ui/task_list/task_list_page.dart';
import 'package:home_care_partner/ui/task_list/task_list_screen.dart';
import 'package:home_care_partner/ui/task_list/widget/task_detail.dart';
import 'package:home_care_partner/ui/task_list/widget/task_plan.dart';
import 'package:home_care_partner/ui/time_sheet/time_sheet_management.dart';
import 'package:home_care_partner/ui/transaction/transaction.dart';
import 'package:home_care_partner/ui/viettel_pay/viettel_pay.dart';
import 'package:home_care_partner/ui/view_image/view_image.dart';
import 'package:home_care_partner/ui/web_view/web_view.dart';
import 'package:home_care_partner/models/notification/notification.dart' as m;
import 'package:home_care_partner/widgets/card/ticket_knpa_card.dart';
import 'models/complain/ticket_report.dart';
import 'models/customer_360/customer_360.arg.dart';
import 'models/customer_360/customer_360_dto.dart';
import 'models/delivery_bill/supply_stock_trans.dart';
import 'models/error_management/error_management_model.dart';
import 'models/error_management/error_system.arg.dart';
import 'models/image/image_response_model.dart';
import 'models/order/order.dart';
import 'models/profile/profile.dart';
import 'models/tasks/task.dart';
import 'ui/auth/login/login.dart';
import 'ui/complain/complain_detail.dart';
import 'ui/complain/widget/explain_history_screen.dart';
import 'middleware/route_guard_middleware.dart';
import 'widgets/screen_wrapper/alert_screen_wrapper.dart';
import 'ui/delivery_bill/bill_trans_myself/bill_trans_myself.dart';
import 'ui/delivery_bill/confirmation_bill/confirmation_bill_list.dart';
import 'ui/delivery_bill/create_trans_warehouse/create_trans_warehouse_page.dart';
import 'ui/delivery_bill/detail_bill/detail_bill.dart';
import 'ui/delivery_bill/personal_inventory/personal_inventory_page.dart';
import 'ui/home/<USER>';
import 'ui/order/create_order/create_order_by_saler.dart';
import 'ui/order/product_used/edit_product_use.dart';
import 'ui/question/detail_question.dart';
import 'ui/question/list_question.dart';
import 'ui/report_system_error/create_report_error_login.dart';
import 'ui/report_system_error/edit_report_error.dart';
import 'ui/sale_group/censorship.dart';
import 'ui/splash/splash.dart';
import 'ui/time_sheet/list_employee.dart';
import 'ui/view_image/view_list_image.dart';
import 'widgets/view_document_cim.dart';

class Routes {
  Routes._();

  //static variables
  static const String splash = '/';

  /// Helper function để tạo route với alert wrapper
  static WidgetBuilder _alertRoute(String route, WidgetBuilder builder, String screenName) {
    return (BuildContext context) {
      final widget = builder(context);
      return wrapWithAlert(
        widget,
        screenName,
        routeName: route,
      );
    };
  }

  /// Helper function để tạo route không cần alert (auth screens, etc.)
  static WidgetBuilder _normalRoute(WidgetBuilder builder) {
    return builder;
  }

  // auth
  static const String login = '/login';
  static const String register = '/register';
  static const String phone_number_forgot =
      '/forgot_password/fill_phone_number';
  static const String change_password_forgot =
      '/forgot_password/change_password';
  static const String change_password = '/change_password';
  static const String verification = '/verification';

  // end auth

  static const String home = '/home';
  static const String task_list = '/task_list';
  static const String page_task = '/page_task';
  static const String task_list_screen = '/task_list_screen';
  static const String communication = '/communication';

  // chat
  static const String chat = '/chat';
  static const String chat_detail = '/chat_detail';

  // profile
  static const String profile = '/profile';
  static const String profile_update = '/profile_update';
  static const String add_profile_tool = '/add_profile_tool';
  static const String list_transfer_tool = '/list_transfer_tool';
  static const String profile_management = '/profile_management';
  static const String profile_privacy = '/profile_privacy';
  static const String profile_notification_setting =
      '/profile_notification_setting';
  static const String profile_catalog = '/profile_catalog';
  static const String profile_ability = '/profile_ability';
  static const String profile_tool = '/profile_tool';
  static const String profile_document = '/profile_document';
  static const String transaction = '/transaction';
  static const String statistic = '/statistic';
  static const String debt = '/debt';
  static const String edited_order = '/edited_order';
  static const String sold_order = '/sold_order';
  static const String auto_distribution = '/auto_distribution';

  // order
  static const String order_detail = '/order_detail';
  static const String order_shipping_detail = '/order_shipping_detail';
  static const String order_detail_confirm = '/order_detail_confirm';
  static const String order_rating = '/order_rating';
  static const String create_order = '/create_order';
  static const String deployment_information = '/deployment_information';
  static const String select_service = '/select_service';
  static const String select_service_appointment = '/select_service_appointment';
  static const String create_invoice = '/create_invoice';
  static const String view_invoice_pdf = '/view_invoice_pdf';
  static const String list_invoice = '/list_invoice';
  static const String viettel_pay = '/viettel_pay';
  static const String create_order_by_saler = '/create_order_by_saler';

  // delivery bill
  static const String delivery_bill_menu = '/delivery_bill_menu';
  static const String personal_inventory = '/personal_inventory';
  static const String bill_trans_myself = '/bill_trans_myself';
  static const String detail_supply_warehouse = '/detail_supply_warehouse';
  static const String confirmation_bill = '/confirmation_bill';
  static const String detail_bill = '/detail_bill';
  static const String create_trans_warehouse = '/create_trans_warehouse';
  static const String create_goods_delivery_note_page = '/create_goods_delivery_note_page';

  // investigate
  static const String investigation_detail = '/investigation_detail';
  static const String question_examined = '/question_examined';
  static const String investigation_detail_waiting = '/investigation_detail_waiting';
  static const String employee_investigation_detail = '/employee_investigation_detail';
  static const String create_investigation = '/create_investigation';

  //sale_group
  static const String create_sale_group = '/create_sale_group';
  static const String detail_sale_group = '/detail_sale_group';
  static const String censorship = '/censorship';

  //proposed_purchase
  static const String proposed_purchase = '/proposed_purchase';
  static const String proposed_purchase_create = '/proposed_purchase_create';

  // other
  static const String notification = '/notification';
  static const String my_notification = '/my_notification';
  static const String web_view = '/web_view';
  static const String not_found = '/not_found';
  static const String my_order = '/my_order';
  static const String request_budget = '/request_budget';
  static const String report_system_error = 'report_system_error';
  static const String report_system_error_detail = 'report_system_error_detail';
  static const String create_report_system_error = 'create_report_system_error';
  static const String contribute_idea = 'contribute_idea';
  static const String contribute_idea_detail = 'contribute_idea_detail';
  static const String create_contribute_idea = 'create_contribute_idea';
  static const String play_video = 'play_video';
  static const String view_image = 'view_image';
  static const String view_list_image = 'view_list_image';
  static const String time_attendance_management = 'time_attendance_management';
  static const String list_employees = 'list_employees';
  static const String list_question = 'list_question';
  static const String detail_question = 'detail_question';
  static const String create_report_system_error_before_login = 'create_report_system_error_before_login';
  static const String report_error_created = 'report_error_created';
  static const viewDocumentCim = '/viewDocumentCim';
  static const edit_report_error = '/edit_report_error';
  static const String mission_detail = '/task_detail';
  static const String task_plan = '/task_plan';
  //complain
  static const String complain_detail = 'complain_detail';
  static const String coordinat_screen = 'coordinat_screen';
  static const String explain_history_screen = 'explain_history_screen';
  static const String cim_explain_history_screen = 'cim_explain_history_screen';
  static const String complain_history_screen = 'complain_history_screen';
  static const String detail_notification = 'detail_notification';
  static const String contact_history_screen = 'contact_history_screen';

  static const String success_create_order = 'success_create_order';
  static const String order_change_package = 'order_change_package';
  static const String messages = '/messages';

  static const String task_detail = '/task_detail';
  static const String list_product_use = '/list_product_use';
  static const String list_product_investigation_use = '/list_product_investigation_use';
  static const String edit_product_use = '/edit_product_use';
  static const String list_survey_cim = '/list_survey_cim';
  static const String detail_survey_cim = '/detail_survey_cim';
  static const String list_contract_cim = '/list_contract_cim';
  static const String detail_contract_cim = '/detail_contract_cim';
  static const String list_ticket_cim = '/list_ticket_cim';
  static const String detail_ticket_cim = '/detail_ticket_cim';
  static const String list_warranty_cim = '/list_warranty_cim';
  static const String detail_warranty_cim = '/detail_warranty_cim';
  static const String list_warranty_history_cim = '/list_warranty_history_cim';
  static const String detail_warranty_history_cim = '/detail_warranty_history_cim';

  // sale point sell order 
  static const String sale_point_order_detail = '/sale_point_order_detail';
  static const String create_sale_point_order_sell = '/create_sale_point_order_sell';

  ///TODO giải trình
  static const String explanation = 'explanation';
  static const String explanation_detail = 'explanation_detail';
  static const String create_ticket = '/create_ticket';
  static const String explanation_explain_history = 'explanation_explain_history';
  static const String customer_360 = 'customer_360';

  static const String mission = '/mission';

  // đơn vận chuyển
  static const String shipping_order = '/shipping_order';

  static const String one_pay = '/one_pay';

  static const String product_list_screen = '/product_list_screen';

  static final routes = <String, WidgetBuilder>{
    splash: (BuildContext context) => SplashScreen(),
    // auth
    login: (BuildContext context) => LoginScreen(),
    register: (BuildContext context) => RegisterScreen(),
    change_password: _alertRoute('/change_password', (context) => ChangePasswordScreen(), 'ChangePasswordScreen'),
    change_password_forgot: _normalRoute((context) => ChangePasswordForgotScreen()),
    phone_number_forgot: _normalRoute((context) => PhoneNumberForgotScreen()),
    verification: _normalRoute((context) => VerificationScreen()),
    // end auth
    home: _alertRoute('/home', (context) => HomeScreen(), 'HomeScreen'),
    task_list: _alertRoute('/task_list', (context) {
      final args = ModalRoute.of(context).settings.arguments;
      return TaskListPage(
        taskListFilterArgs: args,
      );
    }, 'TaskListPage'),
    page_task: _alertRoute('/page_task', (context) {
      final args = ModalRoute.of(context).settings.arguments;
      return PageViewTask(
        taskListFilterArgs: args,
      );
    }, 'PageViewTask'),
    task_list_screen: _alertRoute('/task_list_screen', (context) {
      final args = ModalRoute.of(context).settings.arguments;
      TaskListFilterArgs taskListFilterArgs = args;
      return TaskListScreen(
        initialTab: 1,
        taskListFilterArgs: args,
        isNavigation: true,
        taskGroup: taskListFilterArgs.taskGroup,
      );
    }, 'TaskListScreen'),
    communication: _alertRoute('/communication', (context) => CommunicationPage(), 'CommunicationPage'),
    //chat
    chat: _alertRoute('/chat', (context) => ChatScreen(), 'ChatScreen'),
    chat_detail: _alertRoute('/chat_detail', (context) {
      final ChatDetailArgRoute args = ModalRoute.of(context).settings.arguments;
      return ChatDetailScreen(
        args: args,
      );
    }, 'ChatDetailScreen'),
    // profile
    profile: _alertRoute('/profile', (context) => ProfilePage(), 'ProfilePage'),
    profile_management: _alertRoute('/profile_management', (context) => ProfileManagement(), 'ProfileManagement'),
    profile_privacy: _alertRoute('/profile_privacy', (context) => PrivacyScreen(), 'PrivacyScreen'),
    profile_update: _alertRoute('/profile_update', (context) => ProfileUpdateScreen(), 'ProfileUpdateScreen'),
    profile_notification_setting: _alertRoute('/profile_notification_setting', (context) =>
        NotificationSettingScreen(), 'NotificationSettingScreen'),
    profile_catalog: _alertRoute('/profile_catalog', (context) => ProfileCatalogScreen(), 'ProfileCatalogScreen'),
    profile_ability: _alertRoute('/profile_ability', (context) {
      final ProfileAbilityArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return ProfileAbilityScreen(
        args: args,
      );
    }, 'ProfileAbilityScreen'),
    profile_tool: _alertRoute('/profile_tool', (context) => ProfileToolScreen(), 'ProfileToolScreen'),
    add_profile_tool: _alertRoute('/add_profile_tool', (context) => AddProfileToolScreen(), 'AddProfileToolScreen'),
    list_transfer_tool: _alertRoute('/list_transfer_tool', (context) => ListTransferTool(), 'ListTransferTool'),
    profile_document: _alertRoute('/profile_document', (context) => ProfileDocumentScreen(), 'ProfileDocumentScreen'),
    // end profile
    transaction: _alertRoute('/transaction', (context) => TransactionScreen(), 'TransactionScreen'),
    auto_distribution: _alertRoute('/auto_distribution', (context) => AutoDistributionScreen(), 'AutoDistributionScreen'),
    order_detail: _alertRoute('/order_detail', (context) {
      final OrderDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return OrderDetailScreen(
        args: args,
      );
    }, 'OrderDetailScreen'),
    order_shipping_detail: _alertRoute('/order_shipping_detail', (context) {
      final OrderDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return OrderShippingDetailScreen(
        args: args,
      );
    }, 'OrderShippingDetailScreen'),
    order_rating: _alertRoute('/order_rating', (context) {
      final OrderRatingArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return RatingCustomer(
        args: args,
      );
    }, 'RatingCustomer'),
    create_order: _alertRoute('/create_order', (context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return CreateOrderScreen(args: args);
    }, 'CreateOrderScreen'),
    deployment_information: _alertRoute('/deployment_information', (context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return DeploymentInformationPage(args: args);
    }, 'DeploymentInformationPage'),
    select_service: _alertRoute('/select_service', (context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return SelectService(args: args,);
    }, 'SelectService'),
    select_service_appointment: _alertRoute('/select_service_appointment', (context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return SelectServiceAppointment(args: args,);
    }, 'SelectServiceAppointment'),
    create_order_by_saler: _alertRoute('/create_order_by_saler', (context) {
      final CreateOrderArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return CreateOrderBySaler(args: args);
    }, 'CreateOrderBySaler'),
    create_invoice: _alertRoute('/create_invoice', (context) {
      final InvoiceArg args =
          ModalRoute.of(context).settings.arguments;
      return Invoice(invoiceArg: args,);
    }, 'Invoice'),
    view_invoice_pdf: _alertRoute('/view_invoice_pdf', (context) {
      final InvoiceArg args =
          ModalRoute.of(context).settings.arguments;
      return ViewInvoiceScreen(invoiceArg: args,);
    }, 'ViewInvoiceScreen'),
    list_invoice: _alertRoute('/list_invoice', (context) {
      return NavigationInvoiceScreen();
    }, 'NavigationInvoiceScreen'),
    // other
    notification: _alertRoute('/notification', (context) => NotificationScreen(), 'NotificationScreen'),
    my_notification: _alertRoute('/my_notification', (context) => CommunicationPage(
          args: NavBottomArgRoute(
            initialTab: 1,
          ),
        ), 'CommunicationPage'),
    viettel_pay: _alertRoute('/viettel_pay', (context) {
      final ViettelPayArgRoute args = ModalRoute.of(context).settings.arguments;
      return ViettelPayScreen(
        args: args,
      );
    }, 'ViettelPayScreen'),
    web_view: _alertRoute('/web_view', (context) {
      final WebViewArgRoute args = ModalRoute.of(context).settings.arguments;
      return WebViewScreen(
        args: args,
      );
    }, 'WebViewScreen'),
    debt: _alertRoute('/debt', (context) {
      final DebtArgRoute args = ModalRoute.of(context).settings.arguments;
      return DebtScreen(
        args: args,
      );
    }, 'DebtScreen'),
    edited_order: _alertRoute('/edited_order', (context) {
      final StatisticArgRoute args = ModalRoute.of(context).settings.arguments;

      return EditedOrderScreen(params: args);
    }, 'EditedOrderScreen'),
    sold_order: _alertRoute('/sold_order', (context) {
      final StatisticArgRoute args = ModalRoute.of(context).settings.arguments;
      return SoldOrderScreen(params: args);
    }, 'SoldOrderScreen'),
    not_found: _alertRoute('/not_found', (context) => NotFoundScreen(), 'NotFoundScreen'),
    my_order: _alertRoute('/my_order', (context) => TaskListPage(
          args: NavBottomArgRoute(
            initialTab: 1,
          ),
        ), 'TaskListPage'),
    request_budget: _alertRoute('/request_budget', (context) => RequestBudgetScreen(), 'RequestBudgetScreen'),
    report_system_error: _alertRoute('/report_system_error', (context) => ReportSystemError(), 'ReportSystemError'),
    report_system_error_detail: _alertRoute('/report_system_error_detail', (context) {
      final ErrorManagementDTO args = ModalRoute.of(context).settings.arguments;
      return ReportSystemErrorDetail(args: args,);
    }, 'ReportSystemErrorDetail'),
    create_report_system_error: _alertRoute('/create_report_system_error', (context) {
      final ErrorSystemArg args = ModalRoute.of(context).settings.arguments;
      return CreateReportSystemError(
        args: args,
      );
    }, 'CreateReportSystemError'),
    contribute_idea: _alertRoute('/contribute_idea', (context) => ContributeIdea(), 'ContributeIdea'),
    create_contribute_idea: _alertRoute('/create_contribute_idea', (context) => CreateContributeIdea(), 'CreateContributeIdea'),
    play_video: _alertRoute('/play_video', (context) {
      final String linkVideo = ModalRoute.of(context).settings.arguments;
      return VideoScreen(linkVideo: linkVideo,);
    }, 'VideoScreen'),
    view_image: _alertRoute('/view_image', (context) {
      final String src = ModalRoute.of(context).settings.arguments;
      return ViewImage(src: src,);
    }, 'ViewImage'),
    view_list_image: _alertRoute('/view_list_image', (context) {
      final List<ImageResponseModel> src = ModalRoute.of(context).settings.arguments;
      return ViewListImage(listFiles: src,);
    }, 'ViewListImage'),
    contribute_idea_detail: _alertRoute('/contribute_idea_detail', (context) {
      final FeedbackModel args = ModalRoute.of(context).settings.arguments;
      return ContributeIdeaDetail(args: args);
    }, 'ContributeIdeaDetail'),
    investigation_detail: _alertRoute('/investigation_detail', (context) {
      final InvestigationDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return InvestigationDetailScreen(
        args: args,
      );
    }, 'InvestigationDetailScreen'),
    explanation_detail: _alertRoute('/explanation_detail', (context) {
      ExplanationDetailArgRoute args = ModalRoute.of(context).settings.arguments;
      return ExplanationDetailScreen(
        args: args,
      );
    }, 'ExplanationDetailScreen'),
    question_examined: _alertRoute('/question_examined', (context) {
      final QuestionExaminedScreenArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return QuestionExaminedScreen(
        args: args,
      );
    }, 'QuestionExaminedScreen'),
    employee_investigation_detail: _alertRoute('/employee_investigation_detail', (context) {
      final EmployeeInvestigationScreenArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return EmployeeInvestigationDetailScreen(
        args: args,
      );
    }, 'EmployeeInvestigationDetailScreen'),
    investigation_detail_waiting: _alertRoute('/investigation_detail_waiting', (context) {
      final InvestigationDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return InvestigationWaitingDetailScreen(
        args: args,
      );
    }, 'InvestigationWaitingDetailScreen'),
    create_investigation: _alertRoute('/create_investigation', (context) {
      Order arg = ModalRoute.of(context).settings.arguments;
      return CreateInvestigation(order: arg,);
    }, 'CreateInvestigation'),
    delivery_bill_menu: _alertRoute('/delivery_bill_menu', (context) => DeliveryBillMenuScreen(), 'DeliveryBillMenuScreen'),
    personal_inventory: _alertRoute('/personal_inventory', (context) => PersonalInventoryPage(), 'PersonalInventoryPage'),
    detail_supply_warehouse: _alertRoute('/detail_supply_warehouse', (context) {
      final SupplyStockTrans args = ModalRoute.of(context).settings.arguments;
      return DetailSupplyWareHouse(
        supplyStockTrans: args,
      );
    }, 'DetailSupplyWareHouse'),
    confirmation_bill: _alertRoute('/confirmation_bill', (context) => ListConfirmationBillPage(), 'ListConfirmationBillPage'),
    bill_trans_myself: _alertRoute('/bill_trans_myself', (context) => BillTransMySelfPage(), 'BillTransMySelfPage'),
    detail_bill: _alertRoute('/detail_bill', (context) {
      final DeliveryBillArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return DetailBillPage(
        args: args,
      );
    }, 'DetailBillPage'),
    // => DetailBillPage(),
    create_trans_warehouse: _alertRoute('/create_trans_warehouse', (context) {
      final DeliveryBillArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return CreateTransWareHousePage(
        args: args,
      );
    }, 'CreateTransWareHousePage'),
    create_goods_delivery_note_page: _alertRoute('/create_goods_delivery_note_page', (context) {
      return CreateGoodsDeliveryNotePage();
    }, 'CreateGoodsDeliveryNotePage'),

    create_sale_group: _alertRoute('/create_sale_group', (context) {
      return CreateSaleGroup();
    }, 'CreateSaleGroup'),
    detail_sale_group: _alertRoute('/detail_sale_group', (context) {
            final String args =
          ModalRoute.of(context).settings.arguments;
      return DetailSaleGroup(id : args);
    }, 'DetailSaleGroup'),
    censorship: _alertRoute('/censorship', (context) {
      final String args =
          ModalRoute.of(context).settings.arguments;
      return Censorship(id : args);
    }, 'Censorship'),

    time_attendance_management: _alertRoute('/time_attendance_management', (context) {
      final Profile arg = ModalRoute.of(context).settings.arguments;
      return TimeSheetManagement(
        user: arg,
      );
    }, 'TimeSheetManagement'),
    list_employees: _alertRoute('/list_employees', (context) {
      return ListEmployeeScreen(
      );
    }, 'ListEmployeeScreen'),
    list_question: _alertRoute('/list_question', (context) {
      return ListQuestion(
      );
    }, 'ListQuestion'),
    detail_question: _alertRoute('/detail_question', (context) {
      final String arg = ModalRoute.of(context).settings.arguments;
      return DetailQuestion(
        id: arg,
      );
    }, 'DetailQuestion'),
    create_report_system_error_before_login: _normalRoute((context) {
      return CreateReportSystemErrorLogin();
    }),
    report_error_created: _alertRoute('/report_error_created', (context) {
      return ReportSystemErrorCreated();
    }, 'ReportSystemErrorCreated'),
    viewDocumentCim: _alertRoute('/viewDocumentCim', (context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return ViewDocumentFileCim(
        filePath: arg
      );
    }, 'ViewDocumentFileCim'),
    edit_report_error: _alertRoute('/edit_report_error', (context) {
      return EditReportSystemError();
    }, 'EditReportSystemError'),
    proposed_purchase: _alertRoute('/proposed_purchase', (context) {
      return ProposedPurchaseScreen();
    }, 'ProposedPurchaseScreen'),
    proposed_purchase_create: _alertRoute('/proposed_purchase_create', (context) {
      return ProposedPurchaseCreate();
    }, 'ProposedPurchaseCreate'),

    complain_detail: _alertRoute('/complain_detail', (context) {
      TicketArg arg = ModalRoute.of(context).settings.arguments ;
      return ComplainTicketDetail(id: arg.id, ticketCode: arg.ticketCode,);
    }, 'ComplainTicketDetail'),
    coordinat_screen: _alertRoute('/coordinat_screen', (context) {
      TicketReport arg = ModalRoute.of(context).settings.arguments;
      return CoordinatScreen(ticketReport: arg,);
    }, 'CoordinatScreen'),
    explain_history_screen: _alertRoute('/explain_history_screen', (context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return ExplainHistoryScreen(id: arg,);
    }, 'ExplainHistoryScreen'),
    cim_explain_history_screen: _alertRoute('/cim_explain_history_screen', (context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return CimExplainHistoryScreen(id: arg,);
    }, 'CimExplainHistoryScreen'),
    complain_history_screen: _alertRoute('/complain_history_screen', (context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return ConplainHistoryScreen(id: arg,);
    }, 'ConplainHistoryScreen'),
    contact_history_screen: _alertRoute('/contact_history_screen', (context) {
      String arg = ModalRoute.of(context).settings.arguments;
      return ContactHistoryScreen(id: arg,);
    }, 'ContactHistoryScreen'),
    mission_detail: _alertRoute('/mission_detail', (context) {
      var taskId = ModalRoute.of(context).settings.arguments;
      bool isFromNotificationDetail;
      TaskDetailArgRoute task;
      if (ModalRoute.of(context).settings.arguments is TaskDetailArgRoute) {
        task  = ModalRoute.of(context).settings.arguments;
        taskId = task.taskId;
        isFromNotificationDetail = true;
      }
      return MissionDetail(taskDetailArgRoute: TaskDetailArgRoute(taskId: taskId,isFromNotification: isFromNotificationDetail ),
      );
    }, 'MissionDetail'),
    task_plan: _alertRoute('/task_plan', (context) {
      final Task task = ModalRoute.of(context).settings.arguments;
      return TaskPlan(
        task: task,
      );
    }, 'TaskPlan'),
    success_create_order: _alertRoute('/success_create_order', (context) {
      OrderDetailArgRoute arg = ModalRoute.of(context).settings.arguments;
      return SuccessCreateOrder(arg: arg,);
    }, 'SuccessCreateOrder'),
    detail_notification: _alertRoute('/detail_notification', (context) {
      m.Notification arg = ModalRoute.of(context).settings.arguments;
      return DetailNotificationScreen(noti: arg,);
    }, 'DetailNotificationScreen'),
    order_change_package: _alertRoute('/order_change_package', (context) {
      final ChangePackageArgRoute arg = ModalRoute.of(context).settings.arguments;
      return OrderChangePackagePage(args: arg,);
    }, 'OrderChangePackagePage'),
    list_product_use: _alertRoute('/list_product_use', (context) {
      final Order arg = ModalRoute.of(context).settings.arguments;
      return ListProductUse(order: arg,);
    }, 'ListProductUse'),
    list_product_investigation_use: _alertRoute('/list_product_investigation_use', (context) {
      final Order arg = ModalRoute.of(context).settings.arguments;
      return ListProductUseInvestigation(order: arg,);
    }, 'ListProductUseInvestigation'),
    edit_product_use: _alertRoute('/edit_product_use', (context) {
      final Order arg = ModalRoute.of(context).settings.arguments;
      return EditProductUse(order: arg,);
    }, 'EditProductUse'),
    messages: _alertRoute('/messages', (context) {
      return ChatScreenBottomBar();
    }, 'ChatScreenBottomBar'),
    //TODO tao man hinh gia trinh
    explanation: _alertRoute('/explanation', (context) {
      return Explanation();
    }, 'Explanation'),
    customer_360: _alertRoute('/customer_360', (context) {
      final Customer360Input arg = ModalRoute.of(context).settings.arguments;
      return DetailCustomerPage(arg: arg,);
    }, 'DetailCustomerPage'),
    list_survey_cim: _alertRoute('/list_survey_cim', (context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListSurveyPage(arg: arg,);
    }, 'ListSurveyPage'),
    detail_survey_cim: _alertRoute('/detail_survey_cim', (context) {
      final SurveyCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailSurveyScreen(survey: arg,);
    }, 'DetailSurveyScreen'),
    list_contract_cim: _alertRoute('/list_contract_cim', (context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListContractPage(arg: arg,);
    }, 'ListContractPage'),
    detail_contract_cim: _alertRoute('/detail_contract_cim', (context) {
      final ContractCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailContractScreen(contract: arg,);
    }, 'DetailContractScreen'),
    list_ticket_cim: _alertRoute('/list_ticket_cim', (context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListTicketPage(arg: arg,);
    }, 'ListTicketPage'),
    detail_ticket_cim: _alertRoute('/detail_ticket_cim', (context) {
      final TicketCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailTicketScreen(ticket: arg,);
    }, 'DetailTicketScreen'),
    list_warranty_cim: _alertRoute('/list_warranty_cim', (context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListWarrantPage(arg: arg,);
    }, 'ListWarrantPage'),
    detail_warranty_cim: _alertRoute('/detail_warranty_cim', (context) {
      final WarrantyCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailWarrantScreen(warranty: arg,);
    }, 'DetailWarrantScreen'),
    list_warranty_history_cim: _alertRoute('/list_warranty_history_cim', (context) {
      final Cus360DetailInput arg = ModalRoute.of(context).settings.arguments;
      return ListWarrantPage(arg: arg,);
    }, 'ListWarrantPage'),
    detail_warranty_history_cim: _alertRoute('/detail_warranty_history_cim', (context) {
      final WarrantyHistoryCIM arg = ModalRoute.of(context).settings.arguments;
      return DetailWarrantHistoryScreen(warranty: arg,);
    }, 'DetailWarrantHistoryScreen'),
    sale_point_order_detail: _alertRoute('/sale_point_order_detail', (context) {
      final SalePointOrderDetailArgRoute args =
          ModalRoute.of(context).settings.arguments;
      return SalePointOrderDetailScreen(
        args: args,
      );
    }, 'SalePointOrderDetailScreen'),
    create_sale_point_order_sell: _alertRoute('/create_sale_point_order_sell', (context) {
      return CreateSalesSlipScreen();
    }, 'CreateSalesSlipScreen'),
    mission: _alertRoute('/mission', (context) {
      return MissionList();
    }, 'MissionList'),
    explanation_explain_history: _alertRoute('/explanation_explain_history', (context) {
      ExplanationHistoryArgRoute arg = ModalRoute.of(context).settings.arguments;
      return ExplanationHistory(arg: arg);
    }, 'ExplanationHistory'),
    create_ticket: _alertRoute('/create_ticket', (context) {
      return CreateTicketScreen();
    }, 'CreateTicketScreen'),
    //shippinmg order
    shipping_order: _alertRoute('/shipping_order', (context) {
      return ShippingOrderScreen();
    }, 'ShippingOrderScreen'),
    one_pay: _alertRoute('/one_pay', (context) {
      final args =
          ModalRoute.of(context).settings.arguments;
      return OnePayView(
          arguments: args,
      );
    }, 'OnePayView'),
    product_list_screen: _alertRoute('/product_list_screen', (context) {
      final args =
          ModalRoute.of(context).settings.arguments;
      return ProductListScreen(
          orderId: args,
      );
    }, 'ProductListScreen'),
  };
}

class DeeplinkRoute {
  final String link;

  DeeplinkRoute(this.link);

  void redirect() async {
    var getIt = GetIt.instance;

    print('link..... ${this.link}');
    var latestLink = Uri.parse(this.link);
    var redirectPath = latestLink.path;
    dynamic arg;

    var isLoggedIn = await AuthServices().checkLoggedIn(false);

    if (isLoggedIn == false) {
      redirectPath = Routes.login;
    } else {
      var params = latestLink.queryParameters;
      print('params------------$params');

      if (!redirectPath.isNotEmptyString) {
        redirectPath = Routes.home;
      }

      switch (redirectPath) {
        case Routes.order_detail:
          {
            arg = OrderDetailArgRoute(
              id: params['id'],
            );
            break;
          }
        case Routes.list_transfer_tool:
          break;
        case Routes.investigation_detail:
          {
            arg = InvestigationDetailArgRoute(
              id: params['id'],
            );
            break;
          }
        case Routes.chat_detail:
          {
            var currentUser = getIt<UserStore>().user;
            if (currentUser != null) {
              arg = ChatDetailArgRoute(
                chatGroupKey: params['chatGroupKey'],
                receiveName: params['receiveName'],
                receiverId: params['receiverId'],
                senderId: currentUser.id,
                senderName: currentUser.name,
              );
            } else {
              redirectPath = Routes.login;
            }

            break;
          }
        case Routes.profile_ability:
          {
            arg = ProfileAbilityArgRoute(
              isRoot: false,
              parentId: params['parentId'],
              level: params['level'].parseToInt(),
              title: params['title'],
            );
            break;
          }
        case Routes.mission_detail:
          arg = TaskDetailArgRoute(
            taskId: params['id'],
            isFromNotification: true,
          );
          break;
        case Routes.order_detail_confirm:
          arg = OrderDetailArgRoute(
            id: params['id'],
            routerOrder: Strings.router_by_commission_order,
          );
          break;
        case Routes.order_shipping_detail:
          arg = OrderDetailArgRoute(
            id: params['id'],
            orderType: OrderType.Shipping
          );
          break;
        case Routes.explanation_detail: {
          arg = ExplanationDetailArgRoute(
            punishmentProfiles: PunishmentProfiles(
              id: params['punishmentProfile'],
              fromNotification: true,
            ),
          );
          break;
        }
        default:
          break;
      }
    }

    if (redirectPath == Routes.order_detail_confirm) {
      redirectPath = Routes.order_detail;
    }

    if (redirectPath == Routes.order_shipping_detail) {
      redirectPath = Routes.order_shipping_detail;
    }

    print('redirectPath----------------- $redirectPath');
    getIt<NavigationService>().pushNamed(
      redirectPath,
      arguments: arg,
    );
  }
}
