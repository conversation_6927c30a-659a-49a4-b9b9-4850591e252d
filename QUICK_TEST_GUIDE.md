# 🚀 Quick Test Guide - AlertManagerService

## ✅ **Lỗi đã sửa!**

Đã bỏ `AlertManagerProvider` và sử dụng cách đơn giản hơn:
- ✅ Initialize AlertManagerService trong `initState` của MyApp
- ✅ Không cần wrapper widget phức tạp
- ✅ Hoạt động ngay khi app khởi động

## 🧪 **Test ngay:**

### 1. **Mở app và check console:**
```
AlertManagerService: Initializing...
AlertManagerService: Fetching alerts from API...
AlertManagerService: Received X alerts
FeatureBlockingService: Updated alerts: X alerts for features: [...]
```

### 2. **Test AlertManagerService:**
```dart
// Navigate đến test screen
Navigator.pushNamed(context, '/alert_manager_simple_test');

// Test functions:
// - Initialize: Setup service
// - Refresh: Fetch từ API
// - Clear: Xóa cache
// - Debug Info: Print status
```

### 3. **Test Route Mapping:**
```dart
// Navigate đến test screen
Navigator.pushNamed(context, '/route_mapping_test');

// Test features:
// - Route mapping verification
// - AlertDialogWidget test
// - Navigation test
```

### 4. **Test với dữ liệu thực:**
```dart
// Với API data: [DETAIL_ORDER, DEBT, COMMISSION, INVOICE, CREATE_ORDER, profile_management]

Navigator.pushNamed(context, '/profile_management');
// → Sẽ hiển thị AlertDialogWidget nếu có alert!

Navigator.pushNamed(context, '/create_order');
// → Sẽ hiển thị AlertDialogWidget nếu có alert!

Navigator.pushNamed(context, '/debt');
// → Sẽ hiển thị AlertDialogWidget nếu có alert!
```

## 🔧 **Manual Control:**

### **Force refresh alerts:**
```dart
import 'package:home_care_partner/utils/alert_utils.dart';

// Trong bất kỳ widget nào:
await AlertUtils.refresh();
```

### **Clear alerts (khi logout):**
```dart
await AlertUtils.clear();
```

### **Check status:**
```dart
AlertUtils.printDebugInfo();

// Hoặc:
Map<String, dynamic> status = AlertUtils.getStatus();
print('Is Loading: ${status['isLoading']}');
print('Last Fetch: ${status['lastFetchTime']}');
```

## 📱 **Expected Behavior:**

### **App Start:**
1. MyApp.initState() → AlertManagerService.initialize()
2. Load cache → Call API if needed
3. Update FeatureBlockingService với alerts mới

### **Navigation:**
1. User navigate → AlertScreenWrapper check alerts
2. Nếu có alert → Show AlertDialogWidget
3. User interaction → Close hoặc open app store

### **AlertDialogWidget UI:**
- **isRequired: false** → Nút "Hủy" (đỏ) + "Đồng ý" (xanh)
- **isRequired: true** → Chỉ có nút "Đồng ý" (xanh)
- **Nút "Đồng ý"** → Mở App Store/Play Store

## 🎯 **Verify Success:**

### **Console Logs:**
```
// App start:
AlertManagerService: Initializing...
AlertManagerService: Fetching alerts from API...
AlertManagerService: Received 6 alerts
FeatureBlockingService: Updated alerts: 6 alerts for features: [DETAIL_ORDER, DEBT, COMMISSION, INVOICE, CREATE_ORDER, profile_management]

// Navigation:
AlertScreenWrapper: Checking route key: profile_management
AlertScreenWrapper: Found alert for /profile_management: Profile management alert
```

### **UI Behavior:**
- ✅ Popup hiển thị khi navigate đến màn hình có alert
- ✅ UI nhất quán với AlertDialogWidget
- ✅ isRequired flag hoạt động đúng
- ✅ Platform-specific app store links

## 🚀 **Ready to Use!**

**Hệ thống đã sẵn sàng thay thế AlertChecker:**
- ❌ Không cần AlertChecker widget nữa
- ✅ API được gọi tự động khi app start
- ✅ Alerts hiển thị đúng với AlertDialogWidget
- ✅ Route mapping hoạt động với API data
- ✅ Cache và performance tối ưu

**Test ngay để verify hoạt động! 🎉**
