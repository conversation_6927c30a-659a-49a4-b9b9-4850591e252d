# AlertManagerService - Thay thế AlertChecker

## 🎯 **Vấn đề đã giải quyết**

Bạn đã bỏ `AlertChecker` widget khỏi màn hình home, vậy cần một cách khác để:
1. **Gọi API `getAlerts`** để lấy dữ liệu
2. **Cập nhật `FeatureBlockingService`** với dữ liệu mới
3. **Quản lý lifecycle** của việc fetch alerts

## ✅ **Giải pháp: AlertManagerService**

### 1. **AlertManagerService** - Service chính
```dart
// Singleton service quản lý việc gọi API alerts
class AlertManagerService {
  // Auto initialize khi app khởi động
  Future<void> initialize();
  
  // Fetch alerts từ API nếu cần
  Future<void> fetchAlertsIfNeeded({bool forceRefresh = false});
  
  // Force refresh từ API
  Future<void> refreshAlerts();
  
  // Clear tất cả data
  Future<void> clearAll();
}
```

### 2. **AlertManagerProvider** - Wrapper cho app
```dart
// Đã thêm vào main.dart
AlertManagerProvider(
  child: MaterialApp(...),
)
```

### 3. **AlertUtils** - Utility functions
```dart
// Dễ dàng gọi từ bất kỳ đâu
await AlertUtils.initialize();
await AlertUtils.refresh();
await AlertUtils.fetchIfNeeded();
await AlertUtils.clear();
```

## 🔧 **Cách hoạt động**

### **Tự động (Đã setup):**
1. **App khởi động** → `AlertManagerProvider` tự động initialize
2. **HomeScreen load** → `AlertManagerMixin` tự động fetch if needed
3. **Cache 30 phút** → Tự động fetch lại khi hết hạn
4. **User navigate** → `AlertScreenWrapper` kiểm tra và hiển thị popup

### **Manual (Khi cần):**
```dart
// Force refresh (ví dụ: pull to refresh)
await AlertUtils.refresh();

// Clear khi logout
await AlertUtils.clear();

// Check status
AlertUtils.printDebugInfo();
```

## 📱 **Tích hợp vào app**

### 1. **Đã tự động hoạt động:**
- ✅ `main.dart` → `AlertManagerProvider` wrap MaterialApp
- ✅ `home.dart` → `AlertManagerMixin` added
- ✅ Tự động fetch khi app khởi động
- ✅ Tự động fetch khi vào HomeScreen

### 2. **Thêm vào các màn hình khác (optional):**
```dart
class MyScreen extends StatefulWidget {
  @override
  _MyScreenState createState() => _MyScreenState();
}

class _MyScreenState extends State<MyScreen> with AlertManagerMixin {
  // Tự động fetch alerts khi vào màn hình
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: () => refreshAlerts(), // Manual refresh
          ),
        ],
      ),
      body: ...,
    );
  }
}
```

### 3. **Thêm pull-to-refresh:**
```dart
RefreshIndicator(
  onRefresh: () => AlertUtils.forceRefresh(),
  child: ListView(...),
)
```

## 🧪 **Test và Debug**

### 1. **Test Screen**
```dart
// Navigate để test
Navigator.pushNamed(context, '/route_mapping_test');

// Features:
// - AlertDebugWidget hiển thị status
// - Buttons để Refresh/Clear
// - Test navigation với alerts
```

### 2. **Debug Commands**
```dart
// Print debug info
AlertUtils.printDebugInfo();

// Get status
Map<String, dynamic> status = AlertUtils.getStatus();
print('Is Loading: ${status['isLoading']}');
print('Last Fetch: ${status['lastFetchTime']}');

// Check alerts
List<AlertModel> alerts = FeatureBlockingService.instance.alerts;
print('Current alerts: ${alerts.map((a) => a.feature).toList()}');
```

### 3. **Verify hoạt động**
```dart
// 1. Mở app → Check console logs:
// "AlertManagerService: Initializing..."
// "AlertManagerService: Fetching alerts from API..."
// "AlertManagerService: Received X alerts"

// 2. Navigate đến màn hình có alert:
Navigator.pushNamed(context, '/profile_management');
// → Popup sẽ hiển thị nếu có alert

// 3. Force refresh:
await AlertUtils.refresh();
// → Gọi API lại và cập nhật alerts
```

## ⚙️ **Configuration**

### 1. **Fetch Interval**
```dart
// Trong AlertManagerService
static const int _FETCH_INTERVAL_MINUTES = 30; // Có thể thay đổi
```

### 2. **Cache Duration**
```dart
// Trong FeatureBlockingService
static const int _CACHE_DURATION_HOURS = 1; // Có thể thay đổi
```

### 3. **API Parameters**
```dart
// Trong AlertManagerService._fetchAlerts()
final alerts = await _alertService.getAlerts(
  systemName: "HSPartner",
  features: "ALL", // Có thể thay đổi
);
```

## 🔄 **Lifecycle Management**

### **App Start:**
1. `main.dart` → `AlertManagerProvider` initialize
2. `HomeScreen` → `AlertManagerMixin` fetch if needed
3. Cache loaded → API called if expired

### **User Navigation:**
1. Navigate to screen → `AlertScreenWrapper` check alerts
2. If alert found → Show `AlertDialogWidget`
3. User interaction → Close popup or open app store

### **Background Refresh:**
1. Every 30 minutes → Auto check if need refresh
2. If cache expired → Call API automatically
3. New alerts → Available for next navigation

### **User Logout:**
```dart
// Call khi user logout
await AlertUtils.clear();
```

## 🎉 **Kết quả**

### ✅ **Hoạt động tự động:**
- App khởi động → Fetch alerts
- Cache management → Tự động refresh
- Navigation → Tự động hiển thị popup
- Error handling → Không crash app

### ✅ **Dễ dàng control:**
- `AlertUtils.refresh()` → Force refresh
- `AlertUtils.clear()` → Clear khi logout
- Debug widget → Monitor status
- Console logs → Track hoạt động

### ✅ **Performance tối ưu:**
- Cache 1 giờ → Giảm API calls
- Fetch interval 30 phút → Không spam API
- Background loading → Không block UI
- Error resilient → Fallback to cache

## 🚀 **Ready to use!**

Hệ thống đã sẵn sàng hoạt động mà không cần `AlertChecker` widget. API sẽ được gọi tự động và alerts sẽ hiển thị đúng chỗ! 

**Test ngay:**
1. Mở app → Check console logs
2. Navigate `/route_mapping_test` → Test functions
3. Navigate `/profile_management` → Verify popup
4. Pull to refresh → Test manual refresh
