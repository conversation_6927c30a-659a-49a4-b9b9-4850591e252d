# 🚀 Simple Test Guide - AlertManagerService

## ✅ **Đ<PERSON> sửa tất cả lỗi!**

### **Vấn đề đã giải quyết:**
- ✅ Sửa import thiếu trong alert_utils.dart
- ✅ Bỏ test files phức tạp gây lỗi
- ✅ Tạo AlertTestHelper đơn giản
- ✅ Đơn giản hóa alert_utils.dart
- ✅ Thêm test mixin vào HomeScreen

## 🧪 **Cách test đơn giản:**

### **1. Test trong Console (Recommended):**

```dart
// Trong bất kỳ widget nào, gọi:
import 'package:home_care_partner/utils/alert_test_helper.dart';

// Test tất cả functions:
await AlertTestHelper.runAllTests();

// Hoặc test từng function:
await AlertTestHelper.testInitialize();
await AlertTestHelper.testRefresh();
AlertTestHelper.printDebugInfo();
```

### **2. Test trong HomeScreen:**

```dart
// HomeScreen đã có AlertTestMixin, có thể gọi:
await testAlertManager();        // Test tất cả
await testWithSampleData();      // Test với data mẫu
testRouteMapping();              // Test route mapping
printAlertDebug();               // Print debug info
```

### **3. Test với AlertUtils:**

```dart
import 'package:home_care_partner/utils/alert_utils.dart';

// Basic functions:
await AlertUtils.initialize();
await AlertUtils.refresh();
await AlertUtils.clear();
AlertUtils.printDebugInfo();
```

## 📱 **Expected Console Output:**

### **Khi app khởi động:**
```
AlertManagerService: Initializing...
AlertManagerService: Fetching alerts from API...
AlertManagerService: Received X alerts
FeatureBlockingService: Updated alerts: X alerts for features: [...]
```

### **Khi chạy test:**
```
🚀 Starting AlertManagerService Tests...
=== Testing AlertManagerService Initialize ===
✅ Initialize completed successfully
=== Testing with Sample Data ===
✅ Sample data loaded: 3 alerts
=== AlertManagerService Debug Info ===
Current Alerts: 3
  - profile_management: Profile management feature alert (required: false)
  - CREATE_ORDER: Create order feature alert (required: true)
  - DEBT: Debt feature alert (required: false)
🎉 All tests completed!
```

### **Khi navigate:**
```
AlertScreenWrapper: Checking route key: profile_management
AlertScreenWrapper: Found alert for /profile_management: Profile management feature alert
```

## 🎯 **Test Navigation:**

### **Test với sample data:**
```dart
// 1. Load sample data:
await AlertTestHelper.testWithSampleData();

// 2. Navigate để test popup:
Navigator.pushNamed(context, '/profile_management');
// → Sẽ hiển thị AlertDialogWidget!

Navigator.pushNamed(context, '/create_order');
// → Sẽ hiển thị AlertDialogWidget!

Navigator.pushNamed(context, '/debt');
// → Sẽ hiển thị AlertDialogWidget!
```

### **Test với API data thực:**
```dart
// 1. Refresh từ API:
await AlertUtils.refresh();

// 2. Check alerts:
AlertUtils.printDebugInfo();

// 3. Navigate để test:
Navigator.pushNamed(context, '/profile_management');
```

## 🔧 **Debug Commands:**

### **Check status:**
```dart
Map<String, dynamic> status = AlertUtils.getStatus();
print('Is Loading: ${status['isLoading']}');
print('Last Fetch: ${status['lastFetchTime']}');
print('Should Fetch: ${status['shouldFetch']}');
```

### **Check current alerts:**
```dart
List<AlertModel> alerts = FeatureBlockingService.instance.alerts;
print('Current alerts: ${alerts.map((a) => a.feature).toList()}');
```

### **Force refresh:**
```dart
await AlertUtils.forceRefresh();
```

### **Clear cache:**
```dart
await AlertUtils.clear();
```

## 🎉 **Verify Success:**

### **1. App Start Test:**
- ✅ Mở app → Check console logs
- ✅ Thấy "AlertManagerService: Initializing..."
- ✅ Thấy "AlertManagerService: Received X alerts"

### **2. Function Test:**
- ✅ Gọi `AlertTestHelper.runAllTests()`
- ✅ Thấy tất cả tests pass
- ✅ Thấy debug info hiển thị alerts

### **3. Navigation Test:**
- ✅ Load sample data
- ✅ Navigate đến `/profile_management`
- ✅ Thấy AlertDialogWidget hiển thị
- ✅ Test isRequired behavior

### **4. API Test:**
- ✅ Gọi `AlertUtils.refresh()`
- ✅ Thấy API call trong console
- ✅ Navigate để test với data thực

## 🚀 **Ready to Use!**

**Hệ thống đã sẵn sàng và đơn giản để test:**
- ❌ Không cần test screens phức tạp
- ✅ Test bằng console commands
- ✅ Test bằng mixins trong widgets
- ✅ Debug tools đầy đủ
- ✅ Hoạt động với AlertDialogWidget

**Test ngay để verify hoạt động! 🎉**
