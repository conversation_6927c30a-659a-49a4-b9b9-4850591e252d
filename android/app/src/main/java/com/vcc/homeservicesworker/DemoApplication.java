package com.vcc.homeservicesworker;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.lifecycle.ProcessLifecycleOwner;

import com.google.firebase.FirebaseApp;
import com.ipccsupportsdk.IPCCSupportSDK;

public class DemoApplication extends Application implements LifecycleObserver, Application.ActivityLifecycleCallbacks {

    @Override
    public void onCreate() {
        super.onCreate();
        IPCCSupportSDK.instance.setupDependencies(this);
        ProcessLifecycleOwner.get().getLifecycle().addObserver(this);
        FirebaseApp.initializeApp(this);
        registerActivityLifecycleCallbacks(this);
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
    public void onAppBackgrounded() {
        IPCCSupportSDK.instance.onLeaveHint();
    }

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle bundle) {

    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {
        IPCCSupportSDK.instance.onStart(activity);
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {

    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {

    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle bundle) {

    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {

    }
}
