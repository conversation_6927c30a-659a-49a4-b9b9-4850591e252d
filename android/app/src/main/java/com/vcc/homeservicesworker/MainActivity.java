package com.vcc.homeservicesworker;

import androidx.annotation.NonNull;

import io.flutter.embedding.android.FlutterActivity;

import com.google.firebase.messaging.RemoteMessage;
import com.ipccsupportsdk.IPCCSupportSDK;
import com.ipccsupportsdk.InitSDKExeption;
import com.ipccsupportsdk.configs.LiveConfig;
import com.ipccsupportsdk.model.CallStatus;
import com.google.gson.Gson;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

public class MainActivity extends FlutterActivity {
    private static final String CHANNEL = "com.viettel.vts.channel";

    private IPCCSupportSDK ipccSupportSDK;

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL).setMethodCallHandler(
                new MethodChannel.MethodCallHandler() {
                    @Override
                    public void onMethodCall(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result) {
                        if (methodCall.method.equals("startVideoCall")) {
                            startCall(methodCall, result);
                        } else if (methodCall.method.equals("updateToken")) {
                            updateToken(methodCall, result);
                        }
//                        else if (methodCall.method.equals("handleMessage")) {
//                            handleMessage(methodCall, result);
//                        }
                    }
                }
        );
    }

    private void startCall(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result) {
        String username = methodCall.argument("username");
        String calleeNumber = methodCall.argument("calleeNumber");
        String displayName = methodCall.argument("displayName");
        String orderCode = methodCall.argument("orderCode");
        String sender = methodCall.argument("sender");
        String orderDetail = methodCall.argument("orderDetail");
        String cod = methodCall.argument("cod");
        ipccSupportSDK = IPCCSupportSDK.instance;
        ipccSupportSDK.init(getFragmentManager(), this);

        ipccSupportSDK.setLiveConfig(new LiveConfig(new String[]{"https://mobilecall.viettelpost.vn/svc-pivot/"},
                "1fcb8551ccd9d37a9724d37e28219080",
                "mobilecall.viettelpost.vn:15060",
                "vcc.mycc.vn",
                calleeNumber,
                "",
                username,
                "Viettel@123",
                false));

        try {
            ipccSupportSDK.displayOrderInfo(displayName, orderCode, sender, orderDetail, cod);
            ipccSupportSDK.showCallFragment(getApplication(), this, username, new IPCCSupportSDK.VideoCallSDKEventListener() {
                @Override
                public void onSDKClosed(CallStatus callStatus) {
                    if (methodCall.method.equals("startVideoCall")) {
                        Gson gson = new Gson();
                        String json = gson.toJson(callStatus);
                        result.success(json);
                    } else {
                        result.notImplemented();
                    }
                }

                @Override
                public void onCallConnected(CallStatus callStatus) {
//                    if (methodCall.method.equals("startVideoCall")) {
//                        Gson gson = new Gson();
//                        String json = gson.toJson(callStatus);
//                        result.success(json);
//                    } else {
//                        result.notImplemented();
//                    }
                }
            });
        } catch (InitSDKExeption e) {
            throw new RuntimeException(e);
        }

    }

    private void updateToken(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result) {
        String username = methodCall.argument("username");
        String token = methodCall.argument("token");
        ipccSupportSDK = IPCCSupportSDK.instance;
        ipccSupportSDK.init(getFragmentManager(), this);

        ipccSupportSDK.setLiveConfig(new LiveConfig(new String[]{"https://mobilecall.viettelpost.vn/svc-pivot/"},
                "1fcb8551ccd9d37a9724d37e28219080",
                "mobilecall.viettelpost.vn:15060",
                "vcc.mycc.vn",
                "",
                "",
                username,
                "Viettel@123",
                false));
        ipccSupportSDK.onToken(token, username);
    }

//    private void handleMessage(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result) {
//        System.out.println("nampv handleMessage");
//
//        Bundle extra = convertMapToBundle(Objects.requireNonNull(methodCall.argument("message")));
//        String type = extra.getString("type", "").toLowerCase();
//        if (type.equals("voicecall")) {
//            System.out.println("nampv voicecall");
//            new Handler(Looper.getMainLooper()).post(() -> {
//                IPCCSupportSDK.instance.handleMessage(extra, new IPCCSupportSDK.VideoCallSDKEventListener() {
//                    @Override
//                    public void onSDKClosed(CallStatus callStatus) {
//                        // Xử lý khi SDK đóng
//                    }
//
//                    @Override
//                    public void onCallConnected(CallStatus callStatus) {
//                        // Xử lý khi cuộc gọi được kết nối
//                    }
//                });
//            });
//        }
//    }
//
//    public static Bundle convertMapToBundle(Map<String, Object> map) {
//        Bundle bundle = new Bundle();
//
//        for (Map.Entry<String, Object> entry : map.entrySet()) {
//            String key = entry.getKey();
//            Object value = entry.getValue();
//
//            if (value instanceof String) {
//                bundle.putString(key, (String) value);
//            } else if (value instanceof Integer) {
//                bundle.putInt(key, (Integer) value);
//            } else if (value instanceof Boolean) {
//                bundle.putBoolean(key, (Boolean) value);
//            } else if (value instanceof Serializable) {
//                bundle.putSerializable(key, (Serializable) value);
//            }
//        }
//
//        return bundle;
//    }
}
