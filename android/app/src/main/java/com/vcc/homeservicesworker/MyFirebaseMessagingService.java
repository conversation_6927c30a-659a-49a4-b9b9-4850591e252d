package com.vcc.homeservicesworker;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import com.android.volley.AuthFailureError;
import com.android.volley.NetworkResponse;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.VolleyLog;
import com.android.volley.toolbox.HttpHeaderParser;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.ipccsupportsdk.IPCCSupportSDK;
import com.ipccsupportsdk.configs.LiveConfig;
import com.ipccsupportsdk.model.CallStatus;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.util.Map;

public class MyFirebaseMessagingService extends FirebaseMessagingService {
    private RequestQueue mRequestQueue;
//    private String url = "http://*************:8088";
    private String url = "https://hsapi-app.congtrinhviettel.com.vn";

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        System.out.println("message native" + remoteMessage.getData());
        Bundle extra = remoteMessage.toIntent().getExtras();
        if (extra != null) {
            String type = extra.getString("type", "").toLowerCase();
            if (type.equals("voicecall")) {
                getData(remoteMessage);
            }
        }
    }

    @Override
    public void onNewToken(String token) {
        // Xử lý khi token FCM thay đổi
    }

    private void getData(RemoteMessage remoteMessage) {
        try {
            mRequestQueue = Volley.newRequestQueue(this);
            JSONObject jsonBody = new JSONObject();

            Map<String, String> data = remoteMessage.getData();

            if (data != null && !data.isEmpty()) {
                String phoneNumber = data.get("callerNumber");
                jsonBody.put("phoneNumber", phoneNumber);
            }
//            final String requestBody = jsonBody.toString();

            JsonObjectRequest jsonObjectRequest = new JsonObjectRequest(Request.Method.POST, url + "/app/getInfoOrderCus", jsonBody, new Response.Listener<JSONObject>() {
                @Override
                public void onResponse(JSONObject response) {
                    try {
                        JSONObject dataObject = response.getJSONObject("data");
                        Log.i("VOLLEY", "Error: " + dataObject.toString());
                        String code = dataObject.getString("code");
                        String id = dataObject.getString("id");
                        String contactName = dataObject.getString("contactName");
                        String address = dataObject.getString("address");
                        new Handler(Looper.getMainLooper()).postDelayed(() -> {
                            IPCCSupportSDK.instance.displayOrderInfo(contactName, code, contactName, address, "");

                            IPCCSupportSDK.instance.handleMessage(remoteMessage.toIntent().getExtras(), new IPCCSupportSDK.VideoCallSDKEventListener() {
                                @Override
                                public void onSDKClosed(CallStatus callStatus) {
                                    // Xử lý sự kiện khi SDK đóng
                                    updateData(callStatus, id);
                                }

                                @Override
                                public void onCallConnected(CallStatus callStatus) {
                                    // Xử lý sự kiện khi cuộc gọi được kết nối
                                }
                            });
                        }, 200);
                    } catch (JSONException e) {
                        Log.e("VOLLEY", "Error: " + e.toString());
                        e.printStackTrace();
                    }
                }
            }, new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {
                    Log.e("VOLLEY", "Error: " + error.toString());
                }
            });

            mRequestQueue.add(jsonObjectRequest);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void updateData(CallStatus callStatus, String orderId) {
        try {
            mRequestQueue = Volley.newRequestQueue(this);
            JSONObject jsonBody = new JSONObject();
            Log.i("VOLLEY", "callStatusZZZZZZZZZZZZZZZZZ: " + callStatus.callerNumber + "___________" + orderId + "___________" + callStatus.status + "___________" + callStatus.errorType);
            jsonBody.put("referenceId", orderId);
            jsonBody.put("status", callStatus.status);
            jsonBody.put("errorType", callStatus.errorType);
            jsonBody.put("duration", callStatus.duration);
            jsonBody.put("ringingTime", callStatus.ringingTime);
            if (callStatus.startTime != 0L) {
                jsonBody.put("startTime", callStatus.startTime);
            }
            if (callStatus.endTime != 0L) {
                jsonBody.put("endTime", callStatus.endTime);
            }
            jsonBody.put("callerNumber", callStatus.callerNumber);
            jsonBody.put("callID", callStatus.callID);

            Log.i("VOLLEY", "request: " + url + "/app/updateMobileCall\n" + jsonBody.toString());
            JsonObjectRequest jsonObjectRequest = new JsonObjectRequest(Request.Method.POST, url + "/app/updateMobileCall", jsonBody, new Response.Listener<JSONObject>() {
                @Override
                public void onResponse(JSONObject response) {
                    Log.i("VOLLEY", "info: " + response.toString());
                }
            }, new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {
                    Log.e("VOLLEY", "Error: " + error.toString());
                }
            });

            mRequestQueue.add(jsonObjectRequest);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}

