# Hướng dẫn sử dụng Feature Alert System (Cập nhật)

## 🎯 **Thay đổi quan trọng**

Hệ thống đã được điều chỉnh để phù hợp với yêu cầu mới:

1. **Áp dụng cho TẤT CẢ màn hình** (không chỉ một số route)
2. **Khi vào màn hình có alert → hiển thị popup**
3. **`isRequired`** quyết định popup có thể đóng được hay không

## 🔧 **Cách hoạt động mới**

### 1. API Response Format

```json
[
  {
    "feature": "HOME",
    "message": "Trang chủ có cập nhật mới",
    "isRequired": false  // Popup có thể đóng được
  },
  {
    "feature": "TASK_LIST", 
    "message": "Hệ thống đang bảo trì",
    "isRequired": true   // Popup bắt buộc phải xem
  },
  {
    "feature": "ALL",
    "message": "Ứng dụng có thông báo quan trọng",
    "isRequired": true   // Áp dụng cho tất cả màn hình
  }
]
```

### 2. Logic mới

- **Tất cả alerts** được lưu trữ (không phân biệt `isRequired`)
- **Khi vào màn hình** → `AlertScreenWrapper` tự động kiểm tra
- **Nếu có alert** → hiển thị popup
- **`isRequired: true`** → popup không thể đóng bằng back button hoặc tap outside
- **`isRequired: false`** → popup có thể đóng

### 3. Screen Mapping

```dart
// Mapping screen class name với feature name
'HomeScreen' → 'HOME'
'TaskListPage' → 'TASK_LIST'  
'ProfilePage' → 'PROFILE'
'CommunicationPage' → 'COMMUNICATION'
// ... và nhiều màn hình khác
```

## 🚀 **Cách sử dụng**

### 1. Tự động (Đã setup sẵn)

Hệ thống sẽ tự động hoạt động khi:
- API `getAlerts` trả về data
- User navigate đến màn hình có alert
- Popup sẽ hiển thị tự động

### 2. Manual Testing

```dart
// Test với dữ liệu mẫu
final alerts = [
  AlertModel(
    feature: 'HOME',
    message: 'Trang chủ có cập nhật mới',
    isRequired: false, // Có thể đóng
  ),
  AlertModel(
    feature: 'PROFILE',
    message: 'Vui lòng cập nhật thông tin',
    isRequired: true,  // Bắt buộc xem
  ),
];

await FeatureBlockingService.instance.updateAlerts(alerts);

// Navigate đến màn hình → popup sẽ hiển thị
Navigator.pushNamed(context, '/home');
Navigator.pushNamed(context, '/profile');
```

### 3. Kiểm tra alert cho màn hình

```dart
// Kiểm tra màn hình có alert không
bool hasAlert = FeatureBlockingService.instance.hasFeatureAlert('HOME');

// Lấy thông tin alert
AlertModel alert = FeatureBlockingService.instance.getAlertForFeature('HOME');
if (alert != null) {
  print('Message: ${alert.message}');
  print('Can dismiss: ${!alert.isRequired}');
}
```

## 📋 **Routes đã được setup**

Các routes sau đã được wrap với `AlertScreenWrapper`:

### Main Screens
- `/home` → `HomeScreen`
- `/task_list` → `TaskListPage`
- `/task_list_screen` → `TaskListScreen`
- `/page_task` → `PageViewTask`
- `/my_order` → `TaskListPage`

### Communication
- `/communication` → `CommunicationPage`
- `/chat` → `ChatScreen`
- `/chat_detail` → `ChatDetailScreen`
- `/messages` → `ChatScreenBottomBar`

### Profile
- `/profile` → `ProfilePage`
- `/profile_management` → `ProfileManagement`
- `/profile_update` → `ProfileUpdateScreen`
- `/profile_privacy` → `PrivacyScreen`
- ... và tất cả profile screens khác

### Auth Screens (Không có alert)
- `/login`, `/register`, `/verification` → Không wrap

## 🧪 **Testing**

### 1. Sử dụng Example Screen

```dart
// Thêm vào routes.dart
feature_blocking_example: (BuildContext context) => FeatureBlockingExample(),

// Navigate để test
Navigator.pushNamed(context, '/feature_blocking_example');
```

### 2. Test Scenarios

#### A. Test popup có thể đóng (`isRequired: false`)
```dart
final alerts = [
  AlertModel(
    feature: 'HOME',
    message: 'Có cập nhật mới cho trang chủ',
    isRequired: false,
  ),
];
await FeatureBlockingService.instance.updateAlerts(alerts);
Navigator.pushNamed(context, '/home'); // Popup có nút "Đóng"
```

#### B. Test popup bắt buộc (`isRequired: true`)
```dart
final alerts = [
  AlertModel(
    feature: 'PROFILE',
    message: 'Vui lòng cập nhật thông tin bắt buộc',
    isRequired: true,
  ),
];
await FeatureBlockingService.instance.updateAlerts(alerts);
Navigator.pushNamed(context, '/profile'); // Popup chỉ có nút "Đã hiểu"
```

#### C. Test alert cho tất cả màn hình
```dart
final alerts = [
  AlertModel(
    feature: 'ALL',
    message: 'Thông báo quan trọng cho tất cả người dùng',
    isRequired: true,
  ),
];
await FeatureBlockingService.instance.updateAlerts(alerts);
// Navigate đến bất kỳ màn hình nào → đều hiện popup
```

### 3. Debug Commands

```dart
// Xem tất cả alerts hiện tại
List<AlertModel> alerts = FeatureBlockingService.instance.alerts;
print('Total alerts: ${alerts.length}');

// Xem features có alerts
List<String> features = FeatureBlockingService.instance.blockedFeatures;
print('Features with alerts: $features');

// Clear tất cả alerts
await FeatureBlockingService.instance.clearCache();
```

## 🎨 **Customization**

### 1. Thêm màn hình mới

```dart
// Trong routes.dart
new_screen: _alertRoute('/new_screen', (context) => NewScreen(), 'NewScreen'),

// Trong ScreenFeatureMapping
static const Map<String, String> screenToFeature = {
  // ... existing mappings
  'NewScreen': 'NEW_FEATURE',
};
```

### 2. Tùy chỉnh popup

Popup được hiển thị bởi `AlertScreenWrapper` với:
- Icon thông báo
- Tiêu đề "Thông báo"
- Nội dung từ `alert.message`
- Nút "Đóng" (nếu `isRequired: false`)
- Nút "Đã hiểu" (luôn có)

### 3. Sử dụng Mixin (Alternative)

```dart
class MyScreen extends StatefulWidget {
  @override
  _MyScreenState createState() => _MyScreenState();
}

class _MyScreenState extends State<MyScreen> with AlertScreenMixin {
  @override
  String get screenName => 'MyScreen'; // Bắt buộc implement
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('My Screen')),
      body: Center(child: Text('Content')),
    );
  }
}
```

## 🔍 **Troubleshooting**

### 1. Popup không hiển thị
- Kiểm tra screen name có trong mapping không
- Kiểm tra API có trả về alert cho feature đó không
- Kiểm tra route có được wrap với `_alertRoute` không

### 2. Popup hiển thị nhiều lần
- `AlertScreenWrapper` có logic `_hasShownAlert` để tránh duplicate
- Nếu vẫn bị, kiểm tra navigation flow

### 3. isRequired không hoạt động
- Kiểm tra `WillPopScope` và `barrierDismissible` trong dialog
- Đảm bảo `alert.isRequired` có giá trị đúng

## 📈 **Performance**

- **Cache**: 1 giờ trong SharedPreferences
- **Memory**: Chỉ lưu alerts cần thiết
- **UI**: Popup chỉ hiển thị 1 lần per session per screen

## 🎉 **Ready to use!**

Hệ thống đã sẵn sàng hoạt động với logic mới:
1. API trả về alerts với `isRequired` flag
2. User navigate đến màn hình
3. Popup tự động hiển thị nếu có alert
4. User có thể đóng popup tùy theo `isRequired`
